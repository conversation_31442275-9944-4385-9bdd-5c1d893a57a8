<?php $__env->startSection('title', 'Purchases - Manufacturing App'); ?>

<!-- Vendor Styles -->
<?php $__env->startSection('vendor-style'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Vend<PERSON>ts -->
<?php $__env->startSection('vendor-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Page Scripts -->
<?php $__env->startSection('page-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/js/purchases-management.js',
  'resources/js/purchase-receiving.js'
]); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Total Purchases</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2"><?php echo e($totalPurchases); ?></h4>
              <p class="text-success mb-1">(100%)</p>
            </div>
            <small class="mb-0">All purchase orders</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-shopping-cart-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Pending</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1"><?php echo e($pendingPurchases); ?></h4>
              <p class="text-warning mb-1">(<?php echo e($totalPurchases > 0 ? round(($pendingPurchases/$totalPurchases)*100) : 0); ?>%)</p>
            </div>
            <small class="mb-0">Awaiting approval</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-time-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Ordered</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1"><?php echo e($orderedPurchases); ?></h4>
              <p class="text-info mb-1">(<?php echo e($totalPurchases > 0 ? round(($orderedPurchases/$totalPurchases)*100) : 0); ?>%)</p>
            </div>
            <small class="mb-0">With suppliers</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-truck-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Overdue</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1"><?php echo e($overduePurchases); ?></h4>
              <p class="text-danger mb-1">(<?php echo e($totalPurchases > 0 ? round(($overduePurchases/$totalPurchases)*100) : 0); ?>%)</p>
            </div>
            <small class="mb-0">Past delivery date</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-danger rounded-3">
              <div class="ri-alarm-warning-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Purchases List Table -->
<div class="card">
  <div class="card-header pb-0">
    <h5 class="card-title mb-0">Purchases Management</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-purchases table">
      <thead>
        <tr>
          <th></th>
          <th>Purchase #</th>
          <th>Supplier</th>
          <th>Purchase Date</th>
          <th>Expected Delivery</th>
          <th>Total Amount</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>

  <!-- Modal to add new purchase -->
  <div class="modal fade" id="addPurchaseModal" tabindex="-1" aria-labelledby="addPurchaseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 id="addPurchaseModalLabel" class="modal-title">Add Purchase Order</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form class="add-new-purchase" id="addNewPurchaseForm">
            <input type="hidden" name="id" id="purchase_id">

            <div class="row">
              <!-- Left Column - Supplier & Purchase Info -->
              <div class="col-md-6">
                <!-- Supplier Information -->
                <div class="mb-4">
                  <h6 class="fw-medium mb-3">Supplier Information</h6>

          <div class="form-floating form-floating-outline mb-3">
            <input type="text" class="form-control" id="add-supplier-name" placeholder="Supplier Name" name="supplier_name" />
            <label for="add-supplier-name">Supplier Name</label>
          </div>

          <div class="form-floating form-floating-outline mb-3">
            <input type="text" class="form-control" id="add-supplier-contact" placeholder="Contact Number" name="supplier_contact" />
            <label for="add-supplier-contact">Contact Number</label>
          </div>

          <div class="form-floating form-floating-outline mb-3">
            <input type="email" class="form-control" id="add-supplier-email" placeholder="Email Address" name="supplier_email" />
            <label for="add-supplier-email">Email Address</label>
          </div>

                  <div class="form-floating form-floating-outline mb-3">
                    <textarea class="form-control" id="add-supplier-address" placeholder="Supplier Address" name="supplier_address" rows="3"></textarea>
                    <label for="add-supplier-address">Address</label>
                  </div>
                </div>

                <!-- Purchase Details -->
                <div class="mb-4">
                  <h6 class="fw-medium mb-3">Purchase Details</h6>

                  <div class="form-floating form-floating-outline mb-3">
                    <input type="text" class="form-control" id="add-purchase-date" placeholder="YYYY-MM-DD" name="purchase_date" />
                    <label for="add-purchase-date">Purchase Date</label>
                  </div>

                  <div class="form-floating form-floating-outline mb-3">
                    <input type="text" class="form-control" id="add-expected-delivery" placeholder="YYYY-MM-DD" name="expected_delivery_date" />
                    <label for="add-expected-delivery">Expected Delivery Date</label>
                  </div>
                </div>
              </div>

              <!-- Right Column - Fees & Totals -->
              <div class="col-md-6">
                <!-- Fees -->
                <div class="mb-4">
                  <h6 class="fw-medium mb-3">Additional Fees</h6>

                  <div class="row g-3">
                    <div class="col-4">
                      <div class="form-floating form-floating-outline">
                        <input type="number" step="0.01" class="form-control fee-input" id="add-delivery-fee" placeholder="0.00" name="delivery_fee" />
                        <label for="add-delivery-fee">Delivery</label>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="form-floating form-floating-outline">
                        <input type="number" step="0.01" class="form-control fee-input" id="add-handling-fee" placeholder="0.00" name="handling_fee" />
                        <label for="add-handling-fee">Handling</label>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="form-floating form-floating-outline">
                        <input type="number" step="0.01" class="form-control fee-input" id="add-other-fees" placeholder="0.00" name="other_fees" />
                        <label for="add-other-fees">Other</label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Totals -->
                <div class="mb-4">
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span class="fw-bold" id="purchase-subtotal">0.00</span>
                      </div>
                      <div class="d-flex justify-content-between mb-2">
                        <span>Total Fees:</span>
                        <span class="fw-bold" id="purchase-fees">0.00</span>
                      </div>
                      <hr>
                      <div class="d-flex justify-content-between">
                        <span class="h6">Total Amount:</span>
                        <span class="h5 text-primary fw-bold" id="purchase-total">0.00</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Notes -->
                <div class="mb-4">
                  <div class="form-floating form-floating-outline">
                    <textarea class="form-control" id="add-purchase-notes" placeholder="Purchase notes..." name="notes" rows="4"></textarea>
                    <label for="add-purchase-notes">Notes</label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Purchase Items -->
            <div class="mb-4">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="fw-medium mb-0">Purchase Items</h6>
                <button type="button" class="btn btn-sm btn-primary" id="add-purchase-item">
                  <i class="ri-add-line me-1"></i>Add Item
                </button>
              </div>

              <div id="purchase-items-container">
                <!-- Purchase items will be added here -->
              </div>
            </div>

        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary data-submit">Submit Purchase</button>
        </div>
      </form>
    </div>
  </div>
</div>

  <!-- Purchase Details Modal -->
  <div class="modal fade" id="purchaseDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="purchaseDetailsModalLabel">Purchase Details</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="purchase-details-content">
            <!-- Purchase details will be loaded here -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
          <div id="purchase-actions">
            <!-- Action buttons will be added here based on purchase status -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Receive Items Modal -->
  <div class="modal fade" id="receiveItemsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="receiveItemsModalLabel">Receive Purchase Items</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="receiveItemsForm">
            <input type="hidden" id="receive-purchase-id">

            <div class="mb-4">
              <h6 class="fw-medium">Purchase: <span id="receive-purchase-number" class="text-primary"></span></h6>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <input type="text" class="form-control" id="actual-delivery-date" name="actual_delivery_date">
              <label for="actual-delivery-date">Actual Delivery Date</label>
            </div>

            <div id="receive-items-container">
              <!-- Receive items will be dynamically added here -->
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="save-received-items">Receive Items</button>
        </div>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/layoutMaster', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\recipe\resources\views/pages/purchases/index.blade.php ENDPATH**/ ?>