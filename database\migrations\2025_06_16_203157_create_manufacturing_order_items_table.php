<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('manufacturing_order_id')->constrained('manufacturing_orders')->onDelete('cascade');
            $table->foreignId('finished_product_id')->constrained('finished_products')->onDelete('restrict');
            $table->decimal('planned_quantity', 10, 3);
            $table->decimal('produced_quantity', 10, 3)->default(0);
            $table->decimal('estimated_cost', 12, 2)->nullable(); // Calculated based on recipe
            $table->decimal('actual_cost', 12, 2)->nullable(); // Actual cost after completion
            $table->integer('estimated_time_minutes')->nullable(); // Estimated production time for this item
            $table->integer('actual_time_minutes')->nullable(); // Actual production time for this item
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('manufacturing_order_id', 'mfg_order_items_order_idx');
            $table->index('finished_product_id', 'mfg_order_items_product_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_order_items');
    }
};
