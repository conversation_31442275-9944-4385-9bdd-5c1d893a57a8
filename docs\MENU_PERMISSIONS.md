# Menu Permissions System

This document explains the new Blade-based menu system with <PERSON><PERSON>vel Permission integration.

## Overview

The vertical menu has been converted from JSON to a Blade-based system that dynamically filters menu items based on user permissions. This allows for role-based access control at the menu level.

## Key Features

- **Permission-Based Filtering**: Menu items are shown/hidden based on user permissions
- **Dynamic Menu Generation**: Menu is generated dynamically for each user
- **Role-Based Access**: Different roles see different menu items
- **Backward Compatibility**: Maintains the same structure as the original JSON menu

## File Structure

```
app/Providers/MenuServiceProvider.php          # Menu data provider with permission filtering
resources/views/layouts/sections/menu/verticalMenuData.blade.php  # Blade menu data (optional)
database/seeders/MenuPermissionsSeeder.php     # Permissions and roles seeder
app/Console/Commands/TestMenuPermissions.php   # Testing command for menu permissions
```

## Menu Structure

### Dashboard
- **Permission**: None (always visible for authenticated users)
- **Route**: `/`

### Manufacturing
- **Units**: `view-units` permission
- **Raw Materials**: `view-raw-materials` permission  
- **Finished Products**: `view-finished-products` permission
- **Manufacturing Orders**: `view-manufacturing-orders` permission
- **Purchases**: `view-purchases` permission

### Point of Sale
- **Permission**: `access-pos`
- **Route**: `/pos`

### Administration Section
- **Users**: `view-users` permission
- **Roles & Permissions**: `view-roles` permission

## Roles and Permissions

### Super Admin
- **All permissions** - Can access everything

### Admin  
- Most permissions except super admin functions
- Cannot delete users or manage roles

### Production Manager
- Manufacturing and inventory management
- Units, Raw Materials, Finished Products, Manufacturing Orders, Purchases

### Inventory Manager
- Inventory and purchasing focus
- Raw Materials, Finished Products, Purchases

### Sales Manager
- Sales and POS access
- Finished Products (view), POS access

### Production Operator
- Limited manufacturing access
- View most items, edit Manufacturing Orders

### Cashier
- POS only access
- Finished Products (view), POS access

### Viewer
- Read-only access to all manufacturing data

## How It Works

1. **MenuServiceProvider**: Filters menu items based on user permissions
2. **Permission Check**: Uses `auth()->user()->can($permission)` to check access
3. **Dynamic Filtering**: Menu items without required permissions are hidden
4. **Submenu Logic**: Parent menus are only shown if they have visible child items

## Testing Menu Permissions

Use the built-in command to test how menus appear for different roles:

```bash
# List all available roles
php artisan menu:test-permissions

# Test specific role
php artisan menu:test-permissions "Cashier"
php artisan menu:test-permissions "Production Manager"
php artisan menu:test-permissions "Super Admin"
```

## Adding New Menu Items

To add new menu items with permissions:

1. **Add Permission**: Update `MenuPermissionsSeeder.php` with new permissions
2. **Update Menu**: Add menu item to `getMenuItems()` method in `MenuServiceProvider.php`
3. **Assign to Roles**: Update role permissions in the seeder
4. **Run Seeder**: `php artisan db:seed --class=MenuPermissionsSeeder`

Example:
```php
[
    'url' => 'reports',
    'name' => 'Reports',
    'slug' => 'reports.index',
    'permission' => 'view-reports'
]
```

## Permission Syntax

- **Single Permission**: `'view-users'`
- **Multiple Permissions (OR)**: `'view-users|view-roles'`
- **No Permission Required**: `null`

## Migration from JSON

The original JSON menu is backed up as `verticalMenu.json.backup`. The new system:

- ✅ Maintains same visual structure
- ✅ Supports all existing menu features
- ✅ Adds permission-based filtering
- ✅ Removes unused template menu items
- ✅ Focuses only on actual application features

## Benefits

1. **Security**: Users only see what they can access
2. **Clean Interface**: No clutter from inaccessible features  
3. **Role-Based UX**: Different user types get tailored experiences
4. **Maintainable**: Easy to add/remove menu items with permissions
5. **Testable**: Built-in testing commands for verification

## Troubleshooting

### Menu Not Updating
```bash
php artisan cache:clear
php artisan view:clear
php artisan config:clear
```

### Permission Issues
```bash
# Check user permissions
php artisan tinker
>>> auth()->user()->getAllPermissions()->pluck('name')

# Check user roles  
>>> auth()->user()->getRoleNames()
```

### Testing Different Roles
```bash
# Assign role to current user
php artisan tinker
>>> auth()->user()->assignRole('Role Name')
```
