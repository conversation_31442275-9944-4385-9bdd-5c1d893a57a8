/* Image Upload and Display Styles */

/* Form Image Preview */
.image-preview-container {
  position: relative;
}

.image-preview-container img {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-preview-container img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* POS Product Images */
.product-image-container {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image-container {
  border-color: #696cff;
  box-shadow: 0 4px 12px rgba(105, 108, 255, 0.15);
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

/* DataTable Image Thumbnails */
.table-image-thumbnail {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 6px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.table-image-thumbnail:hover {
  transform: scale(2.5);
  z-index: 1000;
  position: relative;
  border-color: #696cff;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* File Input Styling */
.form-control[type="file"] {
  padding: 0.5rem;
  border: 2px dashed #d9dee3;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.form-control[type="file"]:hover {
  border-color: #696cff;
  background: #f5f5ff;
}

.form-control[type="file"]:focus {
  border-color: #696cff;
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
  background: #fff;
}

/* Image Upload Drop Zone */
.image-drop-zone {
  border: 2px dashed #d9dee3;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.image-drop-zone:hover {
  border-color: #696cff;
  background: #f5f5ff;
}

.image-drop-zone.dragover {
  border-color: #696cff;
  background: #f5f5ff;
  transform: scale(1.02);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .product-image-container {
    height: 100px;
  }
  
  .table-image-thumbnail:hover {
    transform: scale(2);
  }
}

@media (max-width: 576px) {
  .product-image-container {
    height: 80px;
  }
  
  .image-preview-container img {
    max-width: 80px;
    max-height: 80px;
  }
}

/* Loading states */
.image-loading {
  position: relative;
  overflow: hidden;
}

.image-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Error states */
.image-error {
  border-color: #ff4757 !important;
  background: #fff5f5;
}

.image-error::before {
  content: '⚠️';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  z-index: 1;
}

/* Success states */
.image-success {
  border-color: #2ed573 !important;
  background: #f0fff4;
}

/* Image gallery styles for product details */
.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.image-gallery-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.image-gallery-item:hover {
  border-color: #696cff;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.image-gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Zoom overlay for images */
.image-zoom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.image-zoom-overlay.active {
  opacity: 1;
  visibility: visible;
}

.image-zoom-overlay img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Close button for zoom overlay */
.image-zoom-close {
  position: absolute;
  top: 2rem;
  right: 2rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-zoom-close:hover {
  background: #fff;
  transform: scale(1.1);
}
