<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\LocaleMiddleware;
use Illuminate\Support\Facades\Blade;

return Application::configure(basePath: dirname(__DIR__))
  ->withRouting(
    web: __DIR__ . '/../routes/web.php',
    commands: __DIR__ . '/../routes/console.php',
    health: '/up',
  )
  ->withMiddleware(function (Middleware $middleware) {
    $middleware->web(LocaleMiddleware::class);
  })
  ->withExceptions(function (Exceptions $exceptions) {
    //
  })
  ->booted(function () {
    // Register custom Blade directive for currency formatting
    Blade::directive('currency', function ($expression) {
      return "<?php echo Helper::formatCurrency($expression); ?>";
    });
  })
  ->create();
