<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\RawMaterial;
use App\Models\RawMaterialMovement;
use App\Models\Unit;
use App\Services\ImageUploadService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class RawMaterialController extends Controller
{
    protected $imageUploadService;

    public function __construct(ImageUploadService $imageUploadService)
    {
        $this->imageUploadService = $imageUploadService;
    }

    /**
     * Display the raw materials management page.
     */
    public function index()
    {
        $rawMaterials = RawMaterial::with('unit')->get();
        $totalRawMaterials = $rawMaterials->count();
        $activeRawMaterials = RawMaterial::active()->count();
        $lowStockItems = RawMaterial::lowStock()->count();
        $totalValue = $rawMaterials->sum('stock_value');

        return view('pages.raw-materials.index', [
            'totalRawMaterials' => $totalRawMaterials,
            'activeRawMaterials' => $activeRawMaterials,
            'lowStockItems' => $lowStockItems,
            'totalValue' => $totalValue,
        ]);
    }

    /**
     * Get raw materials data for DataTable.
     */
    public function getData(Request $request): JsonResponse
    {
        $columns = [
            1 => 'id',
            2 => 'name',
            3 => 'current_stock',
            4 => 'minimum_stock',
            5 => 'unit_price',
            6 => 'supplier',
            7 => 'is_active',
        ];

        $totalData = RawMaterial::count();
        $totalFiltered = $totalData;

        $limit = $request->input('length');
        $start = $request->input('start');
        $order = $columns[$request->input('order.0.column')] ?? 'id';
        $dir = $request->input('order.0.dir');

        $query = RawMaterial::with('unit');

        if (!empty($request->input('search.value'))) {
            $search = $request->input('search.value');

            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%")
                  ->orWhere('supplier', 'LIKE', "%{$search}%")
                  ->orWhereHas('unit', function($unitQuery) use ($search) {
                      $unitQuery->where('name', 'LIKE', "%{$search}%")
                               ->orWhere('symbol', 'LIKE', "%{$search}%");
                  });
            });

            $totalFiltered = $query->count();
        }

        $rawMaterials = $query->offset($start)
            ->limit($limit)
            ->orderBy($order, $dir)
            ->get();

        $data = [];

        if (!empty($rawMaterials)) {
            $ids = $start;

            foreach ($rawMaterials as $material) {
                $nestedData['id'] = $material->id;
                $nestedData['fake_id'] = ++$ids;
                $nestedData['name'] = $material->name;
                $nestedData['description'] = $material->description;
                $nestedData['current_stock'] = $material->current_stock;
                $nestedData['minimum_stock'] = $material->minimum_stock;
                $nestedData['unit_price'] = $material->unit_price;
                $nestedData['supplier'] = $material->supplier;
                $nestedData['is_active'] = $material->is_active;
                $nestedData['unit'] = $material->unit;
                $nestedData['stock_status'] = $material->stock_status;
                $nestedData['stock_status_color'] = $material->stock_status_color;
                $nestedData['stock_value'] = $material->stock_value;
                $nestedData['image_path'] = $material->image_path;
                $nestedData['image_url'] = $material->image_url;
                $nestedData['image_alt'] = $material->image_alt_text;

                $data[] = $nestedData;
            }
        }

        return response()->json([
            'draw' => intval($request->input('draw')),
            'recordsTotal' => intval($totalData),
            'recordsFiltered' => intval($totalFiltered),
            'code' => 200,
            'data' => $data,
        ]);
    }

    /**
     * Store a newly created raw material or update existing one.
     */
    public function store(Request $request): JsonResponse
    {
        // Convert checkbox value to boolean
        $requestData = $request->all();
        if (isset($requestData['is_active'])) {
            $requestData['is_active'] = in_array($requestData['is_active'], ['on', '1', 'true', true, 1], true);
        } else {
            $requestData['is_active'] = false;
        }

        $validator = Validator::make($requestData, [
            'name' => 'required|string|max:100|unique:raw_materials,name,' . $request->id,
            'description' => 'nullable|string',
            'unit_id' => 'required|exists:units,id',
            'current_stock' => 'required|numeric|min:0|regex:/^\d+(\.\d{1,3})?$/',
            'minimum_stock' => 'required|numeric|min:0|regex:/^\d+(\.\d{1,3})?$/',
            'unit_price' => 'nullable|numeric|min:0|regex:/^\d+(\.\d{1,2})?$/',
            'supplier' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'image_alt' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $materialId = $request->id;

        // Handle image upload
        $imagePath = null;
        $imageAlt = $requestData['image_alt'] ?? null;

        if ($request->hasFile('image')) {
            try {
                $uploadResult = $this->imageUploadService->uploadImage(
                    $request->file('image'),
                    'raw-materials',
                    [
                        'max_width' => 800,
                        'max_height' => 800,
                        'create_thumbnail' => true,
                        'thumbnail_width' => 200,
                        'thumbnail_height' => 200,
                    ]
                );
                $imagePath = $uploadResult['path'];
            } catch (\Exception $e) {
                return response()->json([
                    'message' => 'Image upload failed: ' . $e->getMessage()
                ], 422);
            }
        }

        if ($materialId) {
            // Update existing raw material
            $material = RawMaterial::findOrFail($materialId);

            // Delete old image if new one is uploaded
            if ($imagePath && $material->image_path) {
                $this->imageUploadService->deleteImage($material->image_path);
            }

            $updateData = [
                'name' => $requestData['name'],
                'description' => $requestData['description'],
                'unit_id' => $requestData['unit_id'],
                'current_stock' => $requestData['current_stock'],
                'minimum_stock' => $requestData['minimum_stock'],
                'unit_price' => $requestData['unit_price'],
                'supplier' => $requestData['supplier'],
                'is_active' => $requestData['is_active'],
            ];

            // Only update image fields if new image is uploaded
            if ($imagePath) {
                $updateData['image_path'] = $imagePath;
                $updateData['image_alt'] = $imageAlt;
            } elseif ($imageAlt !== null) {
                // Update alt text even if no new image
                $updateData['image_alt'] = $imageAlt;
            }

            $material->update($updateData);

            return response()->json(['message' => 'Raw material updated successfully']);
        } else {
            // Create new raw material
            RawMaterial::create([
                'name' => $requestData['name'],
                'description' => $requestData['description'],
                'unit_id' => $requestData['unit_id'],
                'current_stock' => $requestData['current_stock'],
                'minimum_stock' => $requestData['minimum_stock'],
                'unit_price' => $requestData['unit_price'],
                'supplier' => $requestData['supplier'],
                'image_path' => $imagePath,
                'image_alt' => $imageAlt,
                'is_active' => $requestData['is_active'],
            ]);

            return response()->json(['message' => 'Raw material created successfully']);
        }
    }

    /**
     * Show the form for editing the specified raw material.
     */
    public function edit($id): JsonResponse
    {
        $material = RawMaterial::with('unit')->findOrFail($id);
        return response()->json($material);
    }

    /**
     * Remove the specified raw material from storage.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $material = RawMaterial::findOrFail($id);

            // Check if raw material is being used in recipes or has movements
            if ($material->recipes()->count() > 0) {
                return response()->json([
                    'message' => 'Cannot delete raw material. It is being used in product recipes.'
                ], 422);
            }

            if ($material->movements()->count() > 0) {
                return response()->json([
                    'message' => 'Cannot delete raw material. It has stock movement history.'
                ], 422);
            }

            // Delete associated image
            if ($material->image_path) {
                $this->imageUploadService->deleteImage($material->image_path);
            }

            $material->delete();
            return response()->json(['message' => 'Raw material deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error deleting raw material'], 500);
        }
    }

    /**
     * Get active raw materials for dropdowns.
     */
    public function getActiveRawMaterials(): JsonResponse
    {
        $materials = RawMaterial::with('unit.category')
            ->active()
            ->orderBy('name')
            ->get(['id', 'name', 'unit_id', 'current_stock', 'unit_price']);

        return response()->json($materials);
    }

    /**
     * Get low stock raw materials.
     */
    public function getLowStockMaterials(): JsonResponse
    {
        $materials = RawMaterial::with('unit')
            ->lowStock()
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json($materials);
    }

    /**
     * Get movements for a specific raw material.
     */
    public function getMovements($id): JsonResponse
    {
        $material = RawMaterial::with('unit')->findOrFail($id);

        $movements = RawMaterialMovement::with(['user', 'rawMaterial.unit'])
            ->where('raw_material_id', $id)
            ->orderBy('movement_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($movement) {
                return [
                    'id' => $movement->id,
                    'movement_date' => $movement->movement_date->format('Y-m-d H:i'),
                    'movement_type' => $movement->movement_type,
                    'movement_type_label' => $movement->getMovementTypeLabel(),
                    'quantity' => $movement->quantity,
                    'absolute_quantity' => $movement->absolute_quantity,
                    'unit_price' => $movement->unit_price,
                    'total_value' => $movement->total_value,
                    'stock_before' => $movement->stock_before,
                    'stock_after' => $movement->stock_after,
                    'reason' => $movement->reason,
                    'notes' => $movement->notes,
                    'document_reference' => $movement->document_reference,
                    'user_name' => $movement->user ? $movement->user->name : 'System',
                    'is_positive' => $movement->isPositiveMovement(),
                ];
            });

        return response()->json([
            'material' => [
                'id' => $material->id,
                'name' => $material->name,
                'unit' => $material->unit->symbol,
                'current_stock' => $material->current_stock,
            ],
            'movements' => $movements
        ]);
    }

    /**
     * Add stock to a raw material.
     */
    public function addStock(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|numeric|min:0.001|regex:/^\d+(\.\d{1,3})?$/',
            'unit_price' => 'nullable|numeric|min:0|regex:/^\d+(\.\d{1,2})?$/',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string|max:500',
            'document_reference' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $material = RawMaterial::findOrFail($id);
        $stockBefore = $material->current_stock;
        $quantity = $request->quantity;
        $stockAfter = $stockBefore + $quantity;

        // Create movement record
        RawMaterialMovement::create([
            'raw_material_id' => $material->id,
            'movement_type' => 'manual_adjustment',
            'quantity' => $quantity,
            'unit_price' => $request->unit_price,
            'total_value' => $request->unit_price ? ($quantity * $request->unit_price) : null,
            'document_reference' => $request->document_reference,
            'reason' => $request->reason,
            'notes' => $request->notes,
            'stock_before' => $stockBefore,
            'stock_after' => $stockAfter,
            'user_id' => Auth::id(),
            'movement_date' => now(),
        ]);

        // Update material stock
        $material->update([
            'current_stock' => $stockAfter,
            'unit_price' => $request->unit_price ?: $material->unit_price,
        ]);

        return response()->json([
            'message' => 'Stock added successfully',
            'new_stock' => $stockAfter
        ]);
    }
}
