<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchases', function (Blueprint $table) {
            $table->id();
            $table->string('purchase_number')->unique();
            $table->string('supplier_name');
            $table->string('supplier_contact', 50)->nullable();
            $table->string('supplier_email')->nullable();
            $table->text('supplier_address')->nullable();
            $table->date('purchase_date');
            $table->date('expected_delivery_date')->nullable();
            $table->date('actual_delivery_date')->nullable();
            $table->decimal('subtotal', 12, 2);
            $table->decimal('delivery_fee', 10, 2)->default(0);
            $table->decimal('handling_fee', 10, 2)->default(0);
            $table->decimal('other_fees', 10, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->enum('status', ['pending', 'ordered', 'shipped', 'received', 'completed', 'cancelled'])->default('pending');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('received_by')->nullable();
            $table->timestamps();

            // Foreign key constraints with custom short names
            $table->foreign('created_by', 'purchases_created_by_fk')->references('id')->on('users')->onDelete('restrict');
            $table->foreign('received_by', 'purchases_received_by_fk')->references('id')->on('users')->onDelete('restrict');

            // Indexes for performance with custom short names
            $table->index('purchase_number', 'purchases_number_idx');
            $table->index('supplier_name', 'purchases_supplier_idx');
            $table->index('purchase_date', 'purchases_date_idx');
            $table->index('status', 'purchases_status_idx');
            $table->index('created_by', 'purchases_created_by_idx');
            $table->index(['purchase_date', 'status'], 'purchases_date_status_idx');
            $table->index(['supplier_name', 'status'], 'purchases_supplier_status_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchases');
    }
};
