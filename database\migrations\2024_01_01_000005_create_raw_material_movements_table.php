<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('raw_material_movements', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('raw_material_id');
            $table->enum('movement_type', ['inbound', 'outbound', 'adjustment']);
            $table->decimal('quantity', 10, 3); // Positive for inbound, negative for outbound
            $table->decimal('unit_price', 10, 2)->nullable(); // For inbound movements
            $table->decimal('total_value', 12, 2)->nullable(); // Calculated: quantity * unit_price
            $table->string('document_reference')->nullable();
            $table->text('reason')->nullable();
            $table->text('notes')->nullable();
            $table->decimal('stock_before', 10, 3); // Stock level before this movement
            $table->decimal('stock_after', 10, 3); // Stock level after this movement
            $table->unsignedBigInteger('user_id'); // Who made the movement
            $table->timestamp('movement_date')->useCurrent();
            $table->timestamps();

            // Foreign key constraints with custom short names
            $table->foreign('raw_material_id', 'rm_movements_material_fk')->references('id')->on('raw_materials')->onDelete('cascade');
            $table->foreign('user_id', 'rm_movements_user_fk')->references('id')->on('users')->onDelete('restrict');

            // Indexes for performance with custom short names
            $table->index(['raw_material_id', 'movement_date'], 'rm_movements_material_date_idx');
            $table->index('movement_type', 'rm_movements_type_idx');
            $table->index('user_id', 'rm_movements_user_idx');
            $table->index('movement_date', 'rm_movements_date_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('raw_material_movements');
    }
};
