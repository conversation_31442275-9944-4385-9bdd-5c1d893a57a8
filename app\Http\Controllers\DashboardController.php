<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Sale;
use App\Models\Purchase;
use App\Models\ManufacturingOrder;
use App\Models\RawMaterial;
use App\Models\FinishedProduct;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
  public function index()
  {
    // Get dashboard data
    $dashboardData = $this->getDashboardData();

    return view('content.dashboard.dashboards-analytics', $dashboardData);
  }

  private function getDashboardData()
  {
    $today = Carbon::today();
    $thisWeek = Carbon::now()->startOfWeek();
    $thisMonth = Carbon::now()->startOfMonth();
    $lastMonth = Carbon::now()->subMonth()->startOfMonth();

    // Sales Analytics
    $todaySales = Sale::whereDate('sale_date', $today)->sum('total_amount');
    $thisWeekSales = Sale::where('sale_date', '>=', $thisWeek)->sum('total_amount');
    $thisMonthSales = Sale::where('sale_date', '>=', $thisMonth)->sum('total_amount');
    $lastMonthSales = Sale::whereBetween('sale_date', [$lastMonth, $thisMonth])->sum('total_amount');

    $salesGrowth = $lastMonthSales > 0 ? (($thisMonthSales - $lastMonthSales) / $lastMonthSales) * 100 : 0;

    // Purchase Analytics
    $thisMonthPurchases = Purchase::where('purchase_date', '>=', $thisMonth)->sum('total_amount');
    $pendingPurchases = Purchase::where('status', 'pending')->count();
    $overduePurchases = Purchase::where('status', '!=', 'completed')
        ->where('expected_delivery_date', '<', $today)->count();

    // Manufacturing Analytics
    $activeOrders = ManufacturingOrder::whereIn('status', ['planned', 'in_progress'])->count();
    $completedOrdersThisMonth = ManufacturingOrder::where('status', 'completed')
        ->where('created_at', '>=', $thisMonth)->count();
    $overdueOrders = ManufacturingOrder::where('status', '!=', 'completed')
        ->where('planned_date', '<', $today)->count();

    // Inventory Analytics
    $lowStockMaterials = RawMaterial::whereColumn('current_stock', '<=', 'minimum_stock')->count();
    $lowStockProducts = FinishedProduct::whereColumn('current_stock', '<=', 'minimum_stock')->count();
    $totalMaterialsValue = RawMaterial::selectRaw('SUM(current_stock * unit_price) as total')->first()->total ?? 0;
    $totalProductsValue = FinishedProduct::selectRaw('SUM(current_stock * selling_price) as total')->first()->total ?? 0;

    // Recent Activities
    $recentSales = Sale::with(['items.finishedProduct', 'cashier'])
        ->orderBy('sale_date', 'desc')->take(5)->get();
    $recentPurchases = Purchase::with('createdBy')
        ->orderBy('created_at', 'desc')->take(5)->get();
    $recentOrders = ManufacturingOrder::with(['finishedProduct', 'createdBy'])
        ->orderBy('created_at', 'desc')->take(5)->get();

    // Chart Data
    $salesChartData = $this->getSalesChartData();
    $productionChartData = $this->getProductionChartData();
    $inventoryChartData = $this->getInventoryChartData();

    // Top Products
    $topSellingProducts = $this->getTopSellingProducts();
    $topMaterials = $this->getTopMaterials();

    return [
        // KPIs
        'todaySales' => $todaySales,
        'thisWeekSales' => $thisWeekSales,
        'thisMonthSales' => $thisMonthSales,
        'salesGrowth' => $salesGrowth,
        'thisMonthPurchases' => $thisMonthPurchases,
        'pendingPurchases' => $pendingPurchases,
        'overduePurchases' => $overduePurchases,
        'activeOrders' => $activeOrders,
        'completedOrdersThisMonth' => $completedOrdersThisMonth,
        'overdueOrders' => $overdueOrders,
        'lowStockMaterials' => $lowStockMaterials,
        'lowStockProducts' => $lowStockProducts,
        'totalMaterialsValue' => $totalMaterialsValue,
        'totalProductsValue' => $totalProductsValue,

        // Recent Activities
        'recentSales' => $recentSales,
        'recentPurchases' => $recentPurchases,
        'recentOrders' => $recentOrders,

        // Chart Data
        'salesChartData' => $salesChartData,
        'productionChartData' => $productionChartData,
        'inventoryChartData' => $inventoryChartData,

        // Top Items
        'topSellingProducts' => $topSellingProducts,
        'topMaterials' => $topMaterials,
    ];
  }

  private function getSalesChartData()
  {
    $last7Days = collect();
    for ($i = 6; $i >= 0; $i--) {
        $date = Carbon::now()->subDays($i);
        $sales = Sale::whereDate('sale_date', $date)->sum('total_amount');
        $last7Days->push([
            'date' => $date->format('M d'),
            'sales' => $sales,
            'orders' => Sale::whereDate('sale_date', $date)->count()
        ]);
    }
    return $last7Days;
  }

  private function getProductionChartData()
  {
    $last7Days = collect();
    for ($i = 6; $i >= 0; $i--) {
        $date = Carbon::now()->subDays($i);
        $completed = ManufacturingOrder::where('status', 'completed')
            ->whereDate('updated_at', $date)->count();
        $planned = ManufacturingOrder::where('status', 'planned')
            ->whereDate('planned_date', $date)->count();
        $last7Days->push([
            'date' => $date->format('M d'),
            'completed' => $completed,
            'planned' => $planned
        ]);
    }
    return $last7Days;
  }

  private function getInventoryChartData()
  {
    $materials = RawMaterial::select('name', 'current_stock', 'minimum_stock')
        ->where('is_active', true)
        ->orderBy('current_stock', 'asc')
        ->take(10)
        ->get();

    return $materials->map(function($material) {
        return [
            'name' => $material->name,
            'current' => $material->current_stock,
            'minimum' => $material->minimum_stock,
            'status' => $material->current_stock <= $material->minimum_stock ? 'low' : 'good'
        ];
    });
  }

  private function getTopSellingProducts()
  {
    // Get table prefix for raw SQL parts
    $prefix = DB::getTablePrefix();

    return DB::table('sale_items')
        ->join('finished_products', 'sale_items.finished_product_id', '=', 'finished_products.id')
        ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
        ->where('sales.sale_date', '>=', Carbon::now()->subDays(30))
        ->select('finished_products.name',
                DB::raw("SUM({$prefix}sale_items.quantity) as total_sold"),
                DB::raw("SUM({$prefix}sale_items.line_total) as total_revenue"))
        ->groupBy('finished_products.id', 'finished_products.name')
        ->orderBy('total_revenue', 'desc')
        ->take(5)
        ->get();
  }

  private function getTopMaterials()
  {
    // Get table prefix for raw SQL parts
    $prefix = DB::getTablePrefix();

    return DB::table('manufacturing_consumptions')
        ->join('raw_materials', 'manufacturing_consumptions.raw_material_id', '=', 'raw_materials.id')
        ->where('manufacturing_consumptions.consumed_at', '>=', Carbon::now()->subDays(30))
        ->select('raw_materials.name',
                DB::raw("SUM({$prefix}manufacturing_consumptions.actual_quantity) as total_consumed"),
                DB::raw("SUM({$prefix}manufacturing_consumptions.total_cost) as total_cost"))
        ->groupBy('raw_materials.id', 'raw_materials.name')
        ->orderBy('total_cost', 'desc')
        ->take(5)
        ->get();
  }
}
