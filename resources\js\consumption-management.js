/*
 * Material Consumption Management for Manufacturing Orders
 */

'use strict';

$(function () {
  var consumptionModal = $('#consumptionModal');
  var currentOrderData = {};

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Manage Consumption
  $(document).on('click', '.manage-consumption', function() {
    var orderId = $(this).data('id');
    var orderNumber = $(this).data('number');

    $('#consumption-order-id').val(orderId);
    $('#consumption-order-number').text(orderNumber);
    $('#consumption-items').empty();

    // Load order details with consumptions
    $.get(`${baseUrl}manufacturing-orders/${orderId}`)
      .done(function(data) {
        currentOrderData = data;

        if (data.consumptions && data.consumptions.length > 0) {
          data.consumptions.forEach(function(consumption, index) {
            addConsumptionItem(consumption, index);
          });
        } else {
          $('#consumption-items').html('<div class="alert alert-info">No consumption records found for this order.</div>');
        }

        consumptionModal.modal('show');
      })
      .fail(function(xhr) {
        var response = xhr.responseJSON;
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: response && response.message ? response.message : 'Error loading consumption data',
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      });
  });

  // Add consumption item row
  function addConsumptionItem(consumption, index) {
    // Safe property access with fallbacks
    var actualQuantity = consumption.actual_quantity || 0;
    var plannedQuantity = consumption.planned_quantity || 0;
    var variance = actualQuantity - plannedQuantity;
    var varianceClass = variance > 0 ? 'text-danger' : variance < 0 ? 'text-warning' : 'text-success';
    var varianceIcon = variance > 0 ? 'ri-arrow-up-line' : variance < 0 ? 'ri-arrow-down-line' : 'ri-check-line';

    var materialName = consumption.raw_material ? consumption.raw_material.name : 'N/A';
    var currentStock = consumption.raw_material ? consumption.raw_material.current_stock : 0;
    var unitSymbol = consumption.raw_material && consumption.raw_material.unit ? consumption.raw_material.unit.symbol : '';
    var unitCost = consumption.unit_cost || 0;
    var totalCost = consumption.total_cost || 0;

    var html = `
      <div class="consumption-item border rounded p-3 mb-3">
        <input type="hidden" name="consumptions[${index}][id]" value="${consumption.id}">

        <div class="row">
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text" class="form-control" value="${materialName}" readonly>
              <label>Raw Material</label>
            </div>
            <small class="text-muted">Available: ${currentStock} ${unitSymbol}</small>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control" value="${plannedQuantity}" readonly>
              <label>Planned</label>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control actual-quantity-input"
                     name="consumptions[${index}][actual_quantity]"
                     value="${actualQuantity}"
                     data-planned="${plannedQuantity}"
                     data-unit-cost="${unitCost}"
                     required>
              <label>Actual</label>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="text" class="form-control variance-display" value="${variance.toFixed(3)}" readonly>
              <label>Variance</label>
            </div>
            <small class="${varianceClass}">
              <i class="${varianceIcon}"></i>
              ${variance > 0 ? 'Over' : variance < 0 ? 'Under' : 'Exact'}
            </small>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="text" class="form-control cost-display" value="${window.formatCurrency ? window.formatCurrency(totalCost) : '$' + totalCost.toFixed(2)}" readonly>
              <label>Total Cost</label>
            </div>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control" name="consumptions[${index}][notes]"
                        placeholder="Consumption notes..." rows="2">${consumption.notes || ''}</textarea>
              <label>Notes</label>
            </div>
          </div>
        </div>

        <div class="row mt-2">
          <div class="col-md-6">
            <small class="text-muted">Unit Cost: ${window.formatCurrency ? window.formatCurrency(unitCost) : '$' + unitCost.toFixed(2)} per ${unitSymbol}</small>
          </div>
          <div class="col-md-6">
            <small class="text-muted">Last Updated: ${consumption.consumed_at ? new Date(consumption.consumed_at).toLocaleString() : 'Not recorded'}</small>
          </div>
        </div>
      </div>
    `;

    $('#consumption-items').append(html);
  }

  // Update calculations when actual quantity changes
  $(document).on('input', '.actual-quantity-input', function() {
    var $item = $(this).closest('.consumption-item');
    var actualQuantity = parseFloat($(this).val()) || 0;
    var plannedQuantity = parseFloat($(this).data('planned')) || 0;
    var unitCost = parseFloat($(this).data('unit-cost')) || 0;

    // Calculate variance
    var variance = actualQuantity - plannedQuantity;
    var varianceClass = variance > 0 ? 'text-danger' : variance < 0 ? 'text-warning' : 'text-success';
    var varianceIcon = variance > 0 ? 'ri-arrow-up-line' : variance < 0 ? 'ri-arrow-down-line' : 'ri-check-line';
    var varianceLabel = variance > 0 ? 'Over' : variance < 0 ? 'Under' : 'Exact';

    // Update variance display
    $item.find('.variance-display').val(variance.toFixed(3));
    $item.find('small').removeClass('text-danger text-warning text-success').addClass(varianceClass);
    $item.find('small i').removeClass('ri-arrow-up-line ri-arrow-down-line ri-check-line').addClass(varianceIcon);
    $item.find('small').html(`<i class="${varianceIcon}"></i> ${varianceLabel}`);

    // Calculate total cost
    var totalCost = actualQuantity * unitCost;
    $item.find('.cost-display').val(window.formatCurrency ? window.formatCurrency(totalCost) : '$' + totalCost.toFixed(2));

    // Update overall totals
    updateConsumptionTotals();
  });

  // Update consumption totals
  function updateConsumptionTotals() {
    var totalPlanned = 0;
    var totalActual = 0;
    var totalCost = 0;

    $('.consumption-item').each(function() {
      var $item = $(this);
      var planned = parseFloat($item.find('input[readonly]').eq(1).val()) || 0;
      var actual = parseFloat($item.find('.actual-quantity-input').val()) || 0;
      var unitCost = parseFloat($item.find('.actual-quantity-input').data('unit-cost')) || 0;

      totalPlanned += planned;
      totalActual += actual;
      totalCost += (actual * unitCost);
    });

    // You can add a summary section to display these totals if needed
  }

  // Save consumption
  $('#save-consumption').on('click', function() {
    var orderId = $('#consumption-order-id').val();
    var consumptions = [];

    $('.consumption-item').each(function() {
      var $item = $(this);
      var consumptionData = {
        id: $item.find('input[name*="[id]"]').val(),
        actual_quantity: $item.find('.actual-quantity-input').val(),
        notes: $item.find('textarea').val()
      };

      consumptions.push(consumptionData);
    });

    if (consumptions.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'Warning!',
        text: 'No consumption data to save.',
        customClass: {
          confirmButton: 'btn btn-warning'
        }
      });
      return;
    }

    // Validate that all actual quantities are provided
    var hasEmptyQuantities = consumptions.some(function(consumption) {
      return !consumption.actual_quantity || parseFloat(consumption.actual_quantity) < 0;
    });

    if (hasEmptyQuantities) {
      Swal.fire({
        icon: 'warning',
        title: 'Warning!',
        text: 'Please provide valid actual quantities for all materials.',
        customClass: {
          confirmButton: 'btn btn-warning'
        }
      });
      return;
    }

    $.ajax({
      type: 'POST',
      url: `${baseUrl}manufacturing-orders/${orderId}/consumption`,
      data: { consumptions: consumptions },
      success: function(response) {
        consumptionModal.modal('hide');

        // Refresh the datatable if it exists
        if (typeof dt_manufacturing_order !== 'undefined') {
          dt_manufacturing_order.draw();
        }

        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function(xhr) {
        var response = xhr.responseJSON;
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: response.message || 'Error updating consumption',
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      }
    });
  });


});
