<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'purchase_id',
        'raw_material_id',
        'quantity',
        'unit_price',
        'line_total',
        'received_quantity',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'line_total' => 'decimal:2',
        'received_quantity' => 'decimal:3',
    ];

    /**
     * Get the purchase that this item belongs to.
     */
    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    /**
     * Get the raw material for this purchase item.
     */
    public function rawMaterial(): BelongsTo
    {
        return $this->belongsTo(RawMaterial::class);
    }

    /**
     * Get the unit price with currency symbol.
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return '$' . number_format($this->unit_price, 2);
    }

    /**
     * Get the line total with currency symbol.
     */
    public function getFormattedLineTotalAttribute(): string
    {
        return '$' . number_format($this->line_total, 2);
    }

    /**
     * Get the quantity with unit symbol.
     */
    public function getFormattedQuantityAttribute(): string
    {
        return $this->quantity . ' ' . ($this->rawMaterial->unit->symbol ?? '');
    }

    /**
     * Get the received quantity with unit symbol.
     */
    public function getFormattedReceivedQuantityAttribute(): string
    {
        return $this->received_quantity . ' ' . ($this->rawMaterial->unit->symbol ?? '');
    }

    /**
     * Get the pending quantity (ordered - received).
     */
    public function getPendingQuantityAttribute(): float
    {
        return $this->quantity - $this->received_quantity;
    }

    /**
     * Get the formatted pending quantity.
     */
    public function getFormattedPendingQuantityAttribute(): string
    {
        return $this->pending_quantity . ' ' . ($this->rawMaterial->unit->symbol ?? '');
    }

    /**
     * Get the completion percentage.
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->quantity <= 0) {
            return 0;
        }
        return ($this->received_quantity / $this->quantity) * 100;
    }

    /**
     * Check if the item is fully received.
     */
    public function isFullyReceived(): bool
    {
        return $this->received_quantity >= $this->quantity;
    }

    /**
     * Check if the item is partially received.
     */
    public function isPartiallyReceived(): bool
    {
        return $this->received_quantity > 0 && $this->received_quantity < $this->quantity;
    }

    /**
     * Get the variance between ordered and received quantities.
     */
    public function getQuantityVarianceAttribute(): float
    {
        return $this->received_quantity - $this->quantity;
    }

    /**
     * Get the variance status (over, under, exact).
     */
    public function getVarianceStatusAttribute(): string
    {
        $variance = $this->quantity_variance;
        if ($variance > 0) {
            return 'over';
        } elseif ($variance < 0) {
            return 'under';
        } else {
            return 'exact';
        }
    }

    /**
     * Get the variance color for UI.
     */
    public function getVarianceColorAttribute(): string
    {
        return match ($this->variance_status) {
            'over' => 'warning',
            'under' => 'danger',
            'exact' => 'success',
            default => 'secondary',
        };
    }

    /**
     * Boot the model to automatically calculate line total.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($purchaseItem) {
            if ($purchaseItem->quantity && $purchaseItem->unit_price) {
                $purchaseItem->line_total = $purchaseItem->quantity * $purchaseItem->unit_price;
            }
        });
    }
}
