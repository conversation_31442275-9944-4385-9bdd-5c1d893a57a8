<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('finished_product_movements', function (Blueprint $table) {
            $table->foreign('manufacturing_order_id')
                  ->references('id')
                  ->on('manufacturing_orders')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('finished_product_movements', function (Blueprint $table) {
            $table->dropForeign(['manufacturing_order_id']);
        });
    }
};
