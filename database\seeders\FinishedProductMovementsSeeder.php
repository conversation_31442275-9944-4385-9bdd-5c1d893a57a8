<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FinishedProduct;
use App\Models\FinishedProductMovement;
use App\Models\User;

class FinishedProductMovementsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for movements
        $user = User::first();
        
        // Get some finished products
        $chocolateCake = FinishedProduct::where('name', 'Chocolate Cake')->first();
        $vanillaCupcakes = FinishedProduct::where('name', 'Vanilla Cupcakes')->first();
        $artisanBread = FinishedProduct::where('name', 'Artisan Bread Loaf')->first();
        
        if (!$chocolateCake || !$vanillaCupcakes || !$artisanBread || !$user) {
            return; // Skip if required data doesn't exist
        }

        $movements = [
            // Chocolate Cake movements
            [
                'finished_product_id' => $chocolateCake->id,
                'movement_type' => 'production',
                'quantity' => 10.000,
                'unit_cost' => 15.50,
                'total_value' => 155.00,
                'document_reference' => 'MO-20241201-0001',
                'reason' => 'Production completion',
                'notes' => 'Manufacturing Order: MO-20241201-0001',
                'stock_before' => 0.000,
                'stock_after' => 10.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(5),
            ],
            [
                'finished_product_id' => $chocolateCake->id,
                'movement_type' => 'outbound',
                'quantity' => -2.000,
                'unit_cost' => 25.99,
                'total_value' => 51.98,
                'document_reference' => 'POS-20241202-0001',
                'reason' => 'POS Sale',
                'notes' => 'Sale to: John Doe',
                'stock_before' => 10.000,
                'stock_after' => 8.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(4),
            ],
            [
                'finished_product_id' => $chocolateCake->id,
                'movement_type' => 'outbound',
                'quantity' => -3.000,
                'unit_cost' => 25.99,
                'total_value' => 77.97,
                'document_reference' => 'POS-20241203-0002',
                'reason' => 'POS Sale',
                'notes' => 'Sale to: Walk-in Customer',
                'stock_before' => 8.000,
                'stock_after' => 5.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(3),
            ],

            // Vanilla Cupcakes movements
            [
                'finished_product_id' => $vanillaCupcakes->id,
                'movement_type' => 'production',
                'quantity' => 24.000,
                'unit_cost' => 12.50,
                'total_value' => 300.00,
                'document_reference' => 'MO-20241201-0002',
                'reason' => 'Production completion',
                'notes' => 'Manufacturing Order: MO-20241201-0002',
                'stock_before' => 0.000,
                'stock_after' => 24.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(6),
            ],
            [
                'finished_product_id' => $vanillaCupcakes->id,
                'movement_type' => 'outbound',
                'quantity' => -6.000,
                'unit_cost' => 16.99,
                'total_value' => 101.94,
                'document_reference' => 'POS-20241202-0003',
                'reason' => 'POS Sale',
                'notes' => 'Sale to: Sarah Smith',
                'stock_before' => 24.000,
                'stock_after' => 18.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(4),
            ],
            [
                'finished_product_id' => $vanillaCupcakes->id,
                'movement_type' => 'adjustment',
                'quantity' => -2.000,
                'unit_cost' => 0.00,
                'total_value' => 0.00,
                'document_reference' => 'ADJ-20241204-0001',
                'reason' => 'Inventory adjustment',
                'notes' => 'Damaged products removed from inventory',
                'stock_before' => 18.000,
                'stock_after' => 16.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(2),
            ],

            // Artisan Bread movements
            [
                'finished_product_id' => $artisanBread->id,
                'movement_type' => 'production',
                'quantity' => 20.000,
                'unit_cost' => 3.50,
                'total_value' => 70.00,
                'document_reference' => 'MO-20241203-0001',
                'reason' => 'Production completion',
                'notes' => 'Manufacturing Order: MO-20241203-0001',
                'stock_before' => 0.000,
                'stock_after' => 20.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(3),
            ],
            [
                'finished_product_id' => $artisanBread->id,
                'movement_type' => 'outbound',
                'quantity' => -5.000,
                'unit_cost' => 6.99,
                'total_value' => 34.95,
                'document_reference' => 'POS-20241204-0001',
                'reason' => 'POS Sale',
                'notes' => 'Sale to: Mike Johnson',
                'stock_before' => 20.000,
                'stock_after' => 15.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(2),
            ],
            [
                'finished_product_id' => $artisanBread->id,
                'movement_type' => 'outbound',
                'quantity' => -7.000,
                'unit_cost' => 6.99,
                'total_value' => 48.93,
                'document_reference' => 'POS-20241205-0001',
                'reason' => 'POS Sale',
                'notes' => 'Sale to: Restaurant Order',
                'stock_before' => 15.000,
                'stock_after' => 8.000,
                'user_id' => $user->id,
                'movement_date' => now()->subDays(1),
            ],
        ];

        foreach ($movements as $movement) {
            FinishedProductMovement::create($movement);
        }
    }
}
