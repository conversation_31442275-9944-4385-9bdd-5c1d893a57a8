<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Recipe extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'finished_product_id',
        'raw_material_id',
        'quantity_required',
        'unit_id',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity_required' => 'decimal:3',
        'sort_order' => 'integer',
    ];

    /**
     * Get the finished product that this recipe belongs to.
     */
    public function finishedProduct(): BelongsTo
    {
        return $this->belongsTo(FinishedProduct::class);
    }

    /**
     * Get the raw material used in this recipe.
     */
    public function rawMaterial(): BelongsTo
    {
        return $this->belongsTo(RawMaterial::class);
    }

    /**
     * Get the unit for the quantity required.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Calculate the cost for this recipe item with unit conversion.
     */
    public function getCostAttribute(): float
    {
        $convertedQuantity = $this->getConvertedQuantity();
        return $convertedQuantity * ($this->rawMaterial->unit_price ?? 0);
    }

    /**
     * Get the quantity required converted to the raw material's unit.
     */
    public function getConvertedQuantity(): float
    {
        // If recipe unit matches raw material unit, no conversion needed
        if ($this->unit_id === $this->rawMaterial->unit_id) {
            return $this->quantity_required;
        }

        // Convert from recipe unit to raw material unit
        try {
            return $this->unit->convertTo($this->rawMaterial->unit, $this->quantity_required);
        } catch (\InvalidArgumentException $e) {
            // If conversion is not possible, return original quantity
            // This should be handled in validation
            return $this->quantity_required;
        }
    }

    /**
     * Check if enough raw material is available for this recipe item with unit conversion.
     */
    public function isAvailable(float $productionQuantity = 1): bool
    {
        $requiredQuantity = $this->getConvertedQuantity() * $productionQuantity;
        return $this->rawMaterial->current_stock >= $requiredQuantity;
    }

    /**
     * Get the shortage quantity if not enough raw material is available with unit conversion.
     */
    public function getShortageQuantity(float $productionQuantity = 1): float
    {
        $requiredQuantity = $this->getConvertedQuantity() * $productionQuantity;
        $shortage = $requiredQuantity - $this->rawMaterial->current_stock;
        return max(0, $shortage);
    }

    /**
     * Get the availability status for UI display with unit conversion.
     */
    public function getAvailabilityStatusAttribute(): string
    {
        $convertedQuantity = $this->getConvertedQuantity();

        if ($this->rawMaterial->current_stock >= $convertedQuantity) {
            return 'available';
        } elseif ($this->rawMaterial->current_stock > 0) {
            return 'partial';
        }
        return 'unavailable';
    }

    /**
     * Check if the recipe unit is compatible with the raw material unit.
     */
    public function isUnitCompatible(): bool
    {
        if ($this->unit_id === $this->rawMaterial->unit_id) {
            return true;
        }

        return $this->unit->canConvertTo($this->rawMaterial->unit);
    }

    /**
     * Get the conversion factor from recipe unit to raw material unit.
     */
    public function getUnitConversionFactor(): float
    {
        if ($this->unit_id === $this->rawMaterial->unit_id) {
            return 1.0;
        }

        try {
            return $this->unit->getConversionFactor($this->rawMaterial->unit);
        } catch (\InvalidArgumentException $e) {
            return 1.0;
        }
    }

    /**
     * Get the display text for unit conversion.
     */
    public function getUnitConversionDisplayAttribute(): string
    {
        if ($this->unit_id === $this->rawMaterial->unit_id) {
            return '';
        }

        if (!$this->isUnitCompatible()) {
            return 'Units not compatible';
        }

        $convertedQuantity = $this->getConvertedQuantity();
        return "{$this->quantity_required} {$this->unit->symbol} = {$convertedQuantity} {$this->rawMaterial->unit->symbol}";
    }

    /**
     * Get the availability status color for UI.
     */
    public function getAvailabilityColorAttribute(): string
    {
        return match ($this->availability_status) {
            'available' => 'green',
            'partial' => 'orange',
            'unavailable' => 'red',
            default => 'gray',
        };
    }
}
