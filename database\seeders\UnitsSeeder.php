<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Unit;
use App\Models\UnitCategory;

class UnitsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get categories
        $weightCategory = UnitCategory::where('name', 'Weight')->first();
        $volumeCategory = UnitCategory::where('name', 'Volume')->first();
        $lengthCategory = UnitCategory::where('name', 'Length')->first();
        $areaCategory = UnitCategory::where('name', 'Area')->first();
        $countCategory = UnitCategory::where('name', 'Count')->first();
        $timeCategory = UnitCategory::where('name', 'Time')->first();

        $units = [
            // Weight units (kg as base unit)
            [
                'name' => 'Kilogram', 'symbol' => 'kg', 'description' => 'Base unit of mass',
                'category_id' => $weightCategory->id, 'conversion_factor' => 1, 'is_base_unit' => true
            ],
            [
                'name' => 'Gram', 'symbol' => 'g', 'description' => 'Unit of mass',
                'category_id' => $weightCategory->id, 'conversion_factor' => 1000, 'is_base_unit' => false
            ],
            [
                'name' => 'Pound', 'symbol' => 'lb', 'description' => 'Unit of mass',
                'category_id' => $weightCategory->id, 'conversion_factor' => 2.20462, 'is_base_unit' => false
            ],
            [
                'name' => 'Ounce', 'symbol' => 'oz', 'description' => 'Unit of mass',
                'category_id' => $weightCategory->id, 'conversion_factor' => 35.274, 'is_base_unit' => false
            ],
            [
                'name' => 'Ton', 'symbol' => 't', 'description' => 'Unit of mass',
                'category_id' => $weightCategory->id, 'conversion_factor' => 0.001, 'is_base_unit' => false
            ],

            // Volume units (L as base unit)
            [
                'name' => 'Liter', 'symbol' => 'L', 'description' => 'Base unit of volume',
                'category_id' => $volumeCategory->id, 'conversion_factor' => 1, 'is_base_unit' => true
            ],
            [
                'name' => 'Milliliter', 'symbol' => 'mL', 'description' => 'Unit of volume',
                'category_id' => $volumeCategory->id, 'conversion_factor' => 1000, 'is_base_unit' => false
            ],
            [
                'name' => 'Gallon', 'symbol' => 'gal', 'description' => 'Unit of volume',
                'category_id' => $volumeCategory->id, 'conversion_factor' => 0.264172, 'is_base_unit' => false
            ],
            [
                'name' => 'Quart', 'symbol' => 'qt', 'description' => 'Unit of volume',
                'category_id' => $volumeCategory->id, 'conversion_factor' => 1.05669, 'is_base_unit' => false
            ],
            [
                'name' => 'Pint', 'symbol' => 'pt', 'description' => 'Unit of volume',
                'category_id' => $volumeCategory->id, 'conversion_factor' => 2.11338, 'is_base_unit' => false
            ],

            // Length units (m as base unit)
            [
                'name' => 'Meter', 'symbol' => 'm', 'description' => 'Base unit of length',
                'category_id' => $lengthCategory->id, 'conversion_factor' => 1, 'is_base_unit' => true
            ],
            [
                'name' => 'Centimeter', 'symbol' => 'cm', 'description' => 'Unit of length',
                'category_id' => $lengthCategory->id, 'conversion_factor' => 100, 'is_base_unit' => false
            ],
            [
                'name' => 'Millimeter', 'symbol' => 'mm', 'description' => 'Unit of length',
                'category_id' => $lengthCategory->id, 'conversion_factor' => 1000, 'is_base_unit' => false
            ],
            [
                'name' => 'Inch', 'symbol' => 'in', 'description' => 'Unit of length',
                'category_id' => $lengthCategory->id, 'conversion_factor' => 39.3701, 'is_base_unit' => false
            ],
            [
                'name' => 'Foot', 'symbol' => 'ft', 'description' => 'Unit of length',
                'category_id' => $lengthCategory->id, 'conversion_factor' => 3.28084, 'is_base_unit' => false
            ],

            // Count units (pcs as base unit)
            [
                'name' => 'Piece', 'symbol' => 'pcs', 'description' => 'Base unit for counting individual items',
                'category_id' => $countCategory->id, 'conversion_factor' => 1, 'is_base_unit' => true
            ],
            [
                'name' => 'Dozen', 'symbol' => 'dz', 'description' => 'Group of 12 items',
                'category_id' => $countCategory->id, 'conversion_factor' => 0.083333, 'is_base_unit' => false
            ],
            [
                'name' => 'Pair', 'symbol' => 'pr', 'description' => 'Group of 2 items',
                'category_id' => $countCategory->id, 'conversion_factor' => 0.5, 'is_base_unit' => false
            ],
            [
                'name' => 'Set', 'symbol' => 'set', 'description' => 'Complete group of items',
                'category_id' => $countCategory->id, 'conversion_factor' => 1, 'is_base_unit' => false
            ],
            [
                'name' => 'Box', 'symbol' => 'box', 'description' => 'Container unit',
                'category_id' => $countCategory->id, 'conversion_factor' => 1, 'is_base_unit' => false
            ],
            [
                'name' => 'Pack', 'symbol' => 'pack', 'description' => 'Package unit',
                'category_id' => $countCategory->id, 'conversion_factor' => 1, 'is_base_unit' => false
            ],

            // Area units (m² as base unit)
            [
                'name' => 'Square Meter', 'symbol' => 'm²', 'description' => 'Base unit of area',
                'category_id' => $areaCategory->id, 'conversion_factor' => 1, 'is_base_unit' => true
            ],
            [
                'name' => 'Square Foot', 'symbol' => 'ft²', 'description' => 'Unit of area',
                'category_id' => $areaCategory->id, 'conversion_factor' => 10.7639, 'is_base_unit' => false
            ],

            // Time units (hr as base unit)
            [
                'name' => 'Hour', 'symbol' => 'hr', 'description' => 'Base unit of time',
                'category_id' => $timeCategory->id, 'conversion_factor' => 1, 'is_base_unit' => true
            ],
            [
                'name' => 'Minute', 'symbol' => 'min', 'description' => 'Unit of time',
                'category_id' => $timeCategory->id, 'conversion_factor' => 60, 'is_base_unit' => false
            ],
        ];

        foreach ($units as $unitData) {
            Unit::create($unitData + ['is_active' => true]);
        }

        // Update base_unit_id in categories
        $this->updateCategoryBaseUnits();
    }

    /**
     * Update the base_unit_id in unit categories.
     */
    private function updateCategoryBaseUnits(): void
    {
        $categories = UnitCategory::all();

        foreach ($categories as $category) {
            $baseUnit = Unit::where('category_id', $category->id)
                ->where('is_base_unit', true)
                ->first();

            if ($baseUnit) {
                $category->update(['base_unit_id' => $baseUnit->id]);
            }
        }
    }
}
