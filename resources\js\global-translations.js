/*
 * Global Translation System for JavaScript
 */

'use strict';

// Global translation object
window.translations = {};

// Translation function
window.__ = function(key, replacements = {}) {
  let translation = window.translations[key] || key;

  // Replace placeholders
  Object.keys(replacements).forEach(placeholder => {
    translation = translation.replace(`:${placeholder}`, replacements[placeholder]);
  });

  return translation;
};

// Load translations from server
function loadTranslations() {
  // Only load if we're not on the login page
  if (window.location.pathname === '/login') {
    return;
  }

  const locale = document.documentElement.lang || 'en';

  // Use XMLHttpRequest to avoid any potential navigation issues
  const xhr = new XMLHttpRequest();
  xhr.open('GET', `/translations/${locale}`, true);
  xhr.onreadystatechange = function() {
    if (xhr.readyState === 4) {
      if (xhr.status === 200) {
        try {
          window.translations = JSON.parse(xhr.responseText);
        } catch (e) {
          console.warn('Failed to parse translations:', e);
          loadFallbackTranslations();
        }
      } else {
        console.warn('Failed to load translations, status:', xhr.status);
        loadFallbackTranslations();
      }
    }
  };
  xhr.send();
}

function loadFallbackTranslations() {
  const locale = document.documentElement.lang || 'en';
  if (locale !== 'en') {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', '/translations/en', true);
    xhr.onreadystatechange = function() {
      if (xhr.readyState === 4 && xhr.status === 200) {
        try {
          window.translations = JSON.parse(xhr.responseText);
        } catch (e) {
          console.error('Failed to parse fallback translations:', e);
        }
      }
    };
    xhr.send();
  }
}

// Load translations immediately when script loads
loadTranslations();

// Also load when DOM is ready as fallback
document.addEventListener('DOMContentLoaded', function() {
  if (Object.keys(window.translations).length === 0) {
    loadTranslations();
  }
});
