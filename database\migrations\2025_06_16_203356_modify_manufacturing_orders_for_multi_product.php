<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manufacturing_orders', function (Blueprint $table) {
            // Make single product fields nullable for backward compatibility
            $table->unsignedBigInteger('finished_product_id')->nullable()->change();
            $table->decimal('planned_quantity', 10, 3)->nullable()->change();
            $table->decimal('produced_quantity', 10, 3)->nullable()->change();

            // Add new fields for multi-product orders
            $table->string('order_type', 20)->default('single')->after('order_number'); // 'single' or 'multi'
            $table->decimal('total_estimated_cost', 12, 2)->nullable()->after('estimated_cost');
            $table->decimal('total_actual_cost', 12, 2)->nullable()->after('actual_cost');
            $table->integer('total_estimated_time_minutes')->nullable()->after('estimated_time_minutes');
            $table->integer('total_actual_time_minutes')->nullable()->after('actual_time_minutes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manufacturing_orders', function (Blueprint $table) {
            // Revert changes
            $table->dropColumn([
                'order_type',
                'total_estimated_cost',
                'total_actual_cost',
                'total_estimated_time_minutes',
                'total_actual_time_minutes'
            ]);

            // Make fields required again (assuming data exists)
            $table->unsignedBigInteger('finished_product_id')->nullable(false)->change();
            $table->decimal('planned_quantity', 10, 3)->nullable(false)->change();
            $table->decimal('produced_quantity', 10, 3)->default(0)->nullable(false)->change();
        });
    }
};
