<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class RawMaterial extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'unit_id',
        'current_stock',
        'minimum_stock',
        'unit_price',
        'supplier',
        'image_path',
        'image_alt',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'current_stock' => 'decimal:3',
        'minimum_stock' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the unit that this raw material uses.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Get the stock movements for this raw material.
     */
    public function movements(): HasMany
    {
        return $this->hasMany(RawMaterialMovement::class)->orderBy('movement_date', 'desc');
    }

    /**
     * Get the recipes that use this raw material.
     */
    public function recipes(): HasMany
    {
        return $this->hasMany(Recipe::class);
    }

    /**
     * Get the manufacturing consumptions for this raw material.
     */
    public function manufacturingConsumptions(): HasMany
    {
        return $this->hasMany(ManufacturingConsumption::class);
    }

    /**
     * Get the purchase items for this raw material.
     */
    public function purchaseItems(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * Scope a query to only include active raw materials.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include low stock items.
     */
    public function scopeLowStock($query)
    {
        return $query->whereColumn('current_stock', '<=', 'minimum_stock');
    }

    /**
     * Check if the raw material is low on stock.
     */
    public function isLowStock(): bool
    {
        return $this->current_stock <= $this->minimum_stock;
    }

    /**
     * Get the stock status (normal, warning, critical).
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->current_stock <= 0) {
            return 'critical';
        } elseif ($this->current_stock <= $this->minimum_stock) {
            return 'warning';
        }
        return 'normal';
    }

    /**
     * Get the stock status color for UI.
     */
    public function getStockStatusColorAttribute(): string
    {
        return match ($this->stock_status) {
            'critical' => 'red',
            'warning' => 'orange',
            'normal' => 'green',
            default => 'gray',
        };
    }

    /**
     * Get the total value of current stock.
     */
    public function getStockValueAttribute(): float
    {
        return $this->current_stock * ($this->unit_price ?? 0);
    }

    /**
     * Get the image URL for display.
     */
    public function getImageUrlAttribute(): string
    {
        if ($this->image_path && file_exists(public_path($this->image_path))) {
            return asset($this->image_path);
        }
        return asset('assets/img/default/raw-material-placeholder.svg');
    }

    /**
     * Get the image alt text.
     */
    public function getImageAltTextAttribute(): string
    {
        return $this->image_alt ?: $this->name;
    }
}
