/**
 * App roles management
 */

'use strict';

// Load role cards and datatable
$(function () {
  var dtUserTable = $('.datatables-users'),
    dt_User,
    statusObj = {
      1: { title: 'Pending', class: 'bg-label-warning' },
      2: { title: 'Active', class: 'bg-label-success' },
      3: { title: 'Inactive', class: 'bg-label-secondary' }
    };

  var userView = baseUrl + 'app/user/view/account';

  // Load role cards
  loadRoleCards();

  // Function to load role cards
  function loadRoleCards() {
    $.ajax({
      url: baseUrl + 'roles/cards',
      type: 'GET',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          renderRoleCards(response.roles);
        } else {
          console.error('Failed to load role cards');
        }
      },
      error: function(xhr, status, error) {
        console.error('Error loading role cards:', error);
      }
    });
  }

  // Function to render role cards
  function renderRoleCards(roles) {
    const container = $('#roleCardsContainer');
    const addRoleCard = $('#addRoleCard');

    // Clear existing cards except add role card
    container.empty();

    // Add role cards
    roles.forEach(function(role) {
      const roleCard = createRoleCard(role);
      container.append(roleCard);
    });

    // Add the "Add Role" card at the end
    container.append(addRoleCard);

    // Reinitialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
  }

  // Function to create a role card
  function createRoleCard(role) {
    let avatarsHtml = '';
    let remainingCount = role.remaining_count;

    // Generate user avatars
    role.users.forEach(function(user, index) {
      if (index < 3) { // Show max 3 avatars
        const stateNum = Math.floor(Math.random() * 7);
        const states = ['success', 'danger', 'warning', 'info', 'dark', 'primary', 'secondary'];
        const state = states[stateNum];

        avatarsHtml += `
          <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="${user.name}" class="avatar pull-up">
            <span class="avatar-initial rounded-circle bg-label-${state}">${user.initials}</span>
          </li>
        `;
      }
    });

    // Add remaining count if there are more users
    if (remainingCount > 0) {
      avatarsHtml += `
        <li class="avatar">
          <span class="avatar-initial rounded-circle pull-up bg-lighter text-body" data-bs-toggle="tooltip" data-bs-placement="bottom" title="${remainingCount} more">+${remainingCount}</span>
        </li>
      `;
    }

    return `
      <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
              <p class="mb-0">Total ${role.users_count} users</p>
              <ul class="list-unstyled d-flex align-items-center avatar-group mb-0">
                ${avatarsHtml}
              </ul>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <div class="role-heading">
                <h5 class="mb-1">${role.name}</h5>
                <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#addRoleModal" class="role-edit-modal" data-role-id="${role.id}">
                  <p class="mb-0">Edit Role</p>
                </a>
              </div>
              <a href="javascript:void(0);" class="text-secondary"><i class="ri-file-copy-line ri-22px"></i></a>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // Users List datatable
  if (dtUserTable.length) {
    dt_User = dtUserTable.DataTable({
      ajax: {
        url: baseUrl + 'user-list',
        type: 'GET',
        data: function(d) {
          // Add role filter to the request
          d.role_filter = $('#UserRole').val();
        }
      },
      columns: [
        // columns according to API response
        { data: 'id' },
        { data: 'id' },
        { data: 'name' },
        { data: 'email' },
        { data: 'role' },
        { data: 'plan', defaultContent: 'Basic' },
        { data: 'status' },
        { data: 'actions' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          orderable: false,
          searchable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          // For Checkboxes
          targets: 1,
          orderable: false,
          checkboxes: {
            selectAllRender: '<input type="checkbox" class="form-check-input">'
          },
          render: function () {
            return '<input type="checkbox" class="dt-checkboxes form-check-input" >';
          },
          searchable: false
        },
        {
          // User full name and email
          targets: 2,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $name = full['name'],
              $email = full['email'],
              $image = full['avatar'];
            if ($image) {
              // For Avatar image
              var $output =
                '<img src="' + assetsPath + 'img/avatars/' + $image + '" alt="Avatar" class="rounded-circle">';
            } else {
              // For Avatar badge
              var stateNum = Math.floor(Math.random() * 6) + 1;
              var states = ['success', 'danger', 'warning', 'info', 'dark', 'primary', 'secondary'];
              var $state = states[stateNum],
                $initials = $name.match(/\b\w/g) || [];
              $initials = (($initials.shift() || '') + ($initials.pop() || '')).toUpperCase();
              $output = '<span class="avatar-initial rounded-circle bg-label-' + $state + '">' + $initials + '</span>';
            }
            // Creates full output for row
            var $row_output =
              '<div class="d-flex justify-content-left align-items-center">' +
              '<div class="avatar-wrapper">' +
              '<div class="avatar avatar-sm me-3">' +
              $output +
              '</div>' +
              '</div>' +
              '<div class="d-flex flex-column">' +
              '<a href="' +
              userView +
              '" class="text-heading"><span class="fw-medium text-truncate">' +
              $name +
              '</span></a>' +
              '<small>' +
              $email +
              '</small>' +
              '</div>' +
              '</div>';
            return $row_output;
          }
        },
        {
          // User email
          targets: 3,
          render: function (data, type, full, meta) {
            var $email = full['email'];
            return '<span >' + $email + '</span>';
          }
        },
        {
          // User Role
          targets: 4,
          render: function (data, type, full, meta) {
            return full['role']; // Role is already formatted from backend
          }
        },
        {
          // Plans
          targets: 5,
          render: function (data, type, full, meta) {
            return '<span class="text-heading">Basic</span>'; // Default plan
          }
        },
        {
          // User Status
          targets: 6,
          render: function (data, type, full, meta) {
            return full['status']; // Status is already formatted from backend
          }
        },
        {
          // Actions
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return full['actions']; // Actions are already formatted from backend
          }
        }
      ],
      order: [[2, 'desc']],
      dom:
        '<"row mx-1"' +
        '<"col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start gap-4 mt-5 mt-md-0"l<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start"B>>' +
        '<"col-sm-12 col-md-7"<"dt-action-buttons d-flex align-items-center justify-content-md-end justify-content-center flex-column flex-sm-row flex-nowrap"<"me-sm-4"f><"user_role w-px-200 mb-5 mb-sm-0">>>' +
        '>t' +
        '<"row mx-1"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      language: {
        sLengthMenu: 'Show _MENU_',
        search: '',
        searchPlaceholder: 'Search User',
        paginate: {
          next: '<i class="ri-arrow-right-s-line"></i>',
          previous: '<i class="ri-arrow-left-s-line"></i>'
        }
      },
      // for buttons
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-outline-secondary dropdown-toggle me-4 waves-effect waves-light',
          text: '<i class="ri-download-line ri-16px me-1"></i> <span class="d-none d-sm-inline-block">Export</span>',
          buttons: [
            {
              extend: 'print',
              text: '<i class="ri-printer-line me-1" ></i>Print',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be print
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              },
              customize: function (win) {
                //customize print view for dark
                $(win.document.body)
                  .css('color', headingColor)
                  .css('border-color', borderColor)
                  .css('background-color', bodyBg);
                $(win.document.body)
                  .find('table')
                  .addClass('compact')
                  .css('color', 'inherit')
                  .css('border-color', 'inherit')
                  .css('background-color', 'inherit');
              }
            },
            {
              extend: 'csv',
              text: '<i class="ri-file-text-line me-1" ></i>Csv',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'excel',
              text: '<i class="ri-file-excel-line me-1"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'pdf',
              text: '<i class="ri-file-pdf-line me-1"></i>Pdf',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'copy',
              text: '<i class="ri-file-copy-line me-1"></i>Copy',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            }
          ]
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Details of ' + data['full_name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      },
      initComplete: function () {
        // Adding role filter once table initialized
        this.api()
          .columns(4)
          .every(function () {
            var column = this;
            var select = $(
              '<select id="UserRole" class="form-select text-capitalize form-select-sm"><option value=""> Select Role </option></select>'
            )
              .appendTo('.user_role')
              .on('change', function () {
                // Reload the table with the new role filter
                dt_User.ajax.reload();
              });

            column
              .data()
              .unique()
              .sort()
              .each(function (d, j) {
                select.append("<option value=' "+ d + "' class='text-capitalize'>" + d + "</option>" );
              });
          });
      }
    });
    $('.add-new').html(
      "<button class='btn btn-primary' data-bs-toggle='modal' data-bs-target='#editUser'><i class='ri-add-line me-0 me-sm-1'></i><span class= 'd-none d-sm-inline-block'> Add User </span ></button>"
    );
  }

  // Delete Record
  $('.datatables-users tbody').on('click', '.delete-record', function () {
    dt_User.row($(this).parents('tr')).remove().draw();
  });

  // Role edit functionality
  $(document).on('click', '.role-edit-modal', function() {
    var roleTitle = $('.role-title');
    var roleId = $(this).data('role-id');

    if (roleId) {
      roleTitle.html('Edit Role');
      loadRoleForEdit(roleId);
    } else {
      roleTitle.html('Add New Role');
      resetRoleForm();
    }
  });

  // Function to load role data for editing (will be made global later)

  // Function moved to global scope

  // Functions moved to global scope

  // Select all functionality
  $(document).on('change', '#selectAll', function() {
    var isChecked = $(this).is(':checked');
    $('.permission-checkbox').prop('checked', isChecked);
  });

  // Update select all when individual checkboxes change
  $(document).on('change', '.permission-checkbox', function() {
    updateSelectAllCheckbox();
  });

  // Add new role button
  $(document).on('click', '.add-new-role', function() {
    var roleTitle = $('.role-title');
    roleTitle.html('Add New Role');
    resetRoleForm();
  });

  // Initialize permissions when modal is shown
  $('#addRoleModal').on('shown.bs.modal', function() {
    if (!$('#roleId').val()) {
      loadPermissions();
    }
  });

  // Form submission
  $('#addRoleForm').on('submit', function(e) {
    e.preventDefault();

    var roleId = $('#roleId').val();
    var roleName = $('#modalRoleName').val();
    var selectedPermissions = [];

    $('.permission-checkbox:checked').each(function() {
      selectedPermissions.push($(this).val());
    });

    var formData = {
      name: roleName,
      permissions: selectedPermissions
    };

    var url = roleId ? baseUrl + 'roles/' + roleId : baseUrl + 'roles';
    var method = roleId ? 'PUT' : 'POST';

    $.ajax({
      url: url,
      type: method,
      data: formData,
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          $('#addRoleModal').modal('hide');

          // Show success message
          if (typeof Swal !== 'undefined') {
            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: response.message,
              timer: 2000,
              showConfirmButton: false
            });
          }

          // Reload role cards
          loadRoleCards();
        } else {
          if (typeof Swal !== 'undefined') {
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: response.message || 'Failed to save role'
            });
          }
        }
      },
      error: function(xhr, status, error) {
        var errorMessage = 'Failed to save role';
        if (xhr.responseJSON && xhr.responseJSON.message) {
          errorMessage = xhr.responseJSON.message;
        }

        if (typeof Swal !== 'undefined') {
          Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: errorMessage
          });
        }
      }
    });
  });
});

// Global function for editing roles (called from DataTable actions)
function editRole(roleId) {
  $('.role-title').html('Edit Role');
  loadRoleForEdit(roleId);
  $('#addRoleModal').modal('show');
}

// Global function for deleting roles (called from DataTable actions)
function deleteRole(roleId) {
  if (typeof Swal !== 'undefined') {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: baseUrl + 'roles/' + roleId,
          type: 'DELETE',
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            if (response.success) {
              Swal.fire({
                icon: 'success',
                title: 'Deleted!',
                text: response.message,
                timer: 2000,
                showConfirmButton: false
              });

              // Reload role cards
              loadRoleCards();
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: response.message || 'Failed to delete role'
              });
            }
          },
          error: function(xhr, status, error) {
            var errorMessage = 'Failed to delete role';
            if (xhr.responseJSON && xhr.responseJSON.message) {
              errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: errorMessage
            });
          }
        });
      }
    });
  }
}

// Helper function to load role data for editing (make it global)
function loadRoleForEdit(roleId) {
  $.ajax({
    url: baseUrl + 'roles/' + roleId + '/edit',
    type: 'GET',
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        // Set role ID and name
        $('#roleId').val(response.role.id);
        $('#modalRoleName').val(response.role.name);

        // Load permissions
        loadPermissions(response.permissions, response.rolePermissions);
      } else {
        console.error('Failed to load role data:', response);
      }
    },
    error: function(xhr, status, error) {
      console.error('Error loading role data:', error);
      console.error('XHR:', xhr);
    }
  });
}

// Function to load permissions dynamically
function loadPermissions(allPermissions = null, rolePermissions = []) {
  if (!allPermissions) {
    // Load permissions from server
    $.ajax({
      url: baseUrl + 'roles/permissions',
      type: 'GET',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          renderPermissions(response.permissions, rolePermissions);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error loading permissions:', error);
      }
    });
  } else {
    renderPermissions(allPermissions, rolePermissions);
  }
}

// Function to render permissions in cards layout
function renderPermissions(permissions, rolePermissions = []) {
  var permissionsHtml = '';

  // Group permissions by category
  var groupedPermissions = {};
  permissions.forEach(function(permission) {
    var parts = permission.name.split('-');
    var action = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    var category = parts.slice(1).join(' ');

    // Capitalize each word in category
    category = category.replace(/\b\w/g, l => l.toUpperCase());

    if (!groupedPermissions[category]) {
      groupedPermissions[category] = [];
    }

    groupedPermissions[category].push({
      name: permission.name,
      action: action,
      checked: rolePermissions.includes(permission.name)
    });
  });

  // Render grouped permissions in card layout
  Object.keys(groupedPermissions).forEach(function(category) {
    permissionsHtml += `
      <div class="card mb-3">
        <div class="card-header py-3">
          <h6 class="card-title mb-0 fw-semibold">${category}</h6>
        </div>
        <div class="card-body py-3">
          <div class="row g-3">`;

    groupedPermissions[category].forEach(function(permission) {
      var checked = permission.checked ? 'checked' : '';
      permissionsHtml += `
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
              <div class="form-check">
                <input class="form-check-input permission-checkbox" type="checkbox"
                       id="permission_${permission.name}"
                       name="permissions[]"
                       value="${permission.name}"
                       ${checked} />
                <label class="form-check-label text-capitalize" for="permission_${permission.name}">
                  ${permission.action}
                </label>
              </div>
            </div>`;
    });

    permissionsHtml += `
          </div>
        </div>
      </div>`;
  });

  $('#permissionsContainer').html(permissionsHtml);

  // Update select all checkbox
  updateSelectAllCheckbox();
}

// Function to update select all checkbox
function updateSelectAllCheckbox() {
  var totalCheckboxes = $('.permission-checkbox').length;
  var checkedCheckboxes = $('.permission-checkbox:checked').length;

  $('#selectAll').prop('checked', totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes);
}

// Function to reset role form
function resetRoleForm() {
  $('#roleId').val('');
  $('#modalRoleName').val('');
  loadPermissions();
}
