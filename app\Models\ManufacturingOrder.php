<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ManufacturingOrder extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_number',
        'order_type',
        'finished_product_id',
        'planned_quantity',
        'produced_quantity',
        'status',
        'planned_date',
        'started_at',
        'completed_at',
        'responsible_person',
        'notes',
        'estimated_cost',
        'actual_cost',
        'total_estimated_cost',
        'total_actual_cost',
        'estimated_time_minutes',
        'actual_time_minutes',
        'total_estimated_time_minutes',
        'total_actual_time_minutes',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'planned_quantity' => 'decimal:3',
        'produced_quantity' => 'decimal:3',
        'planned_date' => 'date',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'total_estimated_cost' => 'decimal:2',
        'total_actual_cost' => 'decimal:2',
        'estimated_time_minutes' => 'integer',
        'actual_time_minutes' => 'integer',
        'total_estimated_time_minutes' => 'integer',
        'total_actual_time_minutes' => 'integer',
    ];

    /**
     * The order statuses available.
     */
    const STATUSES = [
        'planned' => 'Planned',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
    ];

    /**
     * Get the finished product for this manufacturing order.
     */
    public function finishedProduct(): BelongsTo
    {
        return $this->belongsTo(FinishedProduct::class);
    }

    /**
     * Get the user who created this order.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the manufacturing consumptions for this order.
     */
    public function consumptions(): HasMany
    {
        return $this->hasMany(ManufacturingConsumption::class);
    }

    /**
     * Get the finished product movements related to this order.
     */
    public function finishedProductMovements(): HasMany
    {
        return $this->hasMany(FinishedProductMovement::class);
    }

    /**
     * Get the manufacturing order items for this order.
     */
    public function items(): HasMany
    {
        return $this->hasMany(ManufacturingOrderItem::class);
    }

    /**
     * Scope a query to only include planned orders.
     */
    public function scopePlanned($query)
    {
        return $query->where('status', 'planned');
    }

    /**
     * Scope a query to only include in progress orders.
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope a query to only include completed orders.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include cancelled orders.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('planned_date', [$startDate, $endDate]);
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * Get the status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'planned' => 'primary',
            'in_progress' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get the completion percentage.
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->isMultiProduct()) {
            $totalPlanned = $this->total_planned_quantity;
            if ($totalPlanned <= 0) {
                return 0;
            }
            return min(100, ($this->total_produced_quantity / $totalPlanned) * 100);
        }

        // Single product logic
        if (($this->planned_quantity ?? 0) <= 0) {
            return 0;
        }
        return min(100, (($this->produced_quantity ?? 0) / $this->planned_quantity) * 100);
    }

    /**
     * Get the remaining quantity to produce.
     */
    public function getRemainingQuantityAttribute(): float
    {
        return max(0, $this->total_planned_quantity - $this->total_produced_quantity);
    }

    /**
     * Check if the order is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status !== 'completed' &&
               $this->status !== 'cancelled' &&
               $this->planned_date < now()->toDateString();
    }

    /**
     * Check if the order can be started.
     */
    public function canStart(): bool
    {
        if ($this->status !== 'planned') {
            return false;
        }

        if ($this->isMultiProduct()) {
            // For multi-product orders, check if all items can be produced
            foreach ($this->items as $item) {
                if (!$item->finishedProduct->canProduce($item->planned_quantity)) {
                    return false;
                }
            }
            return true;
        } else {
            // For single product orders
            return $this->finishedProduct && $this->finishedProduct->canProduce($this->planned_quantity);
        }
    }

    /**
     * Get the reason why the order cannot be started.
     */
    public function getCannotStartReasonAttribute(): string
    {
        if ($this->status !== 'planned') {
            return 'Order is not in planned status';
        }

        if ($this->isMultiProduct()) {
            // For multi-product orders, check all items
            foreach ($this->items as $item) {
                $product = $item->finishedProduct;

                if ($product->recipes->isEmpty()) {
                    return "No recipe defined for product: {$product->name}";
                }

                foreach ($product->recipes as $recipe) {
                    $requiredQuantity = $recipe->quantity_required * $item->planned_quantity;
                    if ($recipe->rawMaterial->current_stock < $requiredQuantity) {
                        $shortage = $requiredQuantity - $recipe->rawMaterial->current_stock;
                        return "Insufficient {$recipe->rawMaterial->name} for {$product->name} (need {$requiredQuantity}, have {$recipe->rawMaterial->current_stock}, shortage: {$shortage})";
                    }
                }
            }
        } else {
            // For single product orders
            if (!$this->finishedProduct) {
                return 'No product specified for this order';
            }

            if ($this->finishedProduct->recipes->isEmpty()) {
                return 'No recipe defined for this product';
            }

            foreach ($this->finishedProduct->recipes as $recipe) {
                $requiredQuantity = $recipe->quantity_required * $this->planned_quantity;
                if ($recipe->rawMaterial->current_stock < $requiredQuantity) {
                    $shortage = $requiredQuantity - $recipe->rawMaterial->current_stock;
                    return "Insufficient {$recipe->rawMaterial->name} (need {$requiredQuantity}, have {$recipe->rawMaterial->current_stock}, shortage: {$shortage})";
                }
            }
        }

        return '';
    }

    /**
     * Check if the order can be completed.
     */
    public function canComplete(): bool
    {
        if ($this->status !== 'in_progress') {
            return false;
        }

        // Check if any consumption has been manually recorded
        $hasManualConsumption = $this->consumptions()
            ->where('actual_quantity', '>', 0)
            ->exists();

        // If manual consumption exists, can complete
        if ($hasManualConsumption) {
            return true;
        }

        // Check if sufficient materials are available for auto-consumption
        foreach ($this->consumptions as $consumption) {
            $material = $consumption->rawMaterial;
            if ($material->current_stock < $consumption->planned_quantity) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the reason why the order cannot be completed.
     */
    public function getCannotCompleteReasonAttribute(): ?string
    {
        if ($this->status !== 'in_progress') {
            return 'Order must be in progress to complete';
        }

        // Check if any consumption has been manually recorded
        $hasManualConsumption = $this->consumptions()
            ->where('actual_quantity', '>', 0)
            ->exists();

        // If manual consumption exists, can complete
        if ($hasManualConsumption) {
            return null;
        }

        // Check for insufficient materials
        $insufficientMaterials = [];
        foreach ($this->consumptions as $consumption) {
            $material = $consumption->rawMaterial;
            if ($material->current_stock < $consumption->planned_quantity) {
                $insufficientMaterials[] = "{$material->name} (need {$consumption->planned_quantity}, have {$material->current_stock})";
            }
        }

        if (!empty($insufficientMaterials)) {
            return 'Insufficient materials: ' . implode(', ', $insufficientMaterials);
        }

        return null;
    }

    /**
     * Check if this is a multi-product order.
     */
    public function isMultiProduct(): bool
    {
        return $this->order_type === 'multi';
    }

    /**
     * Get the total planned quantity for multi-product orders.
     */
    public function getTotalPlannedQuantityAttribute(): float
    {
        if ($this->isMultiProduct()) {
            return $this->items->sum('planned_quantity');
        }
        return $this->planned_quantity ?? 0;
    }

    /**
     * Get the total produced quantity for multi-product orders.
     */
    public function getTotalProducedQuantityAttribute(): float
    {
        if ($this->isMultiProduct()) {
            return $this->items->sum('produced_quantity');
        }
        return $this->produced_quantity ?? 0;
    }





    /**
     * Get the estimated production time in human readable format.
     */
    public function getEstimatedTimeFormattedAttribute(): string
    {
        if (!$this->estimated_time_minutes) {
            return 'N/A';
        }

        $hours = intval($this->estimated_time_minutes / 60);
        $minutes = $this->estimated_time_minutes % 60;

        if ($hours > 0) {
            return $minutes > 0 ? "{$hours}h {$minutes}m" : "{$hours}h";
        }

        return "{$minutes}m";
    }

    /**
     * Get the actual production time in human readable format.
     */
    public function getActualTimeFormattedAttribute(): string
    {
        if (!$this->actual_time_minutes) {
            return 'N/A';
        }

        $hours = intval($this->actual_time_minutes / 60);
        $minutes = $this->actual_time_minutes % 60;

        if ($hours > 0) {
            return $minutes > 0 ? "{$hours}h {$minutes}m" : "{$hours}h";
        }

        return "{$minutes}m";
    }

    /**
     * Boot the model to auto-generate order numbers.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (!$order->order_number) {
                $order->order_number = 'MO-' . date('Y') . '-' . str_pad(
                    static::whereYear('created_at', date('Y'))->count() + 1,
                    4,
                    '0',
                    STR_PAD_LEFT
                );
            }
        });
    }
}
