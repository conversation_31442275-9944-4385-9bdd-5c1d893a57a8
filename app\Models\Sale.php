<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Sale extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sale_number',
        'customer_name',
        'customer_phone',
        'subtotal',
        'discount_amount',
        'tax_amount',
        'total_amount',
        'amount_paid',
        'change_amount',
        'payment_method',
        'notes',
        'cashier_id',
        'sale_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'change_amount' => 'decimal:2',
        'sale_date' => 'datetime',
    ];

    /**
     * The payment methods available.
     */
    const PAYMENT_METHODS = [
        'cash' => 'Cash',
        'card' => 'Card',
        'transfer' => 'Bank Transfer',
    ];

    /**
     * Get the cashier who processed this sale.
     */
    public function cashier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    /**
     * Get the sale items for this sale.
     */
    public function items(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Get the payment method label.
     */
    public function getPaymentMethodLabelAttribute(): string
    {
        return self::PAYMENT_METHODS[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * Get the payment method color for UI.
     */
    public function getPaymentMethodColorAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'success',
            'card' => 'primary',
            'transfer' => 'info',
            default => 'secondary',
        };
    }

    /**
     * Get the total items count.
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Get the discount percentage.
     */
    public function getDiscountPercentageAttribute(): float
    {
        if ($this->subtotal <= 0) {
            return 0;
        }
        return ($this->discount_amount / $this->subtotal) * 100;
    }

    /**
     * Get the tax percentage.
     */
    public function getTaxPercentageAttribute(): float
    {
        if ($this->subtotal <= 0) {
            return 0;
        }
        return ($this->tax_amount / $this->subtotal) * 100;
    }

    /**
     * Check if the sale has a customer.
     */
    public function hasCustomer(): bool
    {
        return !empty($this->customer_name);
    }

    /**
     * Get formatted sale date.
     */
    public function getFormattedSaleDateAttribute(): string
    {
        return $this->sale_date->format('M d, Y H:i');
    }

    /**
     * Scope a query to only include sales from today.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('sale_date', today());
    }

    /**
     * Scope a query to only include sales from this week.
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('sale_date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Scope a query to only include sales from this month.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('sale_date', now()->month)
                    ->whereYear('sale_date', now()->year);
    }

    /**
     * Scope a query to filter by payment method.
     */
    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope a query to filter by cashier.
     */
    public function scopeByCashier($query, $cashierId)
    {
        return $query->where('cashier_id', $cashierId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('sale_date', [$startDate, $endDate]);
    }

    /**
     * Boot the model to auto-generate sale numbers.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($sale) {
            if (!$sale->sale_number) {
                $date = now()->format('Ymd');
                $lastSale = static::whereDate('created_at', now()->toDateString())
                    ->orderBy('id', 'desc')
                    ->first();
                
                $sequence = $lastSale ? (intval(substr($lastSale->sale_number, -4)) + 1) : 1;
                
                $sale->sale_number = 'POS-' . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
            }
        });
    }
}
