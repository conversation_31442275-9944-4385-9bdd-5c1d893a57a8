<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CurrencySetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CurrencySettingController extends Controller
{
    /**
     * Check if user has permission to manage settings
     */
    private function checkPermission()
    {
        // Super Admin has access to everything
        if (auth()->user()->hasRole('Super Admin')) {
            return true;
        }

        // Check for manage-settings permission for other users
        if (!auth()->user()->can('manage-settings')) {
            abort(403, 'Access denied. You do not have permission to manage settings.');
        }

        return true;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->checkPermission();

        $currencySettings = CurrencySetting::orderBy('is_active', 'desc')
                                         ->orderBy('created_at', 'desc')
                                         ->get();

        $commonCurrencies = CurrencySetting::getCommonCurrencies();

        return view('currency-settings.index', compact('currencySettings', 'commonCurrencies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->checkPermission();

        $commonCurrencies = CurrencySetting::getCommonCurrencies();
        return view('currency-settings.create', compact('commonCurrencies'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->checkPermission();

        $validator = Validator::make($request->all(), [
            'currency_name' => 'required|string|max:255',
            'currency_code' => 'required|string|size:3|unique:currency_settings,currency_code',
            'currency_symbol' => 'required|string|max:10',
            'currency_position' => 'required|in:prefix,suffix',
            'decimal_places' => 'required|integer|min:0|max:4',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        DB::beginTransaction();
        try {
            // If this currency is set as active, deactivate all others
            if ($request->boolean('is_active')) {
                CurrencySetting::where('is_active', true)->update(['is_active' => false]);
            }

            CurrencySetting::create($request->all());

            DB::commit();

            return redirect()->route('currency-settings.index')
                           ->with('success', 'Currency setting created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Error creating currency setting: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(CurrencySetting $currencySetting)
    {
        $this->checkPermission();

        return view('currency-settings.show', compact('currencySetting'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CurrencySetting $currencySetting)
    {
        $this->checkPermission();

        $commonCurrencies = CurrencySetting::getCommonCurrencies();
        return view('currency-settings.edit', compact('currencySetting', 'commonCurrencies'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CurrencySetting $currencySetting)
    {
        \Log::info('Currency update method called', [
            'currency_id' => $currencySetting->id,
            'request_data' => $request->all(),
            'user_id' => auth()->id()
        ]);

        $this->checkPermission();

        $validator = Validator::make($request->all(), [
            'currency_name' => 'required|string|max:255',
            'currency_code' => 'required|string|size:3|unique:currency_settings,currency_code,' . $currencySetting->id,
            'currency_symbol' => 'required|string|max:10',
            'currency_position' => 'required|in:prefix,suffix',
            'decimal_places' => 'required|integer|min:0|max:4',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        DB::beginTransaction();
        try {
            // If this currency is set as active, deactivate all others
            if ($request->boolean('is_active')) {
                CurrencySetting::where('id', '!=', $currencySetting->id)
                              ->where('is_active', true)
                              ->update(['is_active' => false]);
            }

            $currencySetting->update($request->all());

            DB::commit();

            return redirect()->route('currency-settings.index')
                           ->with('success', 'Currency setting updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Error updating currency setting: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CurrencySetting $currencySetting)
    {
        $this->checkPermission();

        try {
            // Prevent deletion of active currency
            if ($currencySetting->is_active) {
                return redirect()->back()
                               ->with('error', 'Cannot delete the active currency setting.');
            }

            $currencySetting->delete();

            return redirect()->route('currency-settings.index')
                           ->with('success', 'Currency setting deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Error deleting currency setting: ' . $e->getMessage());
        }
    }

    /**
     * Set a currency as active
     */
    public function setActive(CurrencySetting $currencySetting)
    {
        $this->checkPermission();

        DB::beginTransaction();
        try {
            // Deactivate all currencies
            CurrencySetting::where('is_active', true)->update(['is_active' => false]);

            // Activate the selected currency
            $currencySetting->update(['is_active' => true]);

            DB::commit();

            return redirect()->route('currency-settings.index')
                           ->with('success', 'Currency activated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Error activating currency: ' . $e->getMessage());
        }
    }

    /**
     * Get active currency for API/AJAX calls
     */
    public function getActiveCurrency()
    {
        $currency = CurrencySetting::getActiveCurrency();
        return response()->json($currency);
    }

    /**
     * Get currency list for select options
     */
    public function getCurrencyList()
    {
        $this->checkPermission();

        $currencies = CurrencySetting::getCommonCurrencies();
        return response()->json($currencies);
    }
}
