/*
 * Purchase Receiving Management
 */

'use strict';

$(function () {
  var receiveItemsModal = $('#receiveItemsModal');

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Receive Items
  $(document).on('click', '.receive-items', function() {
    var purchaseId = $(this).data('id');
    var purchaseNumber = $(this).data('number');

    $('#receive-purchase-id').val(purchaseId);
    $('#receive-purchase-number').text(purchaseNumber);
    $('#receive-items-container').empty();

    // Load purchase details with items
    $.get(`${baseUrl}purchases/${purchaseId}`, function(data) {
      if (data.items && data.items.length > 0) {
        data.items.forEach(function(item, index) {
          addReceiveItem(item, index);
        });
      }

      receiveItemsModal.modal('show');
    });
  });

  // Add receive item row
  function addReceiveItem(item, index) {
    var quantity = parseFloat(item.quantity || 0);
    var receivedQuantity = parseFloat(item.received_quantity || 0);
    var pendingQuantity = quantity - receivedQuantity;
    var completionPercentage = receivedQuantity > 0 ? (receivedQuantity / quantity * 100) : 0;

    var html = `
      <div class="receive-item border rounded p-3 mb-3">
        <input type="hidden" name="items[${index}][id]" value="${item.id}">

        <div class="row">
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text" class="form-control" value="${item.raw_material.name}" readonly>
              <label>Raw Material</label>
            </div>
            <small class="text-muted">Unit: ${item.raw_material.unit.symbol}</small>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control" value="${quantity.toFixed(3)}" readonly>
              <label>Ordered</label>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control" value="${receivedQuantity.toFixed(3)}" readonly>
              <label>Previously Received</label>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control received-quantity-input"
                     name="items[${index}][received_quantity]"
                     value="${pendingQuantity.toFixed(3)}"
                     max="${pendingQuantity.toFixed(3)}"
                     min="0"
                     data-pending="${pendingQuantity.toFixed(3)}"
                     required>
              <label>Receive Now</label>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-floating form-floating-outline">
              <input type="text" class="form-control completion-display" value="${completionPercentage.toFixed(1)}%" readonly>
              <label>Completion</label>
            </div>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-md-8">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control" name="items[${index}][notes]"
                        placeholder="Receiving notes..." rows="2">${item.notes || ''}</textarea>
              <label>Notes</label>
            </div>
          </div>

          <div class="col-md-4">
            <div class="d-flex flex-column">
              <small class="text-muted">Unit Price: ${window.formatCurrency ? window.formatCurrency(parseFloat(item.unit_price || 0)) : '$' + parseFloat(item.unit_price || 0).toFixed(2)}</small>
              <small class="text-muted">Line Total: ${window.formatCurrency ? window.formatCurrency(parseFloat(item.line_total || 0)) : '$' + parseFloat(item.line_total || 0).toFixed(2)}</small>
              <small class="text-muted">Pending: ${pendingQuantity.toFixed(3)} ${item.raw_material.unit.symbol}</small>
            </div>
          </div>
        </div>

        <div class="row mt-2">
          <div class="col-12">
            <div class="progress" style="height: 8px;">
              <div class="progress-bar bg-${completionPercentage >= 100 ? 'success' : completionPercentage >= 50 ? 'info' : 'warning'}"
                   style="width: ${completionPercentage}%"></div>
            </div>
          </div>
        </div>
      </div>
    `;

    $('#receive-items-container').append(html);
  }

  // Update completion when received quantity changes
  $(document).on('input', '.received-quantity-input', function() {
    var $item = $(this).closest('.receive-item');
    var receivedNow = parseFloat($(this).val()) || 0;
    var previouslyReceived = parseFloat($item.find('input[readonly]').eq(2).val()) || 0;
    var totalOrdered = parseFloat($item.find('input[readonly]').eq(1).val()) || 0;
    var pendingQuantity = parseFloat($(this).data('pending')) || 0;

    // Validate received quantity
    if (receivedNow > pendingQuantity) {
      $(this).val(pendingQuantity);
      receivedNow = pendingQuantity;
    }

    // Calculate new completion percentage
    var totalReceived = previouslyReceived + receivedNow;
    var completionPercentage = totalOrdered > 0 ? (totalReceived / totalOrdered * 100) : 0;

    // Update completion display
    $item.find('.completion-display').val(completionPercentage.toFixed(1) + '%');

    // Update progress bar
    var progressBar = $item.find('.progress-bar');
    progressBar.css('width', completionPercentage + '%');

    // Update progress bar color
    progressBar.removeClass('bg-warning bg-info bg-success');
    if (completionPercentage >= 100) {
      progressBar.addClass('bg-success');
    } else if (completionPercentage >= 50) {
      progressBar.addClass('bg-info');
    } else {
      progressBar.addClass('bg-warning');
    }
  });

  // Save received items
  $('#save-received-items').on('click', function() {
    var purchaseId = $('#receive-purchase-id').val();
    var items = [];

    $('.receive-item').each(function() {
      var $item = $(this);
      var itemData = {
        id: $item.find('input[name*="[id]"]').val(),
        received_quantity: $item.find('.received-quantity-input').val(),
        notes: $item.find('textarea').val()
      };

      items.push(itemData);
    });

    if (items.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'Warning!',
        text: 'No items to receive.',
        customClass: {
          confirmButton: 'btn btn-warning'
        }
      });
      return;
    }

    // Validate that at least one item has received quantity > 0
    var hasReceivedItems = items.some(function(item) {
      return parseFloat(item.received_quantity) > 0;
    });

    if (!hasReceivedItems) {
      Swal.fire({
        icon: 'warning',
        title: 'Warning!',
        text: 'Please specify received quantities for at least one item.',
        customClass: {
          confirmButton: 'btn btn-warning'
        }
      });
      return;
    }

    var requestData = {
      items: items,
      actual_delivery_date: $('#actual-delivery-date').val()
    };

    $.ajax({
      type: 'POST',
      url: `${baseUrl}purchases/${purchaseId}/receive`,
      data: requestData,
      success: function(response) {
        receiveItemsModal.modal('hide');

        // Refresh the datatable if it exists
        if (typeof dt_purchases !== 'undefined') {
          dt_purchases.draw();
        }

        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function(xhr) {
        var response = xhr.responseJSON;
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: response.message || 'Error receiving items',
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      }
    });
  });

  // View Details with purchase information
  $(document).on('click', '.view-details', function () {
    var purchase_id = $(this).data('id');

    $.get(`${baseUrl}purchases/${purchase_id}`, function (data) {
      // Build purchase details HTML
      var html = buildPurchaseDetailsHTML(data);
      $('#purchase-details-content').html(html);

      // Add action buttons based on purchase status
      var actionButtons = buildActionButtons(data);
      $('#purchase-actions').html(actionButtons);

      $('#purchaseDetailsModal').modal('show');
    }).fail(function() {
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: 'Failed to load purchase details',
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    });
  });

  // Build purchase details HTML
  function buildPurchaseDetailsHTML(purchase) {
    var html = `
      <div class="row">
        <div class="col-md-6">
          <h6 class="fw-medium">Purchase Information</h6>
          <table class="table table-borderless table-sm">
            <tr><td class="text-muted">Purchase Number:</td><td class="fw-medium">${purchase.purchase_number}</td></tr>
            <tr><td class="text-muted">Supplier:</td><td>${purchase.supplier_name}</td></tr>
            <tr><td class="text-muted">Contact:</td><td>${purchase.supplier_contact || 'N/A'}</td></tr>
            <tr><td class="text-muted">Email:</td><td>${purchase.supplier_email || 'N/A'}</td></tr>
            <tr><td class="text-muted">Purchase Date:</td><td>${purchase.formatted_purchase_date || purchase.purchase_date || 'N/A'}</td></tr>
            <tr><td class="text-muted">Expected Delivery:</td><td>${purchase.formatted_expected_delivery || purchase.expected_delivery_date || 'N/A'}</td></tr>
            <tr><td class="text-muted">Actual Delivery:</td><td>${purchase.formatted_actual_delivery || purchase.actual_delivery_date || 'N/A'}</td></tr>
            <tr><td class="text-muted">Status:</td><td><span class="badge bg-label-${purchase.status_color}">${purchase.status_label}</span></td></tr>
          </table>
        </div>
        <div class="col-md-6">
          <h6 class="fw-medium">Financial Summary</h6>
          <table class="table table-borderless table-sm">
            <tr><td class="text-muted">Subtotal:</td><td>${window.formatCurrency ? window.formatCurrency(purchase.subtotal) : '$' + parseFloat(purchase.subtotal || 0).toFixed(2)}</td></tr>
            <tr><td class="text-muted">Delivery Fee:</td><td>${window.formatCurrency ? window.formatCurrency(purchase.delivery_fee) : '$' + parseFloat(purchase.delivery_fee || 0).toFixed(2)}</td></tr>
            <tr><td class="text-muted">Handling Fee:</td><td>${window.formatCurrency ? window.formatCurrency(purchase.handling_fee) : '$' + parseFloat(purchase.handling_fee || 0).toFixed(2)}</td></tr>
            <tr><td class="text-muted">Other Fees:</td><td>${window.formatCurrency ? window.formatCurrency(purchase.other_fees) : '$' + parseFloat(purchase.other_fees || 0).toFixed(2)}</td></tr>
            <tr><td class="text-muted fw-bold">Total Amount:</td><td class="fw-bold">${window.formatCurrency ? window.formatCurrency(purchase.total_amount) : '$' + parseFloat(purchase.total_amount || 0).toFixed(2)}</td></tr>
            <tr><td class="text-muted">Created By:</td><td>${purchase.created_by ? purchase.created_by.name : 'N/A'}</td></tr>
            <tr><td class="text-muted">Received By:</td><td>${purchase.received_by ? purchase.received_by.name : 'N/A'}</td></tr>
          </table>
        </div>
      </div>
    `;

    if (purchase.supplier_address) {
      html += `
        <div class="row mt-4">
          <div class="col-12">
            <h6 class="fw-medium">Supplier Address</h6>
            <p class="text-muted">${purchase.supplier_address}</p>
          </div>
        </div>
      `;
    }

    if (purchase.items && purchase.items.length > 0) {
      html += `
        <div class="row mt-4">
          <div class="col-12">
            <h6 class="fw-medium">Purchase Items</h6>
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Material</th>
                    <th>Ordered</th>
                    <th>Received</th>
                    <th>Pending</th>
                    <th>Unit Price</th>
                    <th>Line Total</th>
                    <th>Progress</th>
                  </tr>
                </thead>
                <tbody>
      `;

      purchase.items.forEach(function(item) {
        var quantity = parseFloat(item.quantity || 0);
        var receivedQuantity = parseFloat(item.received_quantity || 0);
        var unitPrice = parseFloat(item.unit_price || 0);
        var lineTotal = parseFloat(item.line_total || 0);

        var pendingQuantity = quantity - receivedQuantity;
        var completionPercentage = quantity > 0 ? (receivedQuantity / quantity * 100) : 0;
        var progressColor = completionPercentage >= 100 ? 'success' : completionPercentage >= 50 ? 'info' : 'warning';

        html += `
          <tr>
            <td>${item.raw_material.name}</td>
            <td>${quantity.toFixed(3)} ${item.raw_material.unit.symbol}</td>
            <td>${receivedQuantity.toFixed(3)} ${item.raw_material.unit.symbol}</td>
            <td>${pendingQuantity.toFixed(3)} ${item.raw_material.unit.symbol}</td>
            <td>${window.formatCurrency ? window.formatCurrency(unitPrice) : '$' + unitPrice.toFixed(2)}</td>
            <td>${window.formatCurrency ? window.formatCurrency(lineTotal) : '$' + lineTotal.toFixed(2)}</td>
            <td>
              <div class="progress" style="height: 6px;">
                <div class="progress-bar bg-${progressColor}" style="width: ${completionPercentage}%"></div>
              </div>
              <small>${completionPercentage.toFixed(1)}%</small>
            </td>
          </tr>
        `;
      });

      html += `
                </tbody>
              </table>
            </div>
          </div>
        </div>
      `;
    }

    if (purchase.notes) {
      html += `
        <div class="row mt-4">
          <div class="col-12">
            <h6 class="fw-medium">Notes</h6>
            <p class="text-muted">${purchase.notes}</p>
          </div>
        </div>
      `;
    }

    return html;
  }

  // Build action buttons based on purchase status
  function buildActionButtons(purchase) {
    var buttons = '';

    if (purchase.status === 'pending') {
      buttons += `<button type="button" class="btn btn-info update-status" data-id="${purchase.id}" data-status="ordered">Mark as Ordered</button> `;
    }

    if (purchase.status === 'ordered') {
      buttons += `<button type="button" class="btn btn-primary update-status" data-id="${purchase.id}" data-status="shipped">Mark as Shipped</button> `;
    }

    if (purchase.can_receive) {
      buttons += `<button type="button" class="btn btn-success receive-items" data-id="${purchase.id}" data-number="${purchase.purchase_number}">Receive Items</button> `;
    }

    if (purchase.can_complete) {
      buttons += `<button type="button" class="btn btn-warning complete-purchase" data-id="${purchase.id}">Complete Purchase</button> `;
    }

    if (purchase.can_cancel) {
      buttons += `<button type="button" class="btn btn-outline-danger cancel-purchase" data-id="${purchase.id}">Cancel Purchase</button>`;
    }

    return buttons;
  }

  // Handle status updates
  $(document).on('click', '.update-status', function() {
    var purchaseId = $(this).data('id');
    var newStatus = $(this).data('status');
    var statusText = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);

    Swal.fire({
      title: `Mark as ${statusText}?`,
      text: `Are you sure you want to mark this purchase as ${statusText.toLowerCase()}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: `Yes, mark as ${statusText.toLowerCase()}`,
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-outline-secondary'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: `${baseUrl}purchases/${purchaseId}/status`,
          method: 'POST',
          data: {
            status: newStatus,
            _token: $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });

            // Refresh the table and close modal
            if (typeof dt_purchases !== 'undefined') {
              dt_purchases.ajax.reload();
            }
            $('#purchaseDetailsModal').modal('hide');
          },
          error: function(xhr) {
            var errorMessage = xhr.responseJSON?.message || 'Error updating purchase status';
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: errorMessage,
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Handle complete purchase
  $(document).on('click', '.complete-purchase', function() {
    var purchaseId = $(this).data('id');

    Swal.fire({
      title: 'Complete Purchase?',
      text: 'Are you sure you want to mark this purchase as completed?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, complete it',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-warning',
        cancelButton: 'btn btn-outline-secondary'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: `${baseUrl}purchases/${purchaseId}/complete`,
          method: 'POST',
          data: {
            _token: $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });

            // Refresh the table and close modal
            if (typeof dt_purchases !== 'undefined') {
              dt_purchases.ajax.reload();
            }
            $('#purchaseDetailsModal').modal('hide');
          },
          error: function(xhr) {
            var errorMessage = xhr.responseJSON?.message || 'Error completing purchase';
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: errorMessage,
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Handle cancel purchase
  $(document).on('click', '.cancel-purchase', function() {
    var purchaseId = $(this).data('id');

    Swal.fire({
      title: 'Cancel Purchase?',
      text: 'Are you sure you want to cancel this purchase? This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, cancel it',
      cancelButtonText: 'No, keep it',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-outline-secondary'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: `${baseUrl}purchases/${purchaseId}/cancel`,
          method: 'POST',
          data: {
            _token: $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            Swal.fire({
              icon: 'success',
              title: 'Cancelled!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });

            // Refresh the table and close modal
            if (typeof dt_purchases !== 'undefined') {
              dt_purchases.ajax.reload();
            }
            $('#purchaseDetailsModal').modal('hide');
          },
          error: function(xhr) {
            var errorMessage = xhr.responseJSON?.message || 'Error cancelling purchase';
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: errorMessage,
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });
});
