<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
  /**
   * Seed the application's database.
   */
  public function run(): void
  {
    // Create a test user first
    User::firstOrCreate([
      'email' => '<EMAIL>',
    ], [
      'name' => 'Test User',
      'password' => bcrypt('password'),
      'email_verified_at' => now(),
    ]);

    // Seed permissions and roles first
    $this->call([
      PermissionsSeeder::class,
    ]);

    // Seed all data in proper order (dependencies first)
    $this->call([
      UnitCategoriesSeeder::class,
      UnitsSeeder::class,
      RawMaterialsSeeder::class,
      FinishedProductsSeeder::class,
      RecipesSeeder::class,
      PurchasesSeeder::class,
      ManufacturingOrdersSeeder::class,
      SalesSeeder::class,
      FinishedProductMovementsSeeder::class,
    ]);

    // Assign roles to users after creating business data
    $this->call([
      RoleUserSeeder::class,
    ]);

    // Create additional test users
    User::factory(5)->create();
  }
}
