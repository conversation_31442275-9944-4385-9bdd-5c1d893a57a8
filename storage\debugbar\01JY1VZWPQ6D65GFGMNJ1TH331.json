{"__meta": {"id": "01JY1VZWPQ6D65GFGMNJ1TH331", "datetime": "2025-06-18 15:54:44", "utime": **********.312356, "method": "GET", "uri": "/manufacturing-orders", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.225903, "end": **********.312432, "duration": 1.086529016494751, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": **********.225903, "relative_start": 0, "end": **********.501525, "relative_end": **********.501525, "duration": 0.****************, "duration_str": "276ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.501575, "relative_start": 0.*****************, "end": **********.31244, "relative_end": 7.867813110351562e-06, "duration": 0.****************, "duration_str": "811ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.525444, "relative_start": 0.*****************, "end": **********.536173, "relative_end": **********.536173, "duration": 0.010729074478149414, "duration_str": "10.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.720445, "relative_start": 0.*****************, "end": **********.309662, "relative_end": **********.309662, "duration": 0.****************, "duration_str": "589ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: pages.manufacturing-orders.index", "start": **********.880478, "relative_start": 0.****************, "end": **********.880478, "relative_end": **********.880478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.layoutMaster", "start": **********.909767, "relative_start": 0.****************, "end": **********.909767, "relative_end": **********.909767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.contentNavbarLayout", "start": **********.912247, "relative_start": 0.6863439083099365, "end": **********.912247, "relative_end": **********.912247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenuData", "start": **********.001007, "relative_start": 0.7751040458679199, "end": **********.001007, "relative_end": **********.001007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenu", "start": **********.09588, "relative_start": 0.8699769973754883, "end": **********.09588, "relative_end": **********.09588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: _partials.macros", "start": **********.228166, "relative_start": 1.002263069152832, "end": **********.228166, "relative_end": **********.228166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.submenu", "start": **********.246504, "relative_start": 1.0206010341644287, "end": **********.246504, "relative_end": **********.246504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.navbar.navbar", "start": **********.248788, "relative_start": 1.0228850841522217, "end": **********.248788, "relative_end": **********.248788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.footer.footer", "start": **********.258896, "relative_start": 1.0329930782318115, "end": **********.258896, "relative_end": **********.258896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.commonMaster", "start": **********.261826, "relative_start": 1.0359230041503906, "end": **********.261826, "relative_end": **********.261826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.styles", "start": **********.264688, "relative_start": 1.0387849807739258, "end": **********.264688, "relative_end": **********.264688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scriptsIncludes", "start": **********.274997, "relative_start": 1.0490939617156982, "end": **********.274997, "relative_end": **********.274997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scripts", "start": **********.284225, "relative_start": 1.0583219528198242, "end": **********.284225, "relative_end": **********.284225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 4590928, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "pages.manufacturing-orders.index", "param_count": null, "params": [], "start": **********.880107, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/pages/manufacturing-orders/index.blade.phppages.manufacturing-orders.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Fpages%2Fmanufacturing-orders%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "layouts.layoutMaster", "param_count": null, "params": [], "start": **********.909266, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/layoutMaster.blade.phplayouts.layoutMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FlayoutMaster.blade.php&line=1", "ajax": false, "filename": "layoutMaster.blade.php", "line": "?"}}, {"name": "layouts.contentNavbarLayout", "param_count": null, "params": [], "start": **********.912048, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/contentNavbarLayout.blade.phplayouts.contentNavbarLayout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcontentNavbarLayout.blade.php&line=1", "ajax": false, "filename": "contentNavbarLayout.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenuData", "param_count": null, "params": [], "start": **********.000754, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenuData.blade.phplayouts.sections.menu.verticalMenuData", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenuData.blade.php&line=1", "ajax": false, "filename": "verticalMenuData.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenu", "param_count": null, "params": [], "start": **********.095659, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenu.blade.phplayouts.sections.menu.verticalMenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenu.blade.php&line=1", "ajax": false, "filename": "verticalMenu.blade.php", "line": "?"}}, {"name": "_partials.macros", "param_count": null, "params": [], "start": **********.227962, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/_partials/macros.blade.php_partials.macros", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2F_partials%2Fmacros.blade.php&line=1", "ajax": false, "filename": "macros.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.submenu", "param_count": null, "params": [], "start": **********.246304, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/submenu.blade.phplayouts.sections.menu.submenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2Fsubmenu.blade.php&line=1", "ajax": false, "filename": "submenu.blade.php", "line": "?"}}, {"name": "layouts.sections.navbar.navbar", "param_count": null, "params": [], "start": **********.2486, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/navbar/navbar.blade.phplayouts.sections.navbar.navbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fnavbar%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}}, {"name": "layouts.sections.footer.footer", "param_count": null, "params": [], "start": **********.258705, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/footer/footer.blade.phplayouts.sections.footer.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Ffooter%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "layouts.commonMaster", "param_count": null, "params": [], "start": **********.261631, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/commonMaster.blade.phplayouts.commonMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcommonMaster.blade.php&line=1", "ajax": false, "filename": "commonMaster.blade.php", "line": "?"}}, {"name": "layouts.sections.styles", "param_count": null, "params": [], "start": **********.264492, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/styles.blade.phplayouts.sections.styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "layouts.sections.scriptsIncludes", "param_count": null, "params": [], "start": **********.274772, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scriptsIncludes.blade.phplayouts.sections.scriptsIncludes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2FscriptsIncludes.blade.php&line=1", "ajax": false, "filename": "scriptsIncludes.blade.php", "line": "?"}}, {"name": "layouts.sections.scripts", "param_count": null, "params": [], "start": **********.284039, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scripts.blade.phplayouts.sections.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026730000000000004, "accumulated_duration_str": "26.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `manu_sessions` where `id` = '3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr' limit 1", "type": "query", "params": [], "bindings": ["3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.585655, "duration": 0.01071, "duration_str": "10.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "manufacturing", "explain": null, "start_percent": 0, "width_percent": 40.067}, {"sql": "select * from `manu_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.617156, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "manufacturing", "explain": null, "start_percent": 40.067, "width_percent": 7.557}, {"sql": "select * from `manu_manufacturing_orders` where `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.6361349, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:24", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=24", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 47.624, "width_percent": 5.35}, {"sql": "select * from `manu_finished_products` where `manu_finished_products`.`id` in (2, 4, 5, 6) and `manu_finished_products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.652113, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=24", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 52.974, "width_percent": 3.816}, {"sql": "select * from `manu_users` where `manu_users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.667012, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=24", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 56.79, "width_percent": 7.669}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.68485, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:26", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=26", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "26"}, "connection": "manufacturing", "explain": null, "start_percent": 64.459, "width_percent": 4.676}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'in_progress' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.696591, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=27", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "27"}, "connection": "manufacturing", "explain": null, "start_percent": 69.136, "width_percent": 3.479}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.707834, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:28", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=28", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "28"}, "connection": "manufacturing", "explain": null, "start_percent": 72.615, "width_percent": 3.143}, {"sql": "select * from `manu_cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.730635, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 75.758, "width_percent": 3.966}, {"sql": "select `manu_permissions`.*, `manu_model_has_permissions`.`model_id` as `pivot_model_id`, `manu_model_has_permissions`.`permission_id` as `pivot_permission_id`, `manu_model_has_permissions`.`model_type` as `pivot_model_type` from `manu_permissions` inner join `manu_model_has_permissions` on `manu_permissions`.`id` = `manu_model_has_permissions`.`permission_id` where `manu_model_has_permissions`.`model_id` in (1) and `manu_model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.77131, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "manufacturing", "explain": null, "start_percent": 79.723, "width_percent": 10.438}, {"sql": "select `manu_roles`.*, `manu_model_has_roles`.`model_id` as `pivot_model_id`, `manu_model_has_roles`.`role_id` as `pivot_role_id`, `manu_model_has_roles`.`model_type` as `pivot_model_type` from `manu_roles` inner join `manu_model_has_roles` on `manu_roles`.`id` = `manu_model_has_roles`.`role_id` where `manu_model_has_roles`.`model_id` in (1) and `manu_model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.787914, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "manufacturing", "explain": null, "start_percent": 90.161, "width_percent": 6.285}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "pages.manufacturing-orders.index", "file": "C:\\wamp64\\www\\recipe\\resources\\views/pages/manufacturing-orders/index.blade.php", "line": 233}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.900406, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 96.446, "width_percent": 3.554}]}, "models": {"data": {"App\\Models\\ManufacturingOrder": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrder.php&line=1", "ajax": false, "filename": "ManufacturingOrder.php", "line": "?"}}, "App\\Models\\FinishedProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FFinishedProduct.php&line=1", "ajax": false, "filename": "FinishedProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 38, "messages": [{"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1913115548 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913115548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.800885, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-79978164 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79978164\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.808138, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-566188149 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566188149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.815632, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-825802820 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825802820\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.822357, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1346125228 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346125228\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.832499, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1477486229 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477486229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.840526, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627411627 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627411627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.846456, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1509459580 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509459580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.859888, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-685258031 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685258031\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879246, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-546982885 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546982885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.921681, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-861264642 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861264642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.930981, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-654138230 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654138230\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.936615, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-100041365 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100041365\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.946071, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-892743558 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892743558\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.955856, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1083931529 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083931529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.967127, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1127170029 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1127170029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.97287, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1208558179 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208558179\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.980174, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-940083658 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940083658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.999886, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1262921106 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262921106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009656, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1777425145 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777425145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.015817, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1842018086 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842018086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025429, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2116930666 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116930666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.034804, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2080179411 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080179411\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.047443, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1844891534 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844891534\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.06248, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2064541312 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064541312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.069957, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1043876398 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043876398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076681, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-436360431 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436360431\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.094777, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1496429320 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496429320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100365, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-3966065 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3966065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106164, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1240679856 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240679856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110752, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1290551537 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290551537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116957, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-510955598 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510955598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123318, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1703765657 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703765657\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.129653, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1730775353 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730775353\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.133948, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1237575800 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237575800\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137857, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-546223617 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546223617\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.150562, "xdebug_link": null}, {"message": "[\n  ability => manage-settings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-855014063 data-indent-pad=\"  \"><span class=sf-dump-note>manage-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855014063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.16531, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-602707491 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602707491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.168662, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/manufacturing-orders", "action_name": "manufacturing-orders.index", "controller_action": "App\\Http\\Controllers\\ManufacturingOrderController@index", "uri": "GET manufacturing-orders", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@index<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/manufacturing-orders", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ManufacturingOrderController.php:22-38</a>", "middleware": "web, auth", "duration": "1.12s", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-153846905 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-153846905\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1442793364 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1442793364\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1041421305 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/manufacturing-orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1583 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IlBpdVovc0VMVlZocFJEOHBvem1EbkE9PSIsInZhbHVlIjoidXdWSWlWSmtNQWxzaGFQeEYzRmpOOExSTXZNUnRkcWtjRmhROEVqMm1XSmlPUnNHYVJPaG5ERGhMWk1WV3VwdEk2T0U0ell1ZThuamRyVXZsNlF0eWRUaEJaVVhhNi8xTUFDZFQ4OUtHcjBkemRxbUtCUjF5S01TM25jdlJCKzkiLCJtYWMiOiJkMDE2YjhiZTBiYjM1ZmVjZGE3MTgxNGUyNmEyNmI3ZGEyMjBmNGE1NDRhZjhlYTQ2MTQzODljMWI2OGU0Y2NjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkhxOWhwMFYyTyszL2tSRkx2QjVKbXc9PSIsInZhbHVlIjoiMFhjY2JPamxWTklnVDBnQ3EvOGlIcFBkV0dkK08vWEJrQ0w2aWdjanllMGhoVEdNNnVseWJ1dUloQjRnYnc5QXNHaHpHYlhvUyt0d3BFdVdjc3k2Z1lQN3RyekduT2lRd0lCN0dEUUZmaVRRUTZQcXFQMFNiditUKzRzV1NNckgiLCJtYWMiOiI0ZmJkYzYyNzcyZTZiZDc3YmM3OGM2NGE1MWM5YWJiYjViYTMyNmMwNDcyNTFiOGE2MDA5YmFhNmY4ZDQxM2UyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041421305\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-633032621 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633032621\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1895267456 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 15:54:43 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895267456\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1359335933 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/translations/fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359335933\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/manufacturing-orders", "action_name": "manufacturing-orders.index", "controller_action": "App\\Http\\Controllers\\ManufacturingOrderController@index"}, "badge": null}}