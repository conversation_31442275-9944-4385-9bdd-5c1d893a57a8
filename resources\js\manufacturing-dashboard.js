/*
 * Manufacturing Dashboard Analytics
 */

'use strict';

// Global variables for charts
let salesChart, productionChart, inventoryChart;

(function () {
  let cardColor, headingColor, labelColor, borderColor, legendColor, shadeColor;

  if (isDarkStyle) {
    cardColor = config.colors_dark.cardColor;
    headingColor = config.colors_dark.headingColor;
    labelColor = config.colors_dark.textMuted;
    legendColor = config.colors_dark.bodyColor;
    borderColor = config.colors_dark.borderColor;
    shadeColor = 'dark';
  } else {
    cardColor = config.colors.cardColor;
    headingColor = config.colors.headingColor;
    labelColor = config.colors.textMuted;
    legendColor = config.colors.bodyColor;
    borderColor = config.colors.borderColor;
    shadeColor = '';
  }

  // Sales Chart
  // --------------------------------------------------------------------
  const salesChartEl = document.querySelector('#salesChart'),
    salesChartConfig = {
      chart: {
        height: 300,
        type: 'area',
        parentHeightOffset: 0,
        toolbar: {
          show: false
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: false,
        curve: 'straight'
      },
      legend: {
        show: true,
        position: 'top',
        horizontalAlign: 'start',
        labels: {
          colors: legendColor,
          useSeriesColors: false
        }
      },
      grid: {
        borderColor: borderColor,
        xaxis: {
          lines: {
            show: true
          }
        }
      },
      colors: [config.colors.primary, config.colors.info],
      series: [
        {
          name: 'Sales',
          data: [0, 0, 0, 0, 0, 0, 0] // Will be populated by PHP data
        },
        {
          name: 'Orders',
          data: [0, 0, 0, 0, 0, 0, 0] // Will be populated by PHP data
        }
      ],
      xaxis: {
        categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        labels: {
          style: {
            colors: labelColor,
            fontSize: '13px'
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: labelColor,
            fontSize: '13px'
          }
        }
      },
      fill: {
        opacity: 1,
        type: 'solid'
      },
      tooltip: {
        shared: false,
        y: {
          formatter: function (val, opts) {
            if (opts.seriesIndex === 0) {
              // Sales data - format as currency
              return window.formatCurrency ? window.formatCurrency(val) : '$' + val.toFixed(2);
            } else {
              // Orders data - format as number
              return val + ' orders';
            }
          }
        }
      }
    };
  if (typeof salesChartEl !== undefined && salesChartEl !== null) {
    salesChart = new ApexCharts(salesChartEl, salesChartConfig);
    salesChart.render();
  }

  // Production Chart
  // --------------------------------------------------------------------
  const productionChartEl = document.querySelector('#productionChart'),
    productionChartConfig = {
      chart: {
        height: 300,
        type: 'bar',
        parentHeightOffset: 0,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '50%',
          endingShape: 'rounded',
          borderRadius: 8
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      legend: {
        show: true,
        position: 'top',
        horizontalAlign: 'start',
        labels: {
          colors: legendColor,
          useSeriesColors: false
        }
      },
      grid: {
        borderColor: borderColor,
        xaxis: {
          lines: {
            show: true
          }
        }
      },
      colors: [config.colors.success, config.colors.warning],
      series: [
        {
          name: 'Completed',
          data: [0, 0, 0, 0, 0, 0, 0] // Will be populated by PHP data
        },
        {
          name: 'Planned',
          data: [0, 0, 0, 0, 0, 0, 0] // Will be populated by PHP data
        }
      ],
      xaxis: {
        categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        labels: {
          style: {
            colors: labelColor,
            fontSize: '13px'
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: labelColor,
            fontSize: '13px'
          }
        }
      },
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + ' orders';
          }
        }
      }
    };
  if (typeof productionChartEl !== undefined && productionChartEl !== null) {
    productionChart = new ApexCharts(productionChartEl, productionChartConfig);
    productionChart.render();
  }

  // Inventory Status Chart
  // --------------------------------------------------------------------
  const inventoryChartEl = document.querySelector('#inventoryChart'),
    inventoryChartConfig = {
      chart: {
        height: 300,
        type: 'donut',
        parentHeightOffset: 0
      },
      labels: ['Good Stock', 'Low Stock', 'Out of Stock'],
      series: [70, 25, 5], // Will be populated by PHP data
      colors: [config.colors.success, config.colors.warning, config.colors.danger],
      stroke: {
        show: false,
        curve: 'straight'
      },
      dataLabels: {
        enabled: true,
        formatter: function (val, opt) {
          return parseInt(val) + '%';
        }
      },
      legend: {
        show: true,
        position: 'bottom',
        labels: {
          colors: legendColor,
          useSeriesColors: false
        }
      },
      plotOptions: {
        pie: {
          donut: {
            labels: {
              show: true,
              name: {
                fontSize: '2rem',
                fontFamily: 'Public Sans'
              },
              value: {
                fontSize: '1.2rem',
                color: legendColor,
                fontFamily: 'Public Sans',
                formatter: function (val) {
                  return parseInt(val) + '%';
                }
              },
              total: {
                show: true,
                fontSize: '1.5rem',
                color: headingColor,
                label: 'Inventory',
                formatter: function (w) {
                  return '100%';
                }
              }
            }
          }
        }
      },
      responsive: [
        {
          breakpoint: 992,
          options: {
            chart: {
              height: 380
            },
            legend: {
              position: 'bottom'
            }
          }
        },
        {
          breakpoint: 576,
          options: {
            chart: {
              height: 320
            },
            plotOptions: {
              pie: {
                donut: {
                  labels: {
                    show: true,
                    name: {
                      fontSize: '1.5rem'
                    },
                    value: {
                      fontSize: '1rem'
                    },
                    total: {
                      fontSize: '1.5rem'
                    }
                  }
                }
              }
            },
            legend: {
              position: 'bottom'
            }
          }
        }
      ]
    };
  if (typeof inventoryChartEl !== undefined && inventoryChartEl !== null) {
    inventoryChart = new ApexCharts(inventoryChartEl, inventoryChartConfig);
    inventoryChart.render();
  }

})();

// Update charts with real data when available
document.addEventListener('DOMContentLoaded', function() {
  // Wait a bit for charts to be fully initialized
  setTimeout(function() {
    if (typeof window.dashboardData !== 'undefined') {
      // Update sales chart
      if (window.dashboardData.salesChartData && salesChart) {
        const salesData = window.dashboardData.salesChartData;
        salesChart.updateSeries([
          {
            name: 'Sales',
            data: salesData.map(item => parseFloat(item.sales) || 0)
          },
          {
            name: 'Orders',
            data: salesData.map(item => parseInt(item.orders) || 0)
          }
        ]);
        salesChart.updateOptions({
          xaxis: {
            categories: salesData.map(item => item.date)
          }
        });
      }

      // Update production chart
      if (window.dashboardData.productionChartData && productionChart) {
        const productionData = window.dashboardData.productionChartData;
        productionChart.updateSeries([
          {
            name: 'Completed',
            data: productionData.map(item => parseInt(item.completed) || 0)
          },
          {
            name: 'Planned',
            data: productionData.map(item => parseInt(item.planned) || 0)
          }
        ]);
        productionChart.updateOptions({
          xaxis: {
            categories: productionData.map(item => item.date)
          }
        });
      }

      // Update inventory chart
      if (window.dashboardData.inventoryChartData && inventoryChart) {
        const inventoryData = window.dashboardData.inventoryChartData;
        inventoryChart.updateSeries(inventoryData);
      }
    }
  }, 100); // Small delay to ensure charts are initialized
});
