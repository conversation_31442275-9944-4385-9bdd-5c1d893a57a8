@php
$configData = Helper::appClasses();
@endphp

@use('Illuminate\Support\Facades\Auth')

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ $configData['style'] }}-style {{ $configData['styleOpt'] }}-style-option {{ $configData['rtlSupport'] ? 'rtl' : '' }}" dir="{{ $configData['textDirection'] }}" data-theme="{{ $configData['theme'] }}" data-assets-path="{{ asset('/assets') . '/' }}" data-base-url="{{url('/')}}" data-framework="laravel"  >

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <title>@yield('title') | {{ config('variables.templateName') }}</title>
  <meta name="description" content="{{ config('variables.templateDescription') ? config('variables.templateDescription') : '' }}" />
  <meta name="keywords" content="{{ config('variables.templateKeyword') ? config('variables.templateKeyword') : '' }}" />
  <!-- laravel CRUD token -->
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <!-- Canonical SEO -->
  <link rel="canonical" href="{{ config('variables.productPage') ? config('variables.productPage') : '' }}">
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="{{ asset('assets/img/favicon/favicon.ico') }}" />

  <!-- Include Styles -->
  @include('layouts/sections/styles')

  <!-- Image Upload Styles -->
  <link rel="stylesheet" href="{{ asset('assets/css/image-upload.css') }}">

  <!-- Include Scripts for customizer, helper, analytics, config -->
  <!-- POS Layout: Minimal script includes to avoid template customizer conflicts -->
  @vite(['resources/assets/vendor/js/helpers.js'])
  @vite(['resources/assets/js/config.js'])

  <!-- POS Specific Styles -->
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Inter', sans-serif;
    }

    .pos-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem 0;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .pos-container {
      min-height: calc(100vh - 80px);
      padding: 1rem;
    }

    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
      max-height: calc(100vh - 200px);
      overflow-y: auto;
    }

    .product-card {
      background: white;
      border-radius: 12px;
      padding: 1rem;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      cursor: pointer;
      border: 2px solid transparent;
    }

    .product-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.15);
      border-color: #667eea;
    }

    .cart-sidebar {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 16px rgba(0,0,0,0.1);
      height: calc(100vh - 120px);
      display: flex;
      flex-direction: column;
    }

    .cart-items {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
      min-height: 200px;
      max-height: 400px;
    }

    .cart-footer {
      border-top: 1px solid #e9ecef;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 0 0 12px 12px;
    }

    .search-box {
      background: white;
      border-radius: 50px;
      padding: 0.5rem 1rem;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border: none;
      width: 100%;
      max-width: 400px;
    }

    .search-box:focus {
      outline: none;
      box-shadow: 0 2px 16px rgba(102, 126, 234, 0.3);
    }

    .btn-pos {
      border-radius: 8px;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .btn-pos:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .cart-item {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 0.75rem;
      margin-bottom: 0.5rem;
      border-left: 4px solid #667eea;
      display: block;
      visibility: visible;
      opacity: 1;
      position: relative;
    }

    .quantity-controls {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .quantity-btn {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: none;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .quantity-btn:hover:not(:disabled):not(.disabled) {
      background: #5a6fd8;
      transform: scale(1.1);
    }

    .quantity-btn:disabled,
    .quantity-btn.disabled {
      background: #ccc !important;
      color: #999 !important;
      cursor: not-allowed !important;
      transform: none !important;
      opacity: 0.6 !important;
    }

    .quantity-input {
      width: 60px;
      text-align: center;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 0.25rem;
    }

    @media (max-width: 768px) {
      .pos-container {
        padding: 0.5rem;
      }

      .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
      }

      .cart-sidebar {
        height: auto;
        margin-top: 1rem;
      }
    }
  </style>
</head>

<body>
  <!-- POS Header -->
  <div class="pos-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-3">
          <h4 class="mb-0">
            <i class="ri-store-2-line me-2"></i>
            Point of Sale
          </h4>
        </div>
        <div class="col-md-6 text-center">
          <input type="text" class="search-box" id="product-search" placeholder="Search products...">
        </div>
        <div class="col-md-3 text-end">
          <div class="d-flex align-items-center justify-content-end gap-3">
            <span class="text-white-50">Cashier:</span>
            <span class="fw-bold">{{ Auth::user()->name }}</span>
            <a href="{{ url('/') }}" class="btn btn-outline-light btn-sm">
              <i class="ri-home-line me-1"></i>
              Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- POS Content -->
  <div class="pos-container">
    @yield('content')
  </div>

  <!-- Include Scripts -->
  <!-- POS Layout: Custom script includes without main.js to avoid conflicts -->
  @vite([
    'resources/assets/vendor/libs/jquery/jquery.js',
    'resources/assets/vendor/libs/popper/popper.js',
    'resources/assets/vendor/js/bootstrap.js',
    'resources/assets/vendor/libs/node-waves/node-waves.js',
    'resources/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js',
    'resources/assets/vendor/libs/hammer/hammer.js',
    'resources/assets/vendor/libs/typeahead-js/typeahead.js',
    'resources/assets/vendor/js/menu.js'
  ])

  @yield('vendor-script')
  <!-- END: Page Vendor JS-->

  <!-- POS specific initialization -->
  <script>
    // Initialize basic functionality without main.js conflicts
    if (typeof Waves !== 'undefined') {
      Waves.init();
      Waves.attach(".btn[class*='btn-']:not(.position-relative):not([class*='btn-outline-']):not([class*='btn-label-'])", ['waves-light']);
      Waves.attach("[class*='btn-outline-']:not(.position-relative)");
      Waves.attach("[class*='btn-label-']:not(.position-relative)");
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  </script>

  @yield('page-script')

  <!-- POS Base URL for JavaScript -->
  <script>
    window.baseUrl = "{{ url('/') }}/";
    window.posBaseUrl = "{{ url('/pos') }}/";
    window.assetsPath = "{{ asset('/assets') }}/";
    window.templateName = "{{ config('variables.templateName') }}";

    // Define required global variables for template functionality
    window.headingColor = '#3b4055';
    window.borderColor = '#e5e5e8';
    window.bodyBg = '#f7f7f9';

    // Prevent template customizer errors in POS layout
    window.templateCustomizer = null;
  </script>
</body>
</html>
