<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('finished_product_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('finished_product_id')->constrained('finished_products')->onDelete('cascade');
            $table->enum('movement_type', ['inbound', 'outbound', 'adjustment', 'production']);
            $table->decimal('quantity', 10, 3); // Positive for inbound/production, negative for outbound
            $table->decimal('unit_cost', 10, 2)->nullable(); // Cost per unit (for production movements)
            $table->decimal('total_value', 12, 2)->nullable(); // Calculated: quantity * unit_cost
            $table->string('document_reference')->nullable();
            $table->text('reason')->nullable();
            $table->text('notes')->nullable();
            $table->decimal('stock_before', 10, 3); // Stock level before this movement
            $table->decimal('stock_after', 10, 3); // Stock level after this movement
            $table->unsignedBigInteger('manufacturing_order_id')->nullable(); // Will add foreign key constraint later
            $table->foreignId('user_id')->constrained('users')->onDelete('restrict'); // Who made the movement
            $table->timestamp('movement_date')->useCurrent();
            $table->timestamps();

            // Indexes for performance with custom short names
            $table->index(['finished_product_id', 'movement_date'], 'fp_movements_product_date_idx');
            $table->index('movement_type', 'fp_movements_type_idx');
            $table->index('manufacturing_order_id', 'fp_movements_order_idx');
            $table->index('user_id', 'fp_movements_user_idx');
            $table->index('movement_date', 'fp_movements_date_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('finished_product_movements');
    }
};
