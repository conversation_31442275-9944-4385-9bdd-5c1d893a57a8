/**
 * Currency Settings Management
 */

'use strict';

$(function () {
  console.log('Currency settings JS loaded');
  let currencyTable;

  // Initialize DataTable
  if ($('#currencyTable').length) {
    currencyTable = $('#currencyTable').DataTable({
      responsive: true,
      order: [[5, 'desc']], // Order by status (active first)
      columnDefs: [
        {
          targets: -1, // Actions column
          orderable: false,
          searchable: false
        }
      ],
      language: {
        search: '',
        searchPlaceholder: 'Search currencies...'
      }
    });
  }

  // Currency selection handler for add modal
  $('#add_currency_list').on('change', function() {
    const selectedOption = $(this).find('option:selected');
    const currencyName = selectedOption.data('name');
    const currencySymbol = selectedOption.data('symbol');
    const currencyCode = selectedOption.val();

    if (currencyCode) {
      $('#add_currency_name').val(currencyName);
      $('#add_currency_code').val(currencyCode);
      $('#add_currency_symbol').val(currencySymbol);
    }
  });

  // Edit currency button handler - using event delegation
  $(document).on('click', '.edit-currency', function() {
    const currencyId = $(this).data('id');
    const currencyName = $(this).data('name');
    const currencyCode = $(this).data('code');
    const currencySymbol = $(this).data('symbol');
    const currencyPosition = $(this).data('position');
    const decimalPlaces = $(this).data('decimal');
    const isActive = $(this).data('active');

    console.log('Edit currency clicked:', {
      id: currencyId,
      name: currencyName,
      code: currencyCode,
      symbol: currencySymbol,
      position: currencyPosition,
      decimal: decimalPlaces,
      active: isActive
    });

    // Populate edit form
    $('#edit_currency_name').val(currencyName);
    $('#edit_currency_code').val(currencyCode);
    $('#edit_currency_symbol').val(currencySymbol);
    $('#edit_currency_position').val(currencyPosition);
    $('#edit_decimal_places').val(decimalPlaces);
    $('#edit_is_active').prop('checked', isActive == 1);

    // Set form action
    $('#editCurrencyForm').attr('action', `/currency-settings/${currencyId}`);

    // Show modal
    $('#editCurrencyModal').modal('show');
  });

  // Form validation for add currency
  $('#addCurrencyForm').on('submit', function(e) {
    const currencyCode = $('#add_currency_code').val().toUpperCase();
    $('#add_currency_code').val(currencyCode);

    // Basic validation
    if (currencyCode.length !== 3) {
      e.preventDefault();
      showAlert('error', 'Currency code must be exactly 3 characters long.');
      return false;
    }
  });

  // Form validation for edit currency
  $('#editCurrencyForm').on('submit', function(e) {
    console.log('Edit form submitted');

    const currencyCode = $('#edit_currency_code').val().toUpperCase();
    $('#edit_currency_code').val(currencyCode);

    // Basic validation
    if (currencyCode.length !== 3) {
      e.preventDefault();
      showAlert('error', 'Currency code must be exactly 3 characters long.');
      return false;
    }

    // Debug form data
    const formData = new FormData(this);
    console.log('Form data:', Object.fromEntries(formData));
    console.log('Form action:', $(this).attr('action'));
    console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));
  });

  // Currency code input formatting
  $('#add_currency_code, #edit_currency_code').on('input', function() {
    $(this).val($(this).val().toUpperCase());
  });

  // Preview currency format in real-time
  function updateCurrencyPreview(modalType) {
    const prefix = modalType === 'add' ? 'add_' : 'edit_';
    const symbol = $(`#${prefix}currency_symbol`).val();
    const position = $(`#${prefix}currency_position`).val();
    const decimal = parseInt($(`#${prefix}decimal_places`).val()) || 2;

    const sampleAmount = 1234.56;
    const formattedAmount = sampleAmount.toFixed(decimal);

    let preview;
    if (position === 'prefix') {
      preview = symbol + formattedAmount;
    } else {
      preview = formattedAmount + symbol;
    }

    // Update preview if element exists
    const previewElement = $(`#${prefix}currency_preview`);
    if (previewElement.length) {
      previewElement.text(preview);
    }
  }

  // Real-time preview updates
  $('#add_currency_symbol, #add_currency_position, #add_decimal_places').on('change input', function() {
    updateCurrencyPreview('add');
  });

  $('#edit_currency_symbol, #edit_currency_position, #edit_decimal_places').on('change input', function() {
    updateCurrencyPreview('edit');
  });

  // Show alert function
  function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const alertHtml = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of the page
    $('main').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
      $('.alert').fadeOut();
    }, 5000);
  }

  // Handle form submission responses
  $('form').on('submit', function() {
    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.html();

    submitBtn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-2"></i>Processing...');

    // Re-enable button after 10 seconds as fallback
    setTimeout(function() {
      submitBtn.prop('disabled', false).html(originalText);
    }, 10000);
  });

  // Initialize Select2 for currency dropdown
  if ($('#add_currency_list').length) {
    $('#add_currency_list').select2({
      dropdownParent: $('#addCurrencyModal'),
      placeholder: 'Select a currency...',
      allowClear: true
    });
  }

  // Reset forms when modals are hidden
  $('#addCurrencyModal').on('hidden.bs.modal', function() {
    $('#addCurrencyForm')[0].reset();
    $('#add_currency_list').val('').trigger('change');
  });

  $('#editCurrencyModal').on('hidden.bs.modal', function() {
    $('#editCurrencyForm')[0].reset();
  });

  // Confirm deletion
  $('form[method="POST"]').on('submit', function(e) {
    if ($(this).find('button[type="submit"]').hasClass('text-danger')) {
      if (!confirm('Are you sure you want to delete this currency setting?')) {
        e.preventDefault();
        return false;
      }
    }
  });

  // Auto-refresh table after successful operations
  if (window.location.search.includes('success')) {
    setTimeout(function() {
      if (currencyTable) {
        currencyTable.ajax.reload(null, false);
      }
    }, 1000);
  }
});

// Global currency formatting function
window.formatCurrency = function(amount, currencyData = null) {
  if (!currencyData) {
    // Use default currency if none provided
    currencyData = {
      symbol: '$',
      position: 'prefix',
      decimal_places: 2
    };
  }

  const formattedAmount = parseFloat(amount).toFixed(currencyData.decimal_places);

  if (currencyData.position === 'prefix') {
    return currencyData.symbol + formattedAmount;
  } else {
    return formattedAmount + currencyData.symbol;
  }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    formatCurrency: window.formatCurrency
  };
}
