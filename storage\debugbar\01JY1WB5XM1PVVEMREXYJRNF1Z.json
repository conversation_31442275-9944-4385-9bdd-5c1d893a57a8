{"__meta": {"id": "01JY1WB5XM1PVVEMREXYJRNF1Z", "datetime": "2025-06-18 16:00:54", "utime": **********.196779, "method": "GET", "uri": "/manufacturing-orders/data?draw=1&columns%5B0%5D%5Bdata%5D=&columns%5B0%5D%5Bname%5D=&columns%5B0%5D%5Bsearchable%5D=false&columns%5B0%5D%5Borderable%5D=false&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=order_number&columns%5B1%5D%5Bname%5D=&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=finished_product&columns%5B2%5D%5Bname%5D=&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=planned_quantity&columns%5B3%5D%5Bname%5D=&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=completion_percentage&columns%5B4%5D%5Bname%5D=&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=planned_date&columns%5B5%5D%5Bname%5D=&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=status&columns%5B6%5D%5Bname%5D=&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=true&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B7%5D%5Bdata%5D=action&columns%5B7%5D%5Bname%5D=&columns%5B7%5D%5Bsearchable%5D=false&columns%5B7%5D%5Borderable%5D=false&columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=1&order%5B0%5D%5Bdir%5D=desc&start=0&length=7&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1750262452655", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[16:00:54] LOG.error: Call to a member function canProduce() on null {\n    \"userId\": 1,\n    \"exception\": {\n        \"xdebug_message\": \"<tr><th align='left' bgcolor='#f57900' colspan=\\\"5\\\"><span style='background-color: #cc0000; color: #fce94f; font-size: x-large;'>( ! )<\\/span> Error: Call to a member function canProduce() on null in C:\\\\wamp64\\\\www\\\\recipe\\\\app\\\\Models\\\\ManufacturingOrder.php on line <i>220<\\/i><\\/th><\\/tr>\\n<tr><th align='left' bgcolor='#e9b96e' colspan='5'>Call Stack<\\/th><\\/tr>\\n<tr><th align='center' bgcolor='#eeeeec'>#<\\/th><th align='left' bgcolor='#eeeeec'>Time<\\/th><th align='left' bgcolor='#eeeeec'>Memory<\\/th><th align='left' bgcolor='#eeeeec'>Function<\\/th><th align='left' bgcolor='#eeeeec'>Location<\\/th><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>1<\\/td><td bgcolor='#eeeeec' align='center'>0.0000<\\/td><td bgcolor='#eeeeec' align='right'>392200<\\/td><td bgcolor='#eeeeec'>{main}(  )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\resources\\\\server.php' bgcolor='#eeeeec'>...\\\\server.php<b>:<\\/b>0<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>2<\\/td><td bgcolor='#eeeeec' align='center'>0.0019<\\/td><td bgcolor='#eeeeec' align='right'>394024<\\/td><td bgcolor='#eeeeec'>require_once( <font color='#00bb00'>'C:\\\\wamp64\\\\www\\\\recipe\\\\public\\\\index.php<\\/font> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\resources\\\\server.php' bgcolor='#eeeeec'>...\\\\server.php<b>:<\\/b>23<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>3<\\/td><td bgcolor='#eeeeec' align='center'>0.0077<\\/td><td bgcolor='#eeeeec' align='right'>660528<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Application->handleRequest( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\public\\\\index.php' bgcolor='#eeeeec'>...\\\\index.php<b>:<\\/b>17<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>4<\\/td><td bgcolor='#eeeeec' align='center'>0.0088<\\/td><td bgcolor='#eeeeec' align='right'>673088<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Kernel->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Application.php' bgcolor='#eeeeec'>...\\\\Application.php<b>:<\\/b>1220<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>5<\\/td><td bgcolor='#eeeeec' align='center'>0.0100<\\/td><td bgcolor='#eeeeec' align='right'>1214080<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Kernel->sendRequestThroughRouter( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php' bgcolor='#eeeeec'>...\\\\Kernel.php<b>:<\\/b>145<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>6<\\/td><td bgcolor='#eeeeec' align='center'>0.1029<\\/td><td bgcolor='#eeeeec' align='right'>2715320<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->then( <span>$destination = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php' bgcolor='#eeeeec'>...\\\\Kernel.php<b>:<\\/b>176<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>7<\\/td><td bgcolor='#eeeeec' align='center'>0.1030<\\/td><td bgcolor='#eeeeec' align='right'>2723720<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>127<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>8<\\/td><td bgcolor='#eeeeec' align='center'>0.1030<\\/td><td bgcolor='#eeeeec' align='right'>2723976<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\InvokeDeferredCallbacks->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>9<\\/td><td bgcolor='#eeeeec' align='center'>0.1030<\\/td><td bgcolor='#eeeeec' align='right'>2723976<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\InvokeDeferredCallbacks.php' bgcolor='#eeeeec'>...\\\\InvokeDeferredCallbacks.php<b>:<\\/b>22<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>10<\\/td><td bgcolor='#eeeeec' align='center'>0.1031<\\/td><td bgcolor='#eeeeec' align='right'>2724304<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Http\\\\Middleware\\\\TrustProxies->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>11<\\/td><td bgcolor='#eeeeec' align='center'>0.1031<\\/td><td bgcolor='#eeeeec' align='right'>2724304<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\TrustProxies.php' bgcolor='#eeeeec'>...\\\\TrustProxies.php<b>:<\\/b>58<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>12<\\/td><td bgcolor='#eeeeec' align='center'>0.1034<\\/td><td bgcolor='#eeeeec' align='right'>2724824<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Http\\\\Middleware\\\\HandleCors->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>13<\\/td><td bgcolor='#eeeeec' align='center'>0.1061<\\/td><td bgcolor='#eeeeec' align='right'>2726216<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\HandleCors.php' bgcolor='#eeeeec'>...\\\\HandleCors.php<b>:<\\/b>49<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>14<\\/td><td bgcolor='#eeeeec' align='center'>0.1062<\\/td><td bgcolor='#eeeeec' align='right'>2726512<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>15<\\/td><td bgcolor='#eeeeec' align='center'>0.1079<\\/td><td bgcolor='#eeeeec' align='right'>2727560<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php' bgcolor='#eeeeec'>...\\\\PreventRequestsDuringMaintenance.php<b>:<\\/b>110<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>16<\\/td><td bgcolor='#eeeeec' align='center'>0.1080<\\/td><td bgcolor='#eeeeec' align='right'>2727816<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Http\\\\Middleware\\\\ValidatePostSize->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>17<\\/td><td bgcolor='#eeeeec' align='center'>0.1080<\\/td><td bgcolor='#eeeeec' align='right'>2727816<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\ValidatePostSize.php' bgcolor='#eeeeec'>...\\\\ValidatePostSize.php<b>:<\\/b>27<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>18<\\/td><td bgcolor='#eeeeec' align='center'>0.1081<\\/td><td bgcolor='#eeeeec' align='right'>2728088<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>19<\\/td><td bgcolor='#eeeeec' align='center'>0.1081<\\/td><td bgcolor='#eeeeec' align='right'>2728120<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php' bgcolor='#eeeeec'>...\\\\TrimStrings.php<b>:<\\/b>51<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>20<\\/td><td bgcolor='#eeeeec' align='center'>0.1085<\\/td><td bgcolor='#eeeeec' align='right'>2735696<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php' bgcolor='#eeeeec'>...\\\\TransformsRequest.php<b>:<\\/b>21<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>21<\\/td><td bgcolor='#eeeeec' align='center'>0.1086<\\/td><td bgcolor='#eeeeec' align='right'>2735952<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>22<\\/td><td bgcolor='#eeeeec' align='center'>0.1086<\\/td><td bgcolor='#eeeeec' align='right'>2735968<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull.php' bgcolor='#eeeeec'>...\\\\ConvertEmptyStringsToNull.php<b>:<\\/b>31<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>23<\\/td><td bgcolor='#eeeeec' align='center'>0.1087<\\/td><td bgcolor='#eeeeec' align='right'>2735968<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php' bgcolor='#eeeeec'>...\\\\TransformsRequest.php<b>:<\\/b>21<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>24<\\/td><td bgcolor='#eeeeec' align='center'>0.1090<\\/td><td bgcolor='#eeeeec' align='right'>2808528<\\/td><td bgcolor='#eeeeec'>Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>25<\\/td><td bgcolor='#eeeeec' align='center'>0.1138<\\/td><td bgcolor='#eeeeec' align='right'>3044128<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:168-174}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php' bgcolor='#eeeeec'>...\\\\InjectDebugbar.php<b>:<\\/b>66<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>26<\\/td><td bgcolor='#eeeeec' align='center'>0.1138<\\/td><td bgcolor='#eeeeec' align='right'>3044128<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Kernel->Illuminate\\\\Foundation\\\\Http\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php:198-202}( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>170<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>27<\\/td><td bgcolor='#eeeeec' align='center'>0.1138<\\/td><td bgcolor='#eeeeec' align='right'>3044912<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Router->dispatch( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php' bgcolor='#eeeeec'>...\\\\Kernel.php<b>:<\\/b>201<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>28<\\/td><td bgcolor='#eeeeec' align='center'>0.1139<\\/td><td bgcolor='#eeeeec' align='right'>3044912<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Router->dispatchToRoute( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php' bgcolor='#eeeeec'>...\\\\Router.php<b>:<\\/b>739<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>29<\\/td><td bgcolor='#eeeeec' align='center'>0.1209<\\/td><td bgcolor='#eeeeec' align='right'>3097328<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Router->runRoute( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$route = <\\/span><span>class Illuminate\\\\Routing\\\\Route { public $uri = &#39;manufacturing-orders\\/data&#39;; public $methods = [0 =&gt; &#39;GET&#39;, 1 =&gt; &#39;HEAD&#39;]; public $action = [&#39;middleware&#39; =&gt; [...], &#39;uses&#39; =&gt; &#39;App\\\\\\\\Http\\\\\\\\Controllers\\\\\\\\ManufacturingOrderController@getData&#39;, &#39;controller&#39; =&gt; &#39;App\\\\\\\\Http\\\\\\\\Controllers\\\\\\\\ManufacturingOrderController@getData&#39;, &#39;namespace&#39; =&gt; NULL, &#39;prefix&#39; =&gt; &#39;\\/manufacturing-orders&#39;, &#39;where&#39; =&gt; [...], &#39;as&#39; =&gt; &#39;manufacturing-orders.data&#39;]; public $isFallback = FALSE; public $controller = class App\\\\Http\\\\Controllers\\\\ManufacturingOrderController {  }; public $defaults = []; public $wheres = []; public $parameters = []; public $parameterNames = []; protected $originalParameters = []; protected $withTrashedBindings = FALSE; protected $lockSeconds = NULL; protected $waitSeconds = NULL; public $computedMiddleware = [0 =&gt; &#39;web&#39;, 1 =&gt; &#39;auth&#39;]; public $compiled = class Symfony\\\\Component\\\\Routing\\\\CompiledRoute { private string $staticPrefix = &#39;\\/manufacturing-orders\\/data&#39;; private string $regex = &#39;{^\\/manufacturing\\\\\\\\-orders\\/data$}sDu&#39;; private array $tokens = [...]; private array $pathVariables = [...]; private ?string $hostRegex = NULL; private array $hostTokens = [...]; private array $hostVariables = [...]; private array $variables = [...] }; protected $router = class Illuminate\\\\Routing\\\\Router { protected $events = class Illuminate\\\\Events\\\\Dispatcher { ... }; protected $container = class Illuminate\\\\Foundation\\\\Application { ... }; protected $routes = class Illuminate\\\\Routing\\\\RouteCollection { ... }; protected $current = ...; protected $currentRequest = class Illuminate\\\\Http\\\\Request { ... }; protected $middleware = [...]; protected $middlewareGroups = [...]; public $middlewarePriority = [...]; protected $binders = [...]; protected $patterns = [...]; protected $groupStack = [...]; protected $implicitBindingCallback = NULL }; protected $container = class Illuminate\\\\Foundation\\\\Application { protected $resolved = [...]; protected $bindings = [...]; protected $methodBindings = [...]; protected $instances = [...]; protected $scopedInstances = [...]; protected $aliases = [...]; protected $abstractAliases = [...]; protected $extenders = [...]; protected $tags = [...]; protected $buildStack = [...]; protected $with = [...]; public $contextual = [...]; public $contextualAttributes = [...]; protected $reboundCallbacks = [...]; protected $globalBeforeResolvingCallbacks = [...]; protected $globalResolvingCallbacks = [...]; protected $globalAfterResolvingCallbacks = [...]; protected $beforeResolvingCallbacks = [...]; protected $resolvingCallbacks = [...]; protected $afterResolvingCallbacks = [...]; protected $afterResolvingAttributeCallbacks = [...]; protected $basePath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe&#39;; protected $registeredCallbacks = [...]; protected $hasBeenBootstrapped = TRUE; protected $booted = TRUE; protected $bootingCallbacks = [...]; protected $bootedCallbacks = [...]; protected $terminatingCallbacks = [...]; protected $serviceProviders = [...]; protected $loadedProviders = [...]; protected $deferredServices = [...]; protected $bootstrapPath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe\\\\\\\\bootstrap&#39;; protected $appPath = NULL; protected $configPath = NULL; protected $databasePath = NULL; protected $langPath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe\\\\\\\\lang&#39;; protected $publicPath = NULL; protected $storagePath = NULL; protected $environmentPath = NULL; protected $environmentFile = &#39;.env&#39;; protected $isRunningInConsole = FALSE; protected $namespace = NULL; protected $mergeFrameworkConfiguration = TRUE; protected $absoluteCachePathPrefixes = [...] }; protected $bindingFields = [] }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php' bgcolor='#eeeeec'>...\\\\Router.php<b>:<\\/b>750<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>30<\\/td><td bgcolor='#eeeeec' align='center'>0.1211<\\/td><td bgcolor='#eeeeec' align='right'>3098448<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Router->runRouteWithinStack( <span>$route = <\\/span><span>class Illuminate\\\\Routing\\\\Route { public $uri = &#39;manufacturing-orders\\/data&#39;; public $methods = [0 =&gt; &#39;GET&#39;, 1 =&gt; &#39;HEAD&#39;]; public $action = [&#39;middleware&#39; =&gt; [...], &#39;uses&#39; =&gt; &#39;App\\\\\\\\Http\\\\\\\\Controllers\\\\\\\\ManufacturingOrderController@getData&#39;, &#39;controller&#39; =&gt; &#39;App\\\\\\\\Http\\\\\\\\Controllers\\\\\\\\ManufacturingOrderController@getData&#39;, &#39;namespace&#39; =&gt; NULL, &#39;prefix&#39; =&gt; &#39;\\/manufacturing-orders&#39;, &#39;where&#39; =&gt; [...], &#39;as&#39; =&gt; &#39;manufacturing-orders.data&#39;]; public $isFallback = FALSE; public $controller = class App\\\\Http\\\\Controllers\\\\ManufacturingOrderController {  }; public $defaults = []; public $wheres = []; public $parameters = []; public $parameterNames = []; protected $originalParameters = []; protected $withTrashedBindings = FALSE; protected $lockSeconds = NULL; protected $waitSeconds = NULL; public $computedMiddleware = [0 =&gt; &#39;web&#39;, 1 =&gt; &#39;auth&#39;]; public $compiled = class Symfony\\\\Component\\\\Routing\\\\CompiledRoute { private string $staticPrefix = &#39;\\/manufacturing-orders\\/data&#39;; private string $regex = &#39;{^\\/manufacturing\\\\\\\\-orders\\/data$}sDu&#39;; private array $tokens = [...]; private array $pathVariables = [...]; private ?string $hostRegex = NULL; private array $hostTokens = [...]; private array $hostVariables = [...]; private array $variables = [...] }; protected $router = class Illuminate\\\\Routing\\\\Router { protected $events = class Illuminate\\\\Events\\\\Dispatcher { ... }; protected $container = class Illuminate\\\\Foundation\\\\Application { ... }; protected $routes = class Illuminate\\\\Routing\\\\RouteCollection { ... }; protected $current = ...; protected $currentRequest = class Illuminate\\\\Http\\\\Request { ... }; protected $middleware = [...]; protected $middlewareGroups = [...]; public $middlewarePriority = [...]; protected $binders = [...]; protected $patterns = [...]; protected $groupStack = [...]; protected $implicitBindingCallback = NULL }; protected $container = class Illuminate\\\\Foundation\\\\Application { protected $resolved = [...]; protected $bindings = [...]; protected $methodBindings = [...]; protected $instances = [...]; protected $scopedInstances = [...]; protected $aliases = [...]; protected $abstractAliases = [...]; protected $extenders = [...]; protected $tags = [...]; protected $buildStack = [...]; protected $with = [...]; public $contextual = [...]; public $contextualAttributes = [...]; protected $reboundCallbacks = [...]; protected $globalBeforeResolvingCallbacks = [...]; protected $globalResolvingCallbacks = [...]; protected $globalAfterResolvingCallbacks = [...]; protected $beforeResolvingCallbacks = [...]; protected $resolvingCallbacks = [...]; protected $afterResolvingCallbacks = [...]; protected $afterResolvingAttributeCallbacks = [...]; protected $basePath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe&#39;; protected $registeredCallbacks = [...]; protected $hasBeenBootstrapped = TRUE; protected $booted = TRUE; protected $bootingCallbacks = [...]; protected $bootedCallbacks = [...]; protected $terminatingCallbacks = [...]; protected $serviceProviders = [...]; protected $loadedProviders = [...]; protected $deferredServices = [...]; protected $bootstrapPath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe\\\\\\\\bootstrap&#39;; protected $appPath = NULL; protected $configPath = NULL; protected $databasePath = NULL; protected $langPath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe\\\\\\\\lang&#39;; protected $publicPath = NULL; protected $storagePath = NULL; protected $environmentPath = NULL; protected $environmentFile = &#39;.env&#39;; protected $isRunningInConsole = FALSE; protected $namespace = NULL; protected $mergeFrameworkConfiguration = TRUE; protected $absoluteCachePathPrefixes = [...] }; protected $bindingFields = [] }<\\/span>, <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php' bgcolor='#eeeeec'>...\\\\Router.php<b>:<\\/b>786<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>31<\\/td><td bgcolor='#eeeeec' align='center'>0.1219<\\/td><td bgcolor='#eeeeec' align='right'>3112712<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->then( <span>$destination = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php' bgcolor='#eeeeec'>...\\\\Router.php<b>:<\\/b>807<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>32<\\/td><td bgcolor='#eeeeec' align='center'>0.1220<\\/td><td bgcolor='#eeeeec' align='right'>3122064<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>127<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>33<\\/td><td bgcolor='#eeeeec' align='center'>0.1223<\\/td><td bgcolor='#eeeeec' align='right'>3122552<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>34<\\/td><td bgcolor='#eeeeec' align='center'>0.1413<\\/td><td bgcolor='#eeeeec' align='right'>3135000<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php' bgcolor='#eeeeec'>...\\\\EncryptCookies.php<b>:<\\/b>75<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>35<\\/td><td bgcolor='#eeeeec' align='center'>0.1415<\\/td><td bgcolor='#eeeeec' align='right'>3135400<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>36<\\/td><td bgcolor='#eeeeec' align='center'>0.1415<\\/td><td bgcolor='#eeeeec' align='right'>3135400<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse.php' bgcolor='#eeeeec'>...\\\\AddQueuedCookiesToResponse.php<b>:<\\/b>37<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>37<\\/td><td bgcolor='#eeeeec' align='center'>0.1416<\\/td><td bgcolor='#eeeeec' align='right'>3136584<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>38<\\/td><td bgcolor='#eeeeec' align='center'>0.1419<\\/td><td bgcolor='#eeeeec' align='right'>3138984<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$session = <\\/span><span>class Illuminate\\\\Session\\\\Store { protected $id = &#39;3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr&#39;; protected $name = &#39;laravel_session&#39;; protected $attributes = [&#39;_token&#39; =&gt; &#39;Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z&#39;, &#39;url&#39; =&gt; [...], &#39;_previous&#39; =&gt; [...], &#39;_flash&#39; =&gt; [...], &#39;login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d&#39; =&gt; 1, &#39;PHPDEBUGBAR_STACK_DATA&#39; =&gt; [...]]; protected $handler = class Illuminate\\\\Session\\\\DatabaseSessionHandler { protected $connection = class Illuminate\\\\Database\\\\MySqlConnection { ... }; protected $table = &#39;sessions&#39;; protected $minutes = &#39;120&#39;; protected $container = class Illuminate\\\\Foundation\\\\Application { ... }; protected $exists = TRUE }; protected $serialization = &#39;php&#39;; protected $started = TRUE }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php' bgcolor='#eeeeec'>...\\\\StartSession.php<b>:<\\/b>64<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>39<\\/td><td bgcolor='#eeeeec' align='center'>0.1537<\\/td><td bgcolor='#eeeeec' align='right'>3261880<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php' bgcolor='#eeeeec'>...\\\\StartSession.php<b>:<\\/b>121<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>40<\\/td><td bgcolor='#eeeeec' align='center'>0.1539<\\/td><td bgcolor='#eeeeec' align='right'>3262152<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>41<\\/td><td bgcolor='#eeeeec' align='center'>0.1539<\\/td><td bgcolor='#eeeeec' align='right'>3263224<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession.php' bgcolor='#eeeeec'>...\\\\ShareErrorsFromSession.php<b>:<\\/b>49<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>42<\\/td><td bgcolor='#eeeeec' align='center'>0.1540<\\/td><td bgcolor='#eeeeec' align='right'>3263552<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>43<\\/td><td bgcolor='#eeeeec' align='center'>0.1540<\\/td><td bgcolor='#eeeeec' align='right'>3263552<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken.php' bgcolor='#eeeeec'>...\\\\VerifyCsrfToken.php<b>:<\\/b>88<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>44<\\/td><td bgcolor='#eeeeec' align='center'>0.1542<\\/td><td bgcolor='#eeeeec' align='right'>3264368<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Auth\\\\Middleware\\\\Authenticate->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>45<\\/td><td bgcolor='#eeeeec' align='center'>0.1629<\\/td><td bgcolor='#eeeeec' align='right'>3351152<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Auth\\\\Middleware\\\\Authenticate.php' bgcolor='#eeeeec'>...\\\\Authenticate.php<b>:<\\/b>64<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>46<\\/td><td bgcolor='#eeeeec' align='center'>0.1631<\\/td><td bgcolor='#eeeeec' align='right'>3351424<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>47<\\/td><td bgcolor='#eeeeec' align='center'>0.1635<\\/td><td bgcolor='#eeeeec' align='right'>3351424<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings.php' bgcolor='#eeeeec'>...\\\\SubstituteBindings.php<b>:<\\/b>51<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>48<\\/td><td bgcolor='#eeeeec' align='center'>0.1635<\\/td><td bgcolor='#eeeeec' align='right'>3357312<\\/td><td bgcolor='#eeeeec'>App\\\\Http\\\\Middleware\\\\LocaleMiddleware->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>49<\\/td><td bgcolor='#eeeeec' align='center'>0.1636<\\/td><td bgcolor='#eeeeec' align='right'>3357312<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:185-216}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\app\\\\Http\\\\Middleware\\\\LocaleMiddleware.php' bgcolor='#eeeeec'>...\\\\LocaleMiddleware.php<b>:<\\/b>23<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>50<\\/td><td bgcolor='#eeeeec' align='center'>0.1638<\\/td><td bgcolor='#eeeeec' align='right'>3357624<\\/td><td bgcolor='#eeeeec'>Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span>, <span>$next = <\\/span><span>class Closure {  }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>209<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>51<\\/td><td bgcolor='#eeeeec' align='center'>0.1639<\\/td><td bgcolor='#eeeeec' align='right'>3357624<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php:168-174}( <span>$passable = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php' bgcolor='#eeeeec'>...\\\\InjectDebugbar.php<b>:<\\/b>66<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>52<\\/td><td bgcolor='#eeeeec' align='center'>0.1639<\\/td><td bgcolor='#eeeeec' align='right'>3357624<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Router->Illuminate\\\\Routing\\\\{closure:C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php:807-809}( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php' bgcolor='#eeeeec'>...\\\\Pipeline.php<b>:<\\/b>170<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>53<\\/td><td bgcolor='#eeeeec' align='center'>0.1639<\\/td><td bgcolor='#eeeeec' align='right'>3357624<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Route->run(  )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php' bgcolor='#eeeeec'>...\\\\Router.php<b>:<\\/b>808<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>54<\\/td><td bgcolor='#eeeeec' align='center'>0.1639<\\/td><td bgcolor='#eeeeec' align='right'>3357624<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\Route->runController(  )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php' bgcolor='#eeeeec'>...\\\\Route.php<b>:<\\/b>212<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>55<\\/td><td bgcolor='#eeeeec' align='center'>0.1641<\\/td><td bgcolor='#eeeeec' align='right'>3357832<\\/td><td bgcolor='#eeeeec'>Illuminate\\\\Routing\\\\ControllerDispatcher->dispatch( <span>$route = <\\/span><span>class Illuminate\\\\Routing\\\\Route { public $uri = &#39;manufacturing-orders\\/data&#39;; public $methods = [0 =&gt; &#39;GET&#39;, 1 =&gt; &#39;HEAD&#39;]; public $action = [&#39;middleware&#39; =&gt; [...], &#39;uses&#39; =&gt; &#39;App\\\\\\\\Http\\\\\\\\Controllers\\\\\\\\ManufacturingOrderController@getData&#39;, &#39;controller&#39; =&gt; &#39;App\\\\\\\\Http\\\\\\\\Controllers\\\\\\\\ManufacturingOrderController@getData&#39;, &#39;namespace&#39; =&gt; NULL, &#39;prefix&#39; =&gt; &#39;\\/manufacturing-orders&#39;, &#39;where&#39; =&gt; [...], &#39;as&#39; =&gt; &#39;manufacturing-orders.data&#39;]; public $isFallback = FALSE; public $controller = class App\\\\Http\\\\Controllers\\\\ManufacturingOrderController {  }; public $defaults = []; public $wheres = []; public $parameters = []; public $parameterNames = []; protected $originalParameters = []; protected $withTrashedBindings = FALSE; protected $lockSeconds = NULL; protected $waitSeconds = NULL; public $computedMiddleware = [0 =&gt; &#39;web&#39;, 1 =&gt; &#39;auth&#39;]; public $compiled = class Symfony\\\\Component\\\\Routing\\\\CompiledRoute { private string $staticPrefix = &#39;\\/manufacturing-orders\\/data&#39;; private string $regex = &#39;{^\\/manufacturing\\\\\\\\-orders\\/data$}sDu&#39;; private array $tokens = [...]; private array $pathVariables = [...]; private ?string $hostRegex = NULL; private array $hostTokens = [...]; private array $hostVariables = [...]; private array $variables = [...] }; protected $router = class Illuminate\\\\Routing\\\\Router { protected $events = class Illuminate\\\\Events\\\\Dispatcher { ... }; protected $container = class Illuminate\\\\Foundation\\\\Application { ... }; protected $routes = class Illuminate\\\\Routing\\\\RouteCollection { ... }; protected $current = ...; protected $currentRequest = class Illuminate\\\\Http\\\\Request { ... }; protected $middleware = [...]; protected $middlewareGroups = [...]; public $middlewarePriority = [...]; protected $binders = [...]; protected $patterns = [...]; protected $groupStack = [...]; protected $implicitBindingCallback = NULL }; protected $container = class Illuminate\\\\Foundation\\\\Application { protected $resolved = [...]; protected $bindings = [...]; protected $methodBindings = [...]; protected $instances = [...]; protected $scopedInstances = [...]; protected $aliases = [...]; protected $abstractAliases = [...]; protected $extenders = [...]; protected $tags = [...]; protected $buildStack = [...]; protected $with = [...]; public $contextual = [...]; public $contextualAttributes = [...]; protected $reboundCallbacks = [...]; protected $globalBeforeResolvingCallbacks = [...]; protected $globalResolvingCallbacks = [...]; protected $globalAfterResolvingCallbacks = [...]; protected $beforeResolvingCallbacks = [...]; protected $resolvingCallbacks = [...]; protected $afterResolvingCallbacks = [...]; protected $afterResolvingAttributeCallbacks = [...]; protected $basePath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe&#39;; protected $registeredCallbacks = [...]; protected $hasBeenBootstrapped = TRUE; protected $booted = TRUE; protected $bootingCallbacks = [...]; protected $bootedCallbacks = [...]; protected $terminatingCallbacks = [...]; protected $serviceProviders = [...]; protected $loadedProviders = [...]; protected $deferredServices = [...]; protected $bootstrapPath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe\\\\\\\\bootstrap&#39;; protected $appPath = NULL; protected $configPath = NULL; protected $databasePath = NULL; protected $langPath = &#39;C:\\\\\\\\wamp64\\\\\\\\www\\\\\\\\recipe\\\\\\\\lang&#39;; protected $publicPath = NULL; protected $storagePath = NULL; protected $environmentPath = NULL; protected $environmentFile = &#39;.env&#39;; protected $isRunningInConsole = FALSE; protected $namespace = NULL; protected $mergeFrameworkConfiguration = TRUE; protected $absoluteCachePathPrefixes = [...] }; protected $bindingFields = [] }<\\/span>, <span>$controller = <\\/span><span>class App\\\\Http\\\\Controllers\\\\ManufacturingOrderController {  }<\\/span>, <span>$method = <\\/span><span>&#39;getData&#39;<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php' bgcolor='#eeeeec'>...\\\\Route.php<b>:<\\/b>266<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>56<\\/td><td bgcolor='#eeeeec' align='center'>0.1642<\\/td><td bgcolor='#eeeeec' align='right'>3358048<\\/td><td bgcolor='#eeeeec'>App\\\\Http\\\\Controllers\\\\ManufacturingOrderController->getData( <span>$request = <\\/span><span>class Illuminate\\\\Http\\\\Request { public Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag $attributes = class Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $request = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $query = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag $server = class Symfony\\\\Component\\\\HttpFoundation\\\\ServerBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\FileBag $files = class Symfony\\\\Component\\\\HttpFoundation\\\\FileBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\InputBag $cookies = class Symfony\\\\Component\\\\HttpFoundation\\\\InputBag { protected array $parameters = [...] }; public Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag $headers = class Symfony\\\\Component\\\\HttpFoundation\\\\HeaderBag { protected array $headers = [...]; protected array $cacheControl = [...] }; protected $content = NULL; protected ?array $languages = NULL; protected ?array $charsets = NULL; protected ?array $encodings = NULL; protected ?array $acceptableContentTypes = NULL; protected ?string $pathInfo = &#39;\\/manufacturing-orders\\/data&#39;; protected ?string $requestUri = &#39;\\/manufacturing-orders\\/data?draw=1&amp;columns%5B0%5D%5Bdata%5D=&amp;columns%5B0%5D%5Bname%5D=&amp;columns%5B0%5D%5Bsearchable%5D=false&amp;columns%5B0%5D%5Borderable%5D=false&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order_number&amp;columns%5B1%5D%5Bname%5D=&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=finished_product&amp;columns%5B&#39;...; protected ?string $baseUrl = &#39;&#39;; protected ?string $basePath = NULL; protected ?string $method = &#39;GET&#39;; protected ?string $format = NULL; protected Symfony\\\\Component\\\\HttpFoundation\\\\Session\\\\SessionInterface|Closure|null $session = class Illuminate\\\\Session\\\\SymfonySessionDecorator { public readonly Illuminate\\\\Contracts\\\\Session\\\\Session $store = class Illuminate\\\\Session\\\\Store { ... } }; protected ?string $locale = NULL; protected string $defaultLocale = &#39;en&#39;; private ?string ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}preferredFormat = NULL; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isHostValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isForwardedValid = TRUE; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isSafeContentPreferred = *uninitialized*; private array ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}trustedValuesCache = []; private bool ${Symfony\\\\Component\\\\HttpFoundation\\\\Request}isIisRewrite = FALSE; protected $json = NULL; protected $convertedFiles = NULL; protected $userResolver = class Closure {  }; protected $routeResolver = class Closure {  } }<\\/span> )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\ControllerDispatcher.php' bgcolor='#eeeeec'>...\\\\ControllerDispatcher.php<b>:<\\/b>47<\\/td><\\/tr>\\n<tr><td bgcolor='#eeeeec' align='center'>57<\\/td><td bgcolor='#eeeeec' align='center'>0.2455<\\/td><td bgcolor='#eeeeec' align='right'>3840616<\\/td><td bgcolor='#eeeeec'>App\\\\Models\\\\ManufacturingOrder->canStart(  )<\\/td><td title='C:\\\\wamp64\\\\www\\\\recipe\\\\app\\\\Http\\\\Controllers\\\\ManufacturingOrderController.php' bgcolor='#eeeeec'>...\\\\ManufacturingOrderController.php<b>:<\\/b>135<\\/td><\\/tr>\\n\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.195154, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.939656, "end": **********.196843, "duration": 0.****************, "duration_str": "257ms", "measures": [{"label": "Booting", "start": **********.939656, "relative_start": 0, "end": **********.050391, "relative_end": **********.050391, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.050412, "relative_start": 0.*****************, "end": **********.196847, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.054384, "relative_start": 0.*****************, "end": **********.061461, "relative_end": **********.061461, "duration": 0.*****************, "duration_str": "7.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 4425792, "peak_usage_str": "4MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Error", "message": "Call to a member function canProduce() on null", "code": 0, "file": "app/Models/ManufacturingOrder.php", "line": 220, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:56</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">app/Http/Controllers/ManufacturingOrderController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>135</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">canStart</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\ManufacturingOrder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">getData</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">App\\Http\\Controllers\\ManufacturingOrderController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>266</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"58 characters\">[object App\\Http\\Controllers\\ManufacturingOrderController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">getData</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>212</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">app/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">App\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1220</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">C:\\wamp64\\www\\recipe\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    public function canStart(): bool\n", "    {\n", "        return $this->status === 'planned' &&\n", "               $this->finishedProduct->canProduce($this->planned_quantity);\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrder.php&line=220", "ajax": false, "filename": "ManufacturingOrder.php", "line": "220"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 11, "nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01557, "accumulated_duration_str": "15.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `manu_sessions` where `id` = '3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr' limit 1", "type": "query", "params": [], "bindings": ["3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.082867, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "manufacturing", "explain": null, "start_percent": 0, "width_percent": 33.205}, {"sql": "select * from `manu_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.096389, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "manufacturing", "explain": null, "start_percent": 33.205, "width_percent": 5.716}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.1069078, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:77", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=77", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "77"}, "connection": "manufacturing", "explain": null, "start_percent": 38.921, "width_percent": 5.074}, {"sql": "select * from `manu_manufacturing_orders` where `manu_manufacturing_orders`.`deleted_at` is null order by `id` desc limit 7 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.112615, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:108", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=108", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "108"}, "connection": "manufacturing", "explain": null, "start_percent": 43.995, "width_percent": 6.744}, {"sql": "select * from `manu_finished_products` where `manu_finished_products`.`id` in (2, 4, 5, 6) and `manu_finished_products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.121349, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=108", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "108"}, "connection": "manufacturing", "explain": null, "start_percent": 50.739, "width_percent": 7.514}, {"sql": "select * from `manu_units` where `manu_units`.`id` in (16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.1318371, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:108", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=108", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "108"}, "connection": "manufacturing", "explain": null, "start_percent": 58.253, "width_percent": 5.459}, {"sql": "select * from `manu_recipes` where `manu_recipes`.`finished_product_id` in (2, 4, 5, 6) order by `sort_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.142858, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:108", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=108", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "108"}, "connection": "manufacturing", "explain": null, "start_percent": 63.712, "width_percent": 4.56}, {"sql": "select * from `manu_raw_materials` where `manu_raw_materials`.`id` in (20) and `manu_raw_materials`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 30, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.148466, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:108", "source": {"index": 30, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=108", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "108"}, "connection": "manufacturing", "explain": null, "start_percent": 68.272, "width_percent": 4.175}, {"sql": "select * from `manu_users` where `manu_users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.154427, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=108", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "108"}, "connection": "manufacturing", "explain": null, "start_percent": 72.447, "width_percent": 3.661}, {"sql": "select * from `manu_manufacturing_consumptions` where `manu_manufacturing_consumptions`.`manufacturing_order_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.163282, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=108", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "108"}, "connection": "manufacturing", "explain": null, "start_percent": 76.108, "width_percent": 4.56}, {"sql": "select * from `manu_manufacturing_order_items` where `manu_manufacturing_order_items`.`manufacturing_order_id` = 6 and `manu_manufacturing_order_items`.`manufacturing_order_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ManufacturingOrder.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\ManufacturingOrder.php", "line": 326}, {"index": 26, "namespace": null, "name": "app/Models/ManufacturingOrder.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\ManufacturingOrder.php", "line": 182}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 126}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.174475, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "ManufacturingOrder.php:326", "source": {"index": 20, "namespace": null, "name": "app/Models/ManufacturingOrder.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\ManufacturingOrder.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrder.php&line=326", "ajax": false, "filename": "ManufacturingOrder.php", "line": "326"}, "connection": "manufacturing", "explain": null, "start_percent": 80.668, "width_percent": 19.332}]}, "models": {"data": {"App\\Models\\ManufacturingOrder": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrder.php&line=1", "ajax": false, "filename": "ManufacturingOrder.php", "line": "?"}}, "App\\Models\\FinishedProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FFinishedProduct.php&line=1", "ajax": false, "filename": "FinishedProduct.php", "line": "?"}}, "App\\Models\\ManufacturingConsumption": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingConsumption.php&line=1", "ajax": false, "filename": "ManufacturingConsumption.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ManufacturingOrderItem": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrderItem.php&line=1", "ajax": false, "filename": "ManufacturingOrderItem.php", "line": "?"}}, "App\\Models\\Unit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FUnit.php&line=1", "ajax": false, "filename": "Unit.php", "line": "?"}}, "App\\Models\\Recipe": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FRecipe.php&line=1", "ajax": false, "filename": "Recipe.php", "line": "?"}}, "App\\Models\\RawMaterial": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FRawMaterial.php&line=1", "ajax": false, "filename": "RawMaterial.php", "line": "?"}}}, "count": 20, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/manufacturing-orders/data?_=1750262452655&columns%5B0%5D%5Bdata%5D=&columns%5B...", "action_name": "manufacturing-orders.data", "controller_action": "App\\Http\\Controllers\\ManufacturingOrderController@getData", "uri": "GET manufacturing-orders/data", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@getData<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=65\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/manufacturing-orders", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=65\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ManufacturingOrderController.php:65-151</a>", "middleware": "web, auth", "duration": "322ms", "peak_memory": "6MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-285303595 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">order_number</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"16 characters\">finished_product</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"16 characters\">planned_quantity</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"21 characters\">completion_percentage</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">planned_date</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1750262452655</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285303595\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1323646341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1323646341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1769403245 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/manufacturing-orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1583 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IlNqTnlPK2U1bWdGTnRBUFdvWlNGUlE9PSIsInZhbHVlIjoia2tON3p0MzMxZ3VPa3kxa0sxTjZBRmhNdUVLdDBLMTRHTDBVR1hIVlRYVm8waS9VZm9EWUluNE9LZm5zVkFSVDRFM2k0Z3c3WHRNSHgzc21ZTFd4bllMeTgyTjEvQ2I3TjQzTG1zbzhHdXB4MTgxYVdNeExFNmRwTmhJb0czQUIiLCJtYWMiOiJjNTM2YTk3ZTMyYmVmOWU4NWE4ZWIxNDIyZWQyMGQyNTgzZmUzY2ZkNWZhYWFjNjZlNTY0ZTNjMDc0NzY0YzMyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Iklva0VqSmJtTDFpcndnMzlxRGpGNGc9PSIsInZhbHVlIjoiM3JIcjltSzFyNzFwcHlCa1pGZndIdnFxTWxmU3NXOWVuQnlGdzYxeFlGU09MZmVFbGt1TEVmdlV3M1duUndQd2luRHpZMG01WFRmMkNYVHowNTNTWkZQQ3FNcEtkang4L003SmRXVnB0YTliV2JNcFA1STVMSFFuSjROakpLQTIiLCJtYWMiOiIwNzMwYjEwYWUxYTQ2NjlkZTk2MTcwZWU2MDg4ZmY3ZWNlYjdhZjE4YWRlMDk5ZWYzYTNmZTVkOGRjOGQ1M2Q5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769403245\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-350760292 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-350760292\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-492570733 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 16:00:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492570733\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2014690079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/translations/fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014690079\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/manufacturing-orders/data?_=1750262452655&columns%5B0%5D%5Bdata%5D=&columns%5B...", "action_name": "manufacturing-orders.data", "controller_action": "App\\Http\\Controllers\\ManufacturingOrderController@getData"}, "badge": "500 Internal Server Error"}}