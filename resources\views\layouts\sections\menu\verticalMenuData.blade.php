@php
use Illuminate\Support\Facades\Route;

// Define menu structure with permission checks
$menuItems = [
    // Dashboard - Always visible for authenticated users
    [
        'name' => 'Dashboard',
        'icon' => 'menu-icon tf-icons ri-home-smile-line',
        'slug' => 'dashboard',
        'url' => '/',
        'permission' => null // Always visible
    ],

    // Manufacturing Section
    [
        'name' => 'Manufacturing',
        'icon' => 'menu-icon tf-icons ri-settings-3-line',
        'slug' => 'manufacturing',
        'permission' => null, // Parent menu visible if any child is visible
        'submenu' => [
            [
                'url' => 'units',
                'name' => 'Units',
                'slug' => 'units.index',
                'permission' => 'view-units'
            ],
            [
                'url' => 'raw-materials',
                'name' => 'Raw Materials',
                'slug' => 'raw-materials.index',
                'permission' => 'view-raw-materials'
            ],
            [
                'url' => 'finished-products',
                'name' => 'Finished Products',
                'slug' => 'finished-products.index',
                'permission' => 'view-finished-products'
            ],
            [
                'url' => 'manufacturing-orders',
                'name' => 'Manufacturing Orders',
                'slug' => 'manufacturing-orders.index',
                'permission' => 'view-manufacturing-orders'
            ],
            [
                'url' => 'purchases',
                'name' => 'Purchases',
                'slug' => 'purchases.index',
                'permission' => 'view-purchases'
            ]
        ]
    ],

    // POS Section
    [
        'name' => 'Point of Sale',
        'icon' => 'menu-icon tf-icons ri-shopping-cart-line',
        'slug' => 'pos',
        'url' => 'pos',
        'permission' => 'access-pos'
    ],

    // Menu Header for Admin Section
    [
        'menuHeader' => 'Administration',
        'permission' => 'view-users|view-roles' // Show header if user can see any admin feature
    ],

    // User Management
    [
        'name' => 'Users',
        'icon' => 'menu-icon tf-icons ri-user-line',
        'slug' => 'app-user',
        'permission' => 'view-users',
        'submenu' => [
            [
                'url' => 'users',
                'name' => 'User Management',
                'slug' => 'laravel-example-user-management',
                'permission' => 'view-users'
            ]
        ]
    ],

    // Roles & Permissions
    [
        'name' => 'Roles & Permissions',
        'icon' => 'menu-icon tf-icons ri-lock-2-line',
        'slug' => 'app-access',
        'permission' => 'view-roles',
        'submenu' => [
            [
                'url' => 'roles',
                'name' => 'Roles Management',
                'slug' => 'roles.index',
                'permission' => 'view-roles'
            ]
        ]
    ]
];

// Function to check if user has permission
if (!function_exists('checkMenuPermission')) {
    function checkMenuPermission($permission) {
        if (!$permission) return true; // No permission required

        if (strpos($permission, '|') !== false) {
            // Multiple permissions (OR condition)
            $permissions = explode('|', $permission);
            foreach ($permissions as $perm) {
                if (auth()->user()->can(trim($perm))) {
                    return true;
                }
            }
            return false;
        }

        return auth()->user()->can($permission);
    }
}

// Function to check if submenu has any visible items
if (!function_exists('checkVisibleSubmenu')) {
    function checkVisibleSubmenu($submenu) {
        foreach ($submenu as $item) {
            if (checkMenuPermission($item['permission'] ?? null)) {
                return true;
            }
        }
        return false;
    }
}

// Filter menu items based on permissions
$filteredMenu = [];
foreach ($menuItems as $item) {
    // Check if it's a menu header
    if (isset($item['menuHeader'])) {
        if (checkMenuPermission($item['permission'] ?? null)) {
            $filteredMenu[] = $item;
        }
        continue;
    }

    // Check if item has submenu
    if (isset($item['submenu'])) {
        // Filter submenu items
        $filteredSubmenu = [];
        foreach ($item['submenu'] as $subItem) {
            if (checkMenuPermission($subItem['permission'] ?? null)) {
                $filteredSubmenu[] = $subItem;
            }
        }

        // Only include parent if it has visible submenu items
        if (!empty($filteredSubmenu)) {
            $item['submenu'] = $filteredSubmenu;
            $filteredMenu[] = $item;
        }
    } else {
        // Single menu item
        if (checkMenuPermission($item['permission'] ?? null)) {
            $filteredMenu[] = $item;
        }
    }
}

// Convert to object format for compatibility with existing menu system
$menuData = (object) ['menu' => array_map(function($item) {
    return (object) array_map(function($value) {
        if (is_array($value)) {
            return array_map(function($subItem) {
                return (object) $subItem;
            }, $value);
        }
        return $value;
    }, $item);
}, $filteredMenu)];

@endphp

{{-- Return the filtered menu data --}}
@php
    $verticalMenuData = $menuData;
@endphp
