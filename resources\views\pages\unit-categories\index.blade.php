@extends('layouts/layoutMaster')

@section('title', __('Unit Categories Management') . ' - Manufacturing App')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite(['resources/js/unit-categories-management.js'])
@endsection

@section('content')
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">{{ __('Total Categories') }}</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2">{{ $totalCategories }}</h4>
            </div>
            <small class="mb-0">{{ __('All unit categories') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-folder-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">{{ __('Active Categories') }}</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2">{{ $activeCategories }}</h4>
            </div>
            <small class="mb-0">{{ __('Currently active') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">{{ __('Inactive Categories') }}</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2">{{ $inactiveCategories }}</h4>
            </div>
            <small class="mb-0">{{ __('Currently inactive') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-secondary rounded-3">
              <div class="ri-close-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">{{ __('Total Units') }}</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2">{{ $totalUnits }}</h4>
            </div>
            <small class="mb-0">{{ __('All units in system') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-list-check ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Unit Categories List Table -->
<div class="card">
  <div class="card-header pb-0">
    <h5 class="card-title mb-0">{{ __('Unit Categories Management') }}</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-unit-categories table">
      <thead>
        <tr>
          <th></th>
          <th>Id</th>
          <th>{{ __('Category Name') }}</th>
          <th>{{ __('Description') }}</th>
          <th>{{ __('Base Unit') }}</th>
          <th>{{ __('Units Count') }}</th>
          <th>{{ __('Status') }}</th>
          <th>{{ __('Actions') }}</th>
        </tr>
      </thead>
    </table>
  </div>

  <!-- Offcanvas to add new unit category -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddUnitCategory" aria-labelledby="offcanvasAddUnitCategoryLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddUnitCategoryLabel" class="offcanvas-title">{{ __('Add Unit Category') }}</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 h-100">
      <form class="add-new-unit-category pt-0" id="addNewUnitCategoryForm">
        <input type="hidden" name="id" id="unit_category_id">

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-category-name" placeholder="Weight" name="name" aria-label="{{ __('Category Name') }}" />
          <label for="add-category-name">{{ __('Category Name') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <textarea class="form-control" id="add-category-description" placeholder="{{ __('Description') }}..." name="description" rows="3"></textarea>
          <label for="add-category-description">{{ __('Description') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <select class="form-select" id="add-category-base-unit" name="base_unit_id" aria-label="{{ __('Base Unit') }}">
            <option value="">{{ __('Select Base Unit') }}</option>
          </select>
          <label for="add-category-base-unit">{{ __('Base Unit') }}</label>
          <div class="form-text">{{ __('Optional: Select the base unit for this category') }}</div>
        </div>

        <div class="mb-5">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="add-category-active" name="is_active" checked>
            <label class="form-check-label" for="add-category-active">{{ __('Active') }}</label>
          </div>
        </div>

        <button type="submit" class="btn btn-primary me-sm-3 me-1 data-submit">{{ __('Submit') }}</button>
        <button type="reset" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">{{ __('Cancel') }}</button>
      </form>
    </div>
  </div>
</div>
@endsection
