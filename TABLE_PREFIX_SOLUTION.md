# Table Prefix Solution for Laravel Manufacturing App

## Problem
When using table prefixes in Laravel, raw SQL queries with `DB::table()` and joins don't automatically handle the prefix, causing SQL errors.

## Root Cause
The issue occurs when:
1. Using `DB::table()` with joins that reference table names directly
2. Using raw SQL in `DB::raw()` statements with hardcoded table names
3. Not using <PERSON><PERSON>'s table prefix helper methods

## Solutions Implemented

### 1. Fixed DashboardController Raw SQL Queries

**Before (Problematic):**
```php
return DB::table('sale_items')
    ->join('finished_products', 'sale_items.finished_product_id', '=', 'finished_products.id')
    ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
    ->where('sales.sale_date', '>=', Carbon::now()->subDays(30))
    ->select('finished_products.name',
            DB::raw('SUM(sale_items.quantity) as total_sold'),
            DB::raw('SUM(sale_items.line_total) as total_revenue'))
    ->groupBy('finished_products.id', 'finished_products.name')
    ->orderBy('total_revenue', 'desc')
    ->take(5)
    ->get();
```

**After (Fixed):**
```php
// Use table prefix helper for raw SQL queries
$saleItemsTable = DB::getTablePrefix() . 'sale_items';
$finishedProductsTable = DB::getTablePrefix() . 'finished_products';
$salesTable = DB::getTablePrefix() . 'sales';

return DB::table('sale_items')
    ->join('finished_products', $saleItemsTable . '.finished_product_id', '=', $finishedProductsTable . '.id')
    ->join('sales', $saleItemsTable . '.sale_id', '=', $salesTable . '.id')
    ->where($salesTable . '.sale_date', '>=', Carbon::now()->subDays(30))
    ->select($finishedProductsTable . '.name',
            DB::raw('SUM(' . $saleItemsTable . '.quantity) as total_sold'),
            DB::raw('SUM(' . $saleItemsTable . '.line_total) as total_revenue'))
    ->groupBy($finishedProductsTable . '.id', $finishedProductsTable . '.name')
    ->orderBy('total_revenue', 'desc')
    ->take(5)
    ->get();
```

## Better Alternative: Use Eloquent Relationships

Instead of raw SQL, use Eloquent models with relationships for automatic table prefix handling:

### Option 1: Create Missing Models

**Create SaleItem Model:**
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleItem extends Model
{
    protected $fillable = [
        'sale_id',
        'finished_product_id',
        'quantity',
        'unit_price',
        'line_total'
    ];

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function finishedProduct(): BelongsTo
    {
        return $this->belongsTo(FinishedProduct::class);
    }
}
```

**Create ManufacturingConsumption Model:**
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ManufacturingConsumption extends Model
{
    protected $fillable = [
        'manufacturing_order_id',
        'raw_material_id',
        'planned_quantity',
        'actual_quantity',
        'unit_cost',
        'total_cost',
        'consumed_at',
        'recorded_by'
    ];

    protected $casts = [
        'consumed_at' => 'datetime',
    ];

    public function manufacturingOrder(): BelongsTo
    {
        return $this->belongsTo(ManufacturingOrder::class);
    }

    public function rawMaterial(): BelongsTo
    {
        return $this->belongsTo(RawMaterial::class);
    }

    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }
}
```

### Option 2: Update DashboardController with Eloquent

**Replace Raw SQL with Eloquent:**
```php
private function getTopSellingProducts()
{
    // Using Eloquent relationships (automatically handles table prefixes)
    return SaleItem::with(['finishedProduct', 'sale'])
        ->whereHas('sale', function($query) {
            $query->where('sale_date', '>=', Carbon::now()->subDays(30));
        })
        ->selectRaw('finished_product_id, SUM(quantity) as total_sold, SUM(line_total) as total_revenue')
        ->groupBy('finished_product_id')
        ->orderBy('total_revenue', 'desc')
        ->take(5)
        ->get()
        ->map(function($item) {
            return [
                'name' => $item->finishedProduct->name,
                'total_sold' => $item->total_sold,
                'total_revenue' => $item->total_revenue
            ];
        });
}

private function getTopMaterials()
{
    // Using Eloquent relationships (automatically handles table prefixes)
    return ManufacturingConsumption::with('rawMaterial')
        ->where('consumed_at', '>=', Carbon::now()->subDays(30))
        ->selectRaw('raw_material_id, SUM(actual_quantity) as total_consumed, SUM(total_cost) as total_cost')
        ->groupBy('raw_material_id')
        ->orderBy('total_cost', 'desc')
        ->take(5)
        ->get()
        ->map(function($consumption) {
            return [
                'name' => $consumption->rawMaterial->name,
                'total_consumed' => $consumption->total_consumed,
                'total_cost' => $consumption->total_cost
            ];
        });
}
```

## Configuration for Table Prefixes

### 1. Set Table Prefix in Database Config

**config/database.php:**
```php
'mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'laravel'),
    'username' => env('DB_USERNAME', 'root'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => env('DB_PREFIX', ''), // Add this line
    'prefix_indexes' => true,
    'strict' => true,
    'engine' => null,
],
```

### 2. Set Prefix in Environment File

**.env:**
```env
DB_PREFIX=mfg_
```

## General Rules for Table Prefix Compatibility

### ✅ AUTO-HANDLED (Laravel automatically adds prefix):
- `DB::table('table_name')` - Query Builder table references
- `->join('table', 'table.id', '=', 'other.id')` - Query Builder joins
- `->where('table.column', 'value')` - Query Builder where clauses
- Eloquent models and relationships

### ❌ MANUAL PREFIX REQUIRED:
- `DB::raw('SUM(table.column)')` - Raw SQL inside DB::raw()
- `DB::select('SELECT * FROM table')` - Pure raw SQL queries
- `DB::statement('UPDATE table SET...')` - Raw SQL statements

### 🔧 CORRECT Usage with Raw SQL:
```php
// Get table prefix
$prefix = DB::getTablePrefix();

// ✅ Query Builder (auto-prefixed) + Raw SQL (manual prefix)
DB::table('users')
  ->select('name', DB::raw("COUNT({$prefix}posts.id) as post_count"))
  ->join('posts', 'users.id', '=', 'posts.user_id')
  ->groupBy('users.id')

// ✅ Pure raw SQL (manual prefix everywhere)
DB::select("SELECT * FROM {$prefix}users WHERE {$prefix}users.active = 1")

// ❌ WRONG - Mixed auto/manual prefixing
DB::table($prefix . 'users') // Double prefix!
```

### 📋 **Real Example from Fixed Code:**
```php
private function getTopSellingProducts()
{
    $prefix = DB::getTablePrefix();

    return DB::table('sale_items')                    // ✅ Auto-prefixed
        ->join('finished_products', 'sale_items.finished_product_id', '=', 'finished_products.id') // ✅ Auto-prefixed
        ->select('finished_products.name',            // ✅ Auto-prefixed
                DB::raw("SUM({$prefix}sale_items.quantity) as total_sold"),     // ✅ Manual prefix in raw SQL
                DB::raw("SUM({$prefix}sale_items.line_total) as total_revenue")) // ✅ Manual prefix in raw SQL
        ->groupBy('finished_products.id', 'finished_products.name') // ✅ Auto-prefixed
        ->get();
}
```

## Testing Table Prefix Setup

### 1. Test Database Connection
```bash
php artisan tinker
>>> DB::getTablePrefix()
>>> DB::table('users')->count()
```

### 2. Test Migrations with Prefix
```bash
php artisan migrate:fresh --seed
```

### 3. Verify Tables Created with Prefix
```sql
SHOW TABLES;
-- Should show tables like: mfg_users, mfg_sales, etc.
```

## Migration Considerations

When using table prefixes:
1. **Foreign key names** might need adjustment for length limits
2. **Index names** should be shortened to avoid MySQL limits
3. **Existing data** needs migration if adding prefix to existing database

## Deployment with Table Prefix

### 1. Update Environment
```env
DB_PREFIX=mfg_
```

### 2. Run Migrations
```bash
php artisan migrate:fresh --seed --force
```

### 3. Clear Caches
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Summary

✅ **Fixed Issues:**
- DashboardController raw SQL queries now handle table prefixes
- Both `getTopSellingProducts()` and `getTopMaterials()` methods updated

✅ **Recommended Approach:**
- Create missing Eloquent models (SaleItem, ManufacturingConsumption)
- Replace raw SQL with Eloquent relationships
- Use Laravel's built-in table prefix handling

✅ **Configuration:**
- Set `DB_PREFIX` in environment variables
- Update database configuration
- Test thoroughly before production deployment
