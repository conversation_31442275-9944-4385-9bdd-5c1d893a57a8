/*
 * Recipe Management for Finished Products
 */

'use strict';

$(function () {
  // Recipe Management Variables
  var rawMaterialsData = [];
  var unitsData = [];
  var currentProductData = {};
  var recipeModal = $('#recipeModal');

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Load raw materials and units for recipe management
  function loadRecipeData() {
    return Promise.all([
      $.get(baseUrl + 'raw-materials/active'),
      $.get(baseUrl + 'units/active')
    ]).then(function(results) {
      rawMaterialsData = results[0];
      unitsData = results[1];
    });
  }

  // Add recipe item row
  function addRecipeItem(recipe = null) {
    var index = $('#recipe-items .recipe-item').length;
    var html = `
      <div class="recipe-item border rounded p-3 mb-3">
        <div class="row">
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <select class="form-select raw-material-select" name="recipes[${index}][raw_material_id]" required>
                <option value="">Select Raw Material</option>
                ${rawMaterialsData.map(material =>
                  `<option value="${material.id}" data-unit="${material.unit.symbol}" data-price="${material.unit_price || 0}" ${recipe && recipe.raw_material_id == material.id ? 'selected' : ''}>
                    ${material.name} (${material.current_stock} ${material.unit.symbol})
                  </option>`
                ).join('')}
              </select>
              <label>Raw Material</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control quantity-input"
                     name="recipes[${index}][quantity_required]"
                     value="${recipe ? recipe.quantity_required : ''}"
                     placeholder="0.000" required>
              <label>Quantity</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <select class="form-select unit-select" name="recipes[${index}][unit_id]" required>
                <option value="">Select Unit</option>
                ${unitsData.map(unit =>
                  `<option value="${unit.id}" ${recipe && recipe.unit_id == unit.id ? 'selected' : ''}>
                    ${unit.name} (${unit.symbol})
                  </option>`
                ).join('')}
              </select>
              <label>Unit</label>
            </div>
          </div>
          <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger btn-sm remove-recipe-item">
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
        </div>
        <div class="row mt-2">
          <div class="col-md-4">
            <small class="text-muted">Available: <span class="available-stock">-</span></small>
          </div>
          <div class="col-md-4">
            <small class="text-muted">Cost: $<span class="item-cost">0.00</span></small>
          </div>
          <div class="col-md-4">
            <span class="unit-conversion-info"></span>
          </div>
        </div>
      </div>
    `;

    $('#recipe-items').append(html);
    updateRecipeCosts();
  }

  // Remove recipe item
  $(document).on('click', '.remove-recipe-item', function() {
    $(this).closest('.recipe-item').remove();
    updateRecipeCosts();
  });

  // Update available stock when raw material changes
  $(document).on('change', '.raw-material-select', function() {
    var materialId = $(this).val();
    var $item = $(this).closest('.recipe-item');

    if (materialId) {
      var material = rawMaterialsData.find(m => m.id == materialId);
      if (material) {
        $item.find('.available-stock').text(material.current_stock + ' ' + material.unit.symbol);
        // Auto-select the material's unit by default, but allow user to change
        if (!$item.find('.unit-select').val()) {
          $item.find('.unit-select').val(material.unit_id);
        }

        // Update compatible units for this material
        updateCompatibleUnits($item, material);
      }
    } else {
      $item.find('.available-stock').text('-');
      $item.find('.unit-conversion-info').text('');
    }

    updateRecipeCosts();
    updateUnitConversionDisplay($item);
  });

  // Update costs when quantity changes
  $(document).on('input', '.quantity-input', function() {
    var $item = $(this).closest('.recipe-item');
    updateRecipeCosts();
    updateUnitConversionDisplay($item);
  });

  // Update unit conversion when unit changes
  $(document).on('change', '.unit-select', function() {
    var $item = $(this).closest('.recipe-item');
    updateRecipeCosts();
    updateUnitConversionDisplay($item);
  });

  // Update recipe costs
  function updateRecipeCosts() {
    var totalCost = 0;

    $('.recipe-item').each(function() {
      var $item = $(this);
      var materialId = $item.find('.raw-material-select').val();
      var quantity = parseFloat($item.find('.quantity-input').val()) || 0;
      var recipeUnitId = $item.find('.unit-select').val();

      if (materialId && quantity > 0 && recipeUnitId) {
        var material = rawMaterialsData.find(m => m.id == materialId);
        var recipeUnit = unitsData.find(u => u.id == recipeUnitId);

        if (material && material.unit_price && recipeUnit) {
          // Convert quantity to material's unit for cost calculation
          var convertedQuantity = quantity;

          if (material.unit_id != recipeUnitId) {
            convertedQuantity = convertUnits(quantity, recipeUnit, material.unit);
            if (convertedQuantity === null) {
              convertedQuantity = quantity; // Fallback if conversion fails
            }
          }

          var itemCost = convertedQuantity * material.unit_price;
          $item.find('.item-cost').text(window.formatCurrency ? window.formatCurrency(itemCost) : itemCost.toFixed(2));
          totalCost += itemCost;
        } else {
          $item.find('.item-cost').text(window.formatCurrency ? window.formatCurrency(0) : '0.00');
        }
      } else {
        $item.find('.item-cost').text(window.formatCurrency ? window.formatCurrency(0) : '0.00');
      }
    });

    $('#total-cost').text(window.formatCurrency ? window.formatCurrency(totalCost) : '$' + totalCost.toFixed(2));

    // Calculate profit margin
    var sellingPrice = currentProductData.selling_price || 0;
    var profitMargin = sellingPrice - totalCost;
    $('#profit-margin').text(window.formatCurrency ? window.formatCurrency(profitMargin) : '$' + profitMargin.toFixed(2));

    if (profitMargin > 0) {
      $('#profit-margin').removeClass('text-danger').addClass('text-success');
    } else if (profitMargin < 0) {
      $('#profit-margin').removeClass('text-success').addClass('text-danger');
    } else {
      $('#profit-margin').removeClass('text-success text-danger');
    }
  }

  // Manage Recipe
  $(document).on('click', '.manage-recipe', function() {
    var productId = $(this).data('id');
    var productName = $(this).data('name');

    $('#recipe-product-id').val(productId);
    $('#recipe-product-name').text(productName);
    $('#recipe-items').empty();

    // Load recipe data
    loadRecipeData().then(function() {
      $.get(`${baseUrl}finished-products/${productId}/recipe`, function(data) {
        currentProductData = data.product;

        if (data.recipes && data.recipes.length > 0) {
          data.recipes.forEach(function(recipe) {
            addRecipeItem(recipe);
          });
        } else {
          addRecipeItem(); // Add empty recipe item
        }

        recipeModal.modal('show');
      });
    });
  });

  // Add recipe item button
  $('#add-recipe-item').on('click', function() {
    addRecipeItem();
  });

  // Save recipe
  $('#save-recipe').on('click', function() {
    var productId = $('#recipe-product-id').val();
    var recipes = [];

    $('.recipe-item').each(function() {
      var $item = $(this);
      var materialId = $item.find('.raw-material-select').val();
      var quantity = $item.find('.quantity-input').val();
      var unitId = $item.find('.unit-select').val();

      if (materialId && quantity && unitId) {
        recipes.push({
          raw_material_id: materialId,
          quantity_required: quantity,
          unit_id: unitId
        });
      }
    });

    if (recipes.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'Warning!',
        text: 'Please add at least one ingredient to the recipe.',
        customClass: {
          confirmButton: 'btn btn-warning'
        }
      });
      return;
    }

    $.ajax({
      type: 'POST',
      url: `${baseUrl}finished-products/${productId}/recipe`,
      data: { recipes: recipes },
      success: function(response) {
        recipeModal.modal('hide');

        // Refresh the datatable if it exists
        if (typeof dt_finished_product !== 'undefined') {
          dt_finished_product.draw();
        }

        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function(xhr) {
        var response = xhr.responseJSON;
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: response.message || 'Error updating recipe',
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      }
    });
  });

  // Helper function to update compatible units for a material
  function updateCompatibleUnits($item, material) {
    var $unitSelect = $item.find('.unit-select');
    var currentValue = $unitSelect.val();

    // Filter units that are compatible with the material's unit
    var compatibleUnits = unitsData.filter(function(unit) {
      return unit.category_id === material.unit.category_id || unit.id === material.unit_id;
    });

    // Update unit options
    $unitSelect.empty();
    $unitSelect.append('<option value="">Select Unit</option>');

    compatibleUnits.forEach(function(unit) {
      var selected = unit.id == currentValue ? 'selected' : '';
      $unitSelect.append(`<option value="${unit.id}" ${selected}>${unit.name} (${unit.symbol})</option>`);
    });
  }

  // Helper function to update unit conversion display
  function updateUnitConversionDisplay($item) {
    var materialId = $item.find('.raw-material-select').val();
    var recipeUnitId = $item.find('.unit-select').val();
    var quantity = parseFloat($item.find('.quantity-input').val()) || 0;

    if (!materialId || !recipeUnitId || quantity === 0) {
      $item.find('.unit-conversion-info').text('');
      return;
    }

    var material = rawMaterialsData.find(m => m.id == materialId);
    var recipeUnit = unitsData.find(u => u.id == recipeUnitId);

    if (!material || !recipeUnit) {
      $item.find('.unit-conversion-info').text('');
      return;
    }

    // If units are the same, no conversion needed
    if (material.unit_id == recipeUnitId) {
      $item.find('.unit-conversion-info').text('');
      return;
    }

    // Check if units are compatible (same category)
    if (material.unit.category_id !== recipeUnit.category_id) {
      $item.find('.unit-conversion-info')
        .html('<small class="text-danger">⚠️ Units not compatible</small>');
      return;
    }

    // Calculate conversion
    var convertedQuantity = convertUnits(quantity, recipeUnit, material.unit);
    if (convertedQuantity !== null) {
      $item.find('.unit-conversion-info')
        .html(`<small class="text-info">📐 ${quantity} ${recipeUnit.symbol} = ${convertedQuantity.toFixed(3)} ${material.unit.symbol}</small>`);
    } else {
      $item.find('.unit-conversion-info')
        .html('<small class="text-warning">⚠️ Conversion not available</small>');
    }
  }

  // Helper function to convert between units
  function convertUnits(quantity, fromUnit, toUnit) {
    if (fromUnit.id === toUnit.id) {
      return quantity;
    }

    // Check if units are in the same category
    if (fromUnit.category_id !== toUnit.category_id) {
      return null;
    }

    // Convert through base unit
    // First convert to base unit
    var baseQuantity = quantity / fromUnit.conversion_factor;

    // Then convert from base unit to target unit
    var convertedQuantity = baseQuantity * toUnit.conversion_factor;

    return convertedQuantity;
  }

  // Initialize recipe data on page load
  loadRecipeData();
});
