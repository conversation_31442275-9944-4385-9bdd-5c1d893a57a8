/**
 * Purchases Management
 */

'use strict';

// Datatable (jquery)
$(function () {
  // Variable declaration for table
  var dt_purchases_table = $('.datatables-purchases'),
    offCanvasForm = $('#offcanvasAddPurchase'),
    purchaseDetailsModal = $('#purchaseDetailsModal');

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Initialize flatpickr for date pickers
  if (document.getElementById('add-purchase-date')) {
    flatpickr('#add-purchase-date', {
      dateFormat: 'Y-m-d',
      defaultDate: 'today'
    });
  }

  if (document.getElementById('add-expected-delivery')) {
    flatpickr('#add-expected-delivery', {
      dateFormat: 'Y-m-d',
      minDate: 'today'
    });
  }

  // Variables for purchase items management
  let purchaseItems = [];

  // Add purchase item functionality
  $('#add-purchase-item').on('click', function() {
    addPurchaseItem();
  });

  // Fee inputs change handler
  $('.fee-input').on('input', function() {
    calculateTotals();
  });

  // Load raw materials
  function loadRawMaterials() {
    $.get(baseUrl + 'purchases/raw-materials/active', function(data) {
      rawMaterials = data;
    }).fail(function() {
      console.error('Failed to load raw materials');
    });
  }

  // Add purchase item
  function addPurchaseItem() {
    const itemIndex = purchaseItems.length;
    const itemHtml = `
      <div class="purchase-item border rounded p-3 mb-3" data-index="${itemIndex}">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Item ${itemIndex + 1}</h6>
          <button type="button" class="btn btn-sm btn-outline-danger remove-item">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>

        <div class="row g-3">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <select class="form-select raw-material-select" name="items[${itemIndex}][raw_material_id]" required>
                <option value="">Select Raw Material</option>
                ${rawMaterials.map(material =>
                  `<option value="${material.id}" data-unit="${material.unit?.name || ''}">${material.name}</option>`
                ).join('')}
              </select>
              <label>Raw Material</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control quantity-input"
                     name="items[${itemIndex}][quantity]" placeholder="0.000" required min="0.001">
              <label>Quantity</label>
            </div>
            <small class="text-muted unit-display"></small>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.01" class="form-control price-input"
                     name="items[${itemIndex}][unit_price]" placeholder="0.00" required min="0">
              <label>Unit Price</label>
            </div>
          </div>
        </div>

        <div class="row g-3 mt-2">
          <div class="col-md-9">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control" name="items[${itemIndex}][notes]"
                        placeholder="Item notes..." rows="2"></textarea>
              <label>Notes</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="d-flex align-items-center h-100">
              <div class="text-end">
                <small class="text-muted">Line Total:</small>
                <div class="h6 mb-0 line-total">0.00</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    $('#purchase-items-container').append(itemHtml);
    purchaseItems.push({
      raw_material_id: '',
      quantity: 0,
      unit_price: 0,
      notes: ''
    });

    // Update item indices
    updateItemIndices();
  }

  // Remove purchase item
  $(document).on('click', '.remove-item', function() {
    const $item = $(this).closest('.purchase-item');
    const index = parseInt($item.data('index'));

    $item.remove();
    purchaseItems.splice(index, 1);

    updateItemIndices();
    calculateTotals();
  });

  // Raw material selection change
  $(document).on('change', '.raw-material-select', function() {
    const $select = $(this);
    const $item = $select.closest('.purchase-item');
    const selectedOption = $select.find('option:selected');
    const unit = selectedOption.data('unit') || '';

    $item.find('.unit-display').text(unit ? `Unit: ${unit}` : '');
  });

  // Quantity and price input changes
  $(document).on('input', '.quantity-input, .price-input', function() {
    const $item = $(this).closest('.purchase-item');
    calculateLineTotal($item);
    calculateTotals();
  });

  // Calculate line total for an item
  function calculateLineTotal($item) {
    const quantity = parseFloat($item.find('.quantity-input').val()) || 0;
    const price = parseFloat($item.find('.price-input').val()) || 0;
    const lineTotal = quantity * price;

    $item.find('.line-total').text(lineTotal.toFixed(2));
  }

  // Calculate totals
  function calculateTotals() {
    let subtotal = 0;

    $('.purchase-item').each(function() {
      const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
      const price = parseFloat($(this).find('.price-input').val()) || 0;
      subtotal += quantity * price;
    });

    const deliveryFee = parseFloat($('#add-delivery-fee').val()) || 0;
    const handlingFee = parseFloat($('#add-handling-fee').val()) || 0;
    const otherFees = parseFloat($('#add-other-fees').val()) || 0;
    const totalFees = deliveryFee + handlingFee + otherFees;
    const total = subtotal + totalFees;

    $('#purchase-subtotal').text(subtotal.toFixed(2));
    $('#purchase-fees').text(totalFees.toFixed(2));
    $('#purchase-total').text(total.toFixed(2));
  }

  // Update item indices after adding/removing items
  function updateItemIndices() {
    $('.purchase-item').each(function(index) {
      $(this).attr('data-index', index);
      $(this).find('h6').text(`Item ${index + 1}`);

      // Update input names
      $(this).find('select[name*="raw_material_id"]').attr('name', `items[${index}][raw_material_id]`);
      $(this).find('input[name*="quantity"]').attr('name', `items[${index}][quantity]`);
      $(this).find('input[name*="unit_price"]').attr('name', `items[${index}][unit_price]`);
      $(this).find('textarea[name*="notes"]').attr('name', `items[${index}][notes]`);
    });
  }

  if (document.getElementById('actual-delivery-date')) {
    flatpickr('#actual-delivery-date', {
      dateFormat: 'Y-m-d',
      defaultDate: 'today'
    });
  }

  // Load raw materials for dropdown
  var rawMaterials = [];
  function loadRawMaterials() {
    $.get(baseUrl + 'purchases/raw-materials/active', function(data) {
      rawMaterials = data;
    });
  }

  // Load supplier suggestions
  function loadSupplierSuggestions(searchTerm = '') {
    return $.get(baseUrl + 'purchases/suppliers/suggestions', { search: searchTerm });
  }

  // Load materials on page load
  loadRawMaterials();

  // Supplier name autocomplete
  $('#add-supplier-name').on('input', function() {
    var searchTerm = $(this).val();
    if (searchTerm.length >= 2) {
      loadSupplierSuggestions(searchTerm).done(function(suppliers) {
        // You can implement autocomplete dropdown here
        console.log('Suppliers:', suppliers);
      });
    }
  });

  // Purchases datatable
  if (dt_purchases_table.length) {
    var dt_purchases = dt_purchases_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: baseUrl + 'purchases/data'
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'purchase_number' },
        { data: 'supplier_name' },
        { data: 'purchase_date' },
        { data: 'expected_delivery_date' },
        { data: 'total_amount' },
        { data: 'status' },
        { data: 'action' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          // Purchase number with overdue indicator
          targets: 1,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $purchaseNumber = full['purchase_number'];
            var $isOverdue = full['is_overdue'];

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $purchaseNumber + '</span>' +
              ($isOverdue ? '<small class="text-danger"><i class="ri-alarm-warning-line"></i> Overdue</small>' : '') +
              '</div>';
          }
        },
        {
          // Supplier info
          targets: 2,
          render: function (data, type, full, meta) {
            var $supplier = full['supplier_name'];
            var $contact = full['supplier_contact'];

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $supplier + '</span>' +
              ($contact ? '<small class="text-muted">' + $contact + '</small>' : '') +
              '</div>';
          }
        },
        {
          // Purchase date
          targets: 3,
          render: function (data, type, full, meta) {
            return full['formatted_purchase_date'];
          }
        },
        {
          // Expected delivery date
          targets: 4,
          render: function (data, type, full, meta) {
            var $expectedDate = full['formatted_expected_delivery'];
            var $isOverdue = full['is_overdue'];

            return '<div class="d-flex flex-column">' +
              '<span class="' + ($isOverdue ? 'text-danger' : '') + '">' + $expectedDate + '</span>' +
              '</div>';
          }
        },
        {
          // Total amount with fees breakdown
          targets: 5,
          render: function (data, type, full, meta) {
            var $total = window.formatCurrency ? window.formatCurrency(full['total_amount']) : '$' + parseFloat(full['total_amount']).toFixed(2);
            var $subtotal = window.formatCurrency ? window.formatCurrency(full['subtotal']) : '$' + parseFloat(full['subtotal']).toFixed(2);
            var $fees = window.formatCurrency ? window.formatCurrency(full['total_fees']) : '$' + parseFloat(full['total_fees']).toFixed(2);

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $total + '</span>' +
              '<small class="text-muted">Subtotal: ' + $subtotal + '</small>' +
              (parseFloat(full['total_fees']) > 0 ? '<small class="text-muted">Fees: ' + $fees + '</small>' : '') +
              '</div>';
          }
        },
        {
          // Status with action indicators
          targets: 6,
          className: 'text-center',
          render: function (data, type, full, meta) {
            var $status = full['status'];
            var $statusLabel = full['status_label'];
            var $statusColor = full['status_color'];
            var $canReceive = full['can_receive'];
            var $canComplete = full['can_complete'];

            var statusBadge = '<span class="badge bg-label-' + $statusColor + '">' + $statusLabel + '</span>';

            if ($canReceive) {
              statusBadge += '<br><small class="text-success"><i class="ri-truck-line"></i> Can Receive</small>';
            }
            if ($canComplete) {
              statusBadge += '<br><small class="text-warning"><i class="ri-check-circle-line"></i> Can Complete</small>';
            }

            return statusBadge;
          }
        },
        {
          // Actions
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            var actions = '<div class="d-flex align-items-center gap-50">';

            // Edit button (only for pending purchases)
            if (full['status'] === 'pending') {
              actions += `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddPurchase"><i class="ri-edit-box-line ri-20px"></i></button>`;
            }

            // Delete button (only for pending purchases)
            if (full['status'] === 'pending') {
              actions += `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}"><i class="ri-delete-bin-7-line ri-20px"></i></button>`;
            }

            // More actions dropdown
            actions += '<button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ri-more-2-line ri-20px"></i></button>';
            actions += '<div class="dropdown-menu dropdown-menu-end m-0">';

            // View details
            actions += `<a href="javascript:;" class="dropdown-item view-details" data-id="${full['id']}">View Details</a>`;

            // Update status
            if (full['status'] === 'pending') {
              actions += `<a href="javascript:;" class="dropdown-item update-status" data-id="${full['id']}" data-status="ordered">Mark as Ordered</a>`;
            }
            if (full['status'] === 'ordered') {
              actions += `<a href="javascript:;" class="dropdown-item update-status" data-id="${full['id']}" data-status="shipped">Mark as Shipped</a>`;
            }

            // Receive items (if can receive)
            if (full['can_receive']) {
              actions += `<a href="javascript:;" class="dropdown-item receive-items" data-id="${full['id']}" data-number="${full['purchase_number']}">Receive Items</a>`;
            }

            // Complete purchase (if can complete)
            if (full['can_complete']) {
              actions += `<a href="javascript:;" class="dropdown-item complete-purchase" data-id="${full['id']}">Complete Purchase</a>`;
            }

            // Cancel purchase (if not completed)
            if (full['can_cancel']) {
              actions += `<a href="javascript:;" class="dropdown-item cancel-purchase" data-id="${full['id']}">Cancel Purchase</a>`;
            }

            actions += '</div></div>';

            return actions;
          }
        }
      ],
      order: [[1, 'desc']],
      dom:
        '<"card-header d-flex rounded-0 flex-wrap pb-md-0 pt-0"' +
        '<"me-5 ms-n2"f>' +
        '<"d-flex justify-content-start justify-content-md-end align-items-baseline"<"dt-action-buttons d-flex align-items-start align-items-md-center justify-content-sm-center gap-4"lB>>' +
        '>t' +
        '<"row mx-1"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 20, 50, 70, 100],
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: 'Search Purchases',
        info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
        paginate: {
          next: '<i class="ri-arrow-right-s-line"></i>',
          previous: '<i class="ri-arrow-left-s-line"></i>'
        }
      },
      // Buttons with Dropdown
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-outline-secondary dropdown-toggle me-4 waves-effect waves-light',
          text: '<i class="ri-upload-2-line ri-16px me-2"></i><span class="d-none d-sm-inline-block">Export </span>',
          buttons: [
            {
              extend: 'print',
              title: 'Purchases',
              text: '<i class="ri-printer-line me-1" ></i>Print',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            },
            {
              extend: 'csv',
              title: 'Purchases',
              text: '<i class="ri-file-text-line me-1" ></i>Csv',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            },
            {
              extend: 'excel',
              title: 'Purchases',
              text: '<i class="ri-file-excel-line me-1"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            },
            {
              extend: 'pdf',
              title: 'Purchases',
              text: '<i class="ri-file-pdf-line me-1"></i>Pdf',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            }
          ]
        },
        {
          text: '<i class="ri-add-line ri-16px me-0 me-sm-2 align-baseline"></i><span class="d-none d-sm-inline-block">Add New Purchase</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddPurchase'
          }
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Details of ' + data['purchase_number'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== ''
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }
})
