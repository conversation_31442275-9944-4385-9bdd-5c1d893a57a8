<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manufacturing Order #{{ $order->order_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .order-number {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-box {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .info-box h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            color: #333;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status.planned { background-color: #e3f2fd; color: #1976d2; }
        .status.in_progress { background-color: #fff3e0; color: #f57c00; }
        .status.completed { background-color: #e8f5e8; color: #388e3c; }
        .status.cancelled { background-color: #ffebee; color: #d32f2f; }
        
        .products-section {
            margin-bottom: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
        }
        .table td {
            font-size: 11px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .consumption-section {
            margin-top: 30px;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        .signature-box {
            text-align: center;
            border: 1px solid #ddd;
            padding: 40px 10px 10px 10px;
            border-radius: 5px;
        }
        .signature-label {
            font-weight: bold;
            color: #555;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">🖨️ Print</button>

    <div class="header">
        <div class="company-name">{{ config('app.name', 'Recipe Management System') }}</div>
        <div class="document-title">Manufacturing Order</div>
        <div class="order-number">#{{ $order->order_number }}</div>
    </div>

    <div class="info-grid">
        <div class="info-box">
            <h3>Order Information</h3>
            <div class="info-row">
                <span class="info-label">Order Type:</span>
                <span class="info-value">{{ ucfirst($order->order_type) }} Product</span>
            </div>
            <div class="info-row">
                <span class="info-label">Status:</span>
                <span class="status {{ $order->status }}">{{ $order->status_label }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Planned Date:</span>
                <span class="info-value">{{ $order->planned_date->format('M d, Y') }}</span>
            </div>
            @if($order->started_at)
            <div class="info-row">
                <span class="info-label">Started:</span>
                <span class="info-value">{{ $order->started_at->format('M d, Y H:i') }}</span>
            </div>
            @endif
            @if($order->completed_at)
            <div class="info-row">
                <span class="info-label">Completed:</span>
                <span class="info-value">{{ $order->completed_at->format('M d, Y H:i') }}</span>
            </div>
            @endif
        </div>

        <div class="info-box">
            <h3>Production Details</h3>
            <div class="info-row">
                <span class="info-label">Responsible Person:</span>
                <span class="info-value">{{ $order->responsible_person }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Created By:</span>
                <span class="info-value">{{ $order->createdBy->name ?? 'N/A' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Estimated Time:</span>
                <span class="info-value">
                    @if($order->isMultiProduct())
                        {{ $order->total_estimated_time_minutes ?? 0 }} minutes
                    @else
                        {{ $order->estimated_time_minutes ?? 0 }} minutes
                    @endif
                </span>
            </div>
            @if($order->status === 'completed')
            <div class="info-row">
                <span class="info-label">Actual Time:</span>
                <span class="info-value">
                    @if($order->isMultiProduct())
                        {{ $order->total_actual_time_minutes ?? 0 }} minutes
                    @else
                        {{ $order->actual_time_minutes ?? 0 }} minutes
                    @endif
                </span>
            </div>
            @endif
        </div>
    </div>

    @if($order->notes)
    <div class="info-section">
        <div class="info-box">
            <h3>Notes</h3>
            <p>{{ $order->notes }}</p>
        </div>
    </div>
    @endif

    <div class="products-section">
        <h3>Products to Manufacture</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>Product Name</th>
                    <th class="text-center">Planned Qty</th>
                    @if($order->status === 'completed')
                    <th class="text-center">Produced Qty</th>
                    @endif
                    <th class="text-center">Unit</th>
                    <th class="text-right">Est. Cost</th>
                    @if($order->status === 'completed')
                    <th class="text-right">Actual Cost</th>
                    @endif
                </tr>
            </thead>
            <tbody>
                @if($order->isMultiProduct())
                    @foreach($order->items as $item)
                    <tr>
                        <td>{{ $item->finishedProduct->name }}</td>
                        <td class="text-center">{{ number_format($item->planned_quantity, 3) }}</td>
                        @if($order->status === 'completed')
                        <td class="text-center">{{ number_format($item->produced_quantity, 3) }}</td>
                        @endif
                        <td class="text-center">{{ $item->finishedProduct->unit->symbol ?? '' }}</td>
                        <td class="text-right">{{ number_format($item->estimated_cost ?? 0, 2) }}</td>
                        @if($order->status === 'completed')
                        <td class="text-right">{{ number_format($item->actual_cost ?? 0, 2) }}</td>
                        @endif
                    </tr>
                    @endforeach
                    <tr style="font-weight: bold; background-color: #f9f9f9;">
                        <td>TOTAL</td>
                        <td class="text-center">{{ number_format($order->total_planned_quantity, 3) }}</td>
                        @if($order->status === 'completed')
                        <td class="text-center">{{ number_format($order->total_produced_quantity, 3) }}</td>
                        @endif
                        <td class="text-center">-</td>
                        <td class="text-right">{{ number_format($order->total_estimated_cost ?? 0, 2) }}</td>
                        @if($order->status === 'completed')
                        <td class="text-right">{{ number_format($order->total_actual_cost ?? 0, 2) }}</td>
                        @endif
                    </tr>
                @else
                    <tr>
                        <td>{{ $order->finishedProduct->name }}</td>
                        <td class="text-center">{{ number_format($order->planned_quantity, 3) }}</td>
                        @if($order->status === 'completed')
                        <td class="text-center">{{ number_format($order->produced_quantity, 3) }}</td>
                        @endif
                        <td class="text-center">{{ $order->finishedProduct->unit->symbol ?? '' }}</td>
                        <td class="text-right">{{ number_format($order->estimated_cost ?? 0, 2) }}</td>
                        @if($order->status === 'completed')
                        <td class="text-right">{{ number_format($order->actual_cost ?? 0, 2) }}</td>
                        @endif
                    </tr>
                @endif
            </tbody>
        </table>
    </div>

    @if($order->consumptions->count() > 0)
    <div class="consumption-section">
        <h3>Material Consumption</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>Raw Material</th>
                    <th class="text-center">Planned Qty</th>
                    @if($order->status === 'completed')
                    <th class="text-center">Actual Qty</th>
                    @endif
                    <th class="text-center">Unit</th>
                    <th class="text-right">Unit Cost</th>
                    <th class="text-right">Total Cost</th>
                </tr>
            </thead>
            <tbody>
                @foreach($order->consumptions as $consumption)
                <tr>
                    <td>{{ $consumption->rawMaterial->name }}</td>
                    <td class="text-center">{{ number_format($consumption->planned_quantity, 3) }}</td>
                    @if($order->status === 'completed')
                    <td class="text-center">{{ number_format($consumption->actual_quantity, 3) }}</td>
                    @endif
                    <td class="text-center">{{ $consumption->rawMaterial->unit->symbol ?? '' }}</td>
                    <td class="text-right">{{ number_format($consumption->unit_cost, 2) }}</td>
                    <td class="text-right">{{ number_format($consumption->total_cost, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <div class="signature-box">
            <div class="signature-label">Prepared By</div>
        </div>
        <div class="signature-box">
            <div class="signature-label">Approved By</div>
        </div>
        <div class="signature-box">
            <div class="signature-label">Completed By</div>
        </div>
    </div>

    <script>
        // Auto-focus for better printing experience
        window.addEventListener('load', function() {
            document.body.focus();
        });
    </script>
</body>
</html>
