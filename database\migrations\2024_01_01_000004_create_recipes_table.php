<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recipes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('finished_product_id');
            $table->unsignedBigInteger('raw_material_id');
            $table->decimal('quantity_required', 10, 3); // Quantity of raw material needed
            $table->unsignedBigInteger('unit_id'); // Unit for the quantity
            $table->integer('sort_order')->default(0); // For drag-and-drop ordering
            $table->timestamps();

            // Foreign key constraints with custom short names
            $table->foreign('finished_product_id', 'recipes_product_fk')->references('id')->on('finished_products')->onDelete('cascade');
            $table->foreign('raw_material_id', 'recipes_material_fk')->references('id')->on('raw_materials')->onDelete('cascade');
            $table->foreign('unit_id', 'recipes_unit_fk')->references('id')->on('units')->onDelete('restrict');

            // Composite unique key to prevent duplicate entries
            $table->unique(['finished_product_id', 'raw_material_id'], 'recipes_product_material_unique');

            // Indexes for performance
            $table->index('finished_product_id');
            $table->index('raw_material_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recipes');
    }
};
