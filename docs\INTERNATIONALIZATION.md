# Internationalization (i18n) Implementation

This document explains the comprehensive internationalization system implemented for the Manufacturing Application, supporting English, French, and Arabic languages.

## Overview

The application now supports dynamic language switching with:
- **English (en)** - Default language
- **French (fr)** - Complete translation
- **Arabic (ar)** - Complete translation with RTL support

## System Architecture

### Language Files Structure
```
lang/
├── en.json          # English translations
├── fr.json          # French translations  
├── ar.json          # Arabic translations
└── en/              # Laravel default files
    ├── auth.php
    ├── pagination.php
    ├── passwords.php
    └── validation.php
```

### Middleware & Controllers
- **LocaleMiddleware**: Automatically sets locale from session
- **LanguageController**: Handles language switching
- **MenuServiceProvider**: Dynamic menu translation

## Language Switching

### Routes
```php
Route::get('/lang/{locale}', [LanguageController::class, 'swap']);
```

### Available Languages
- `/lang/en` - Switch to English
- `/lang/fr` - Switch to French  
- `/lang/ar` - Switch to Arabic

### Language Switcher Component
Located in `resources/views/layouts/sections/navbar/navbar.blade.php`

## Translation Coverage

### 1. Menu System
All menu items are dynamically translated:
```php
'name' => __('Manufacturing'),
'submenu' => [
    ['name' => __('Units')],
    ['name' => __('Raw Materials')],
    ['name' => __('Finished Products')],
    // ...
]
```

### 2. Authentication Views
- Login form labels and placeholders
- Welcome messages
- Form validation messages

### 3. Manufacturing Modules

#### Units Management
- Page titles and headers
- Statistics cards
- Form labels and buttons
- Table headers
- Status indicators

#### Raw Materials
- Inventory statistics
- Stock level indicators
- Supplier information
- Form elements

#### Finished Products
- Product information
- Recipe management
- Pricing details

#### Manufacturing Orders
- Order status tracking
- Production workflow
- Timeline indicators

#### Purchases
- Purchase order management
- Supplier interactions
- Delivery tracking

### 4. Dashboard Elements
- Welcome messages
- Statistics cards
- Chart titles
- Activity indicators

## Translation Keys

### Common Elements
```json
{
  "Dashboard": "Dashboard",
  "Manufacturing": "Manufacturing", 
  "Units": "Units",
  "Raw Materials": "Raw Materials",
  "Finished Products": "Finished Products",
  "Manufacturing Orders": "Manufacturing Orders",
  "Purchases": "Purchases",
  "Point of Sale": "Point of Sale"
}
```

### Form Elements
```json
{
  "Unit Name": "Unit Name",
  "Symbol": "Symbol", 
  "Description": "Description",
  "Status": "Status",
  "Actions": "Actions",
  "Active": "Active",
  "Inactive": "Inactive",
  "Submit": "Submit",
  "Cancel": "Cancel"
}
```

### Statistics & Metrics
```json
{
  "Total Units": "Total Units",
  "Active Units": "Active Units", 
  "Inactive Units": "Inactive Units",
  "Unit Types": "Unit Types",
  "All measurement units": "All measurement units",
  "Currently in use": "Currently in use",
  "Not currently used": "Not currently used"
}
```

## RTL Support for Arabic

### Automatic Direction Detection
The system automatically detects Arabic locale and applies RTL styling:

```javascript
// In main.js
function directionChange(textDirection) {
  if (textDirection === 'rtl') {
    window.templateCustomizer.setRtl(true);
  } else {
    window.templateCustomizer.setRtl(false);
  }
}
```

### CSS RTL Support
The template includes RTL-specific stylesheets that are automatically loaded for Arabic.

## Usage in Views

### Basic Translation
```blade
<h1>{{ __('Dashboard') }}</h1>
<p>{{ __('Welcome to the application') }}</p>
```

### Form Labels
```blade
<label for="name">{{ __('Unit Name') }}</label>
<input type="text" placeholder="{{ __('Enter unit name') }}">
```

### Dynamic Content
```blade
<h5>{{ __('Total Units') }}: {{ $totalUnits }}</h5>
<small>{{ __('Currently in use') }}</small>
```

### Table Headers
```blade
<th>{{ __('Unit Name') }}</th>
<th>{{ __('Symbol') }}</th>
<th>{{ __('Status') }}</th>
<th>{{ __('Actions') }}</th>
```

## Adding New Translations

### 1. Add to Language Files
Add the new key-value pair to all three language files:

**en.json**
```json
{
  "New Feature": "New Feature"
}
```

**fr.json**
```json
{
  "New Feature": "Nouvelle fonctionnalité"
}
```

**ar.json**
```json
{
  "New Feature": "ميزة جديدة"
}
```

### 2. Use in Views
```blade
<h1>{{ __('New Feature') }}</h1>
```

## Testing Translations

### Manual Testing
1. Navigate to `/lang/en` to switch to English
2. Navigate to `/lang/fr` to switch to French
3. Navigate to `/lang/ar` to switch to Arabic
4. Verify all text is properly translated

### Automated Testing
```bash
# Check for missing translations
php artisan views:translate --dry-run
```

## Best Practices

### 1. Consistent Key Naming
- Use descriptive, hierarchical keys
- Keep keys in English for consistency
- Use sentence case for readability

### 2. Context-Aware Translations
- Consider gender and plural forms
- Account for cultural differences
- Test with native speakers

### 3. RTL Considerations
- Test layout with Arabic text
- Ensure icons and images are appropriate
- Verify form layouts work correctly

### 4. Performance
- Use JSON files for better performance
- Cache translations in production
- Minimize translation file size

## Maintenance

### Regular Updates
1. **Review new features** for translation needs
2. **Update language files** when adding new text
3. **Test all languages** after major changes
4. **Validate RTL layout** for Arabic

### Quality Assurance
- Native speaker review for accuracy
- Cultural appropriateness check
- Technical terminology verification
- User experience testing

## Troubleshooting

### Common Issues

**Translation not showing:**
```bash
# Clear cache
php artisan cache:clear
php artisan view:clear
```

**RTL not working:**
```bash
# Check if Arabic is properly detected
# Verify templateCustomizer is loaded
# Check CSS RTL files are included
```

**Missing translations:**
```bash
# Check language files for the key
# Verify __() function is used correctly
# Ensure locale is set properly
```

## Future Enhancements

### Planned Features
1. **Database translations** for dynamic content
2. **Translation management interface** for non-developers
3. **Automatic translation detection** for missing keys
4. **Export/import tools** for translation files
5. **Pluralization support** for complex grammar rules

### Additional Languages
The system is designed to easily support additional languages by:
1. Adding new JSON files (e.g., `de.json` for German)
2. Updating the language switcher component
3. Adding the locale to middleware validation
4. Testing the new language thoroughly

This internationalization system provides a solid foundation for a truly global manufacturing application with comprehensive language support.
