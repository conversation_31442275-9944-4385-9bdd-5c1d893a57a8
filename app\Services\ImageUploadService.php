<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImageUploadService
{
    /**
     * Upload and process an image.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param array $options
     * @return array
     */
    public function uploadImage(UploadedFile $file, string $directory, array $options = []): array
    {
        // Default options
        $options = array_merge([
            'max_width' => 800,
            'max_height' => 800,
            'quality' => 85,
            'create_thumbnail' => true,
            'thumbnail_width' => 200,
            'thumbnail_height' => 200,
        ], $options);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        $relativePath = $directory . '/' . $filename;
        $fullPath = public_path('uploads/' . $relativePath);

        // Ensure directory exists
        $this->ensureDirectoryExists(dirname($fullPath));

        // Process and save the main image
        $this->processAndSaveImage($file->getPathname(), $fullPath, $options['max_width'], $options['max_height'], $options['quality']);

        $result = [
            'path' => 'uploads/' . $relativePath,
            'filename' => $filename,
            'size' => filesize($fullPath),
            'mime_type' => $file->getMimeType(),
        ];

        // Create thumbnail if requested
        if ($options['create_thumbnail']) {
            $thumbnailPath = $this->createThumbnail(
                $fullPath,
                $directory,
                $filename,
                $options['thumbnail_width'],
                $options['thumbnail_height']
            );
            $result['thumbnail_path'] = $thumbnailPath;
        }

        return $result;
    }

    /**
     * Create a thumbnail image.
     *
     * @param string $originalPath
     * @param string $directory
     * @param string $filename
     * @param int $width
     * @param int $height
     * @return string
     */
    protected function createThumbnail(string $originalPath, string $directory, string $filename, int $width, int $height): string
    {
        $thumbnailFilename = 'thumb_' . $filename;
        $thumbnailRelativePath = $directory . '/' . $thumbnailFilename;
        $thumbnailFullPath = public_path('uploads/' . $thumbnailRelativePath);

        $this->processAndSaveImage($originalPath, $thumbnailFullPath, $width, $height, 85, true);

        return 'uploads/' . $thumbnailRelativePath;
    }

    /**
     * Delete an image and its thumbnail.
     *
     * @param string|null $imagePath
     * @return bool
     */
    public function deleteImage(?string $imagePath): bool
    {
        if (!$imagePath) {
            return true;
        }

        $deleted = true;

        // Delete main image
        $fullPath = public_path($imagePath);
        if (file_exists($fullPath)) {
            $deleted = unlink($fullPath);
        }

        // Delete thumbnail
        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];
        $thumbnailFullPath = public_path($thumbnailPath);
        if (file_exists($thumbnailFullPath)) {
            unlink($thumbnailFullPath);
        }

        return $deleted;
    }

    /**
     * Generate a unique filename for the uploaded file.
     *
     * @param UploadedFile $file
     * @return string
     */
    protected function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME));
        $timestamp = time();
        $random = Str::random(8);

        return "{$name}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Ensure the directory exists.
     *
     * @param string $directory
     * @return void
     */
    protected function ensureDirectoryExists(string $directory): void
    {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }

    /**
     * Validate image file.
     *
     * @param UploadedFile $file
     * @return array
     */
    public function validateImage(UploadedFile $file): array
    {
        $errors = [];

        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            $errors[] = 'Image size must be less than 5MB';
        }

        // Check file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = 'Image must be JPEG, PNG, GIF, or WebP format';
        }

        // Check image dimensions
        if ($file->getMimeType() && str_starts_with($file->getMimeType(), 'image/')) {
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo) {
                [$width, $height] = $imageInfo;
                if ($width < 100 || $height < 100) {
                    $errors[] = 'Image dimensions must be at least 100x100 pixels';
                }
                if ($width > 4000 || $height > 4000) {
                    $errors[] = 'Image dimensions must not exceed 4000x4000 pixels';
                }
            }
        }

        return $errors;
    }

    /**
     * Process and save image using GD library.
     *
     * @param string $sourcePath
     * @param string $destinationPath
     * @param int $maxWidth
     * @param int $maxHeight
     * @param int $quality
     * @param bool $crop
     * @return void
     */
    protected function processAndSaveImage(string $sourcePath, string $destinationPath, int $maxWidth, int $maxHeight, int $quality = 85, bool $crop = false): void
    {
        // Get image info
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            throw new \Exception('Invalid image file');
        }

        [$originalWidth, $originalHeight, $imageType] = $imageInfo;

        // Create image resource from source
        $sourceImage = match($imageType) {
            IMAGETYPE_JPEG => imagecreatefromjpeg($sourcePath),
            IMAGETYPE_PNG => imagecreatefrompng($sourcePath),
            IMAGETYPE_GIF => imagecreatefromgif($sourcePath),
            IMAGETYPE_WEBP => imagecreatefromwebp($sourcePath),
            default => throw new \Exception('Unsupported image type')
        };

        if (!$sourceImage) {
            throw new \Exception('Failed to create image resource');
        }

        // Calculate new dimensions
        if ($crop) {
            // For thumbnails, crop to exact dimensions
            $newWidth = $maxWidth;
            $newHeight = $maxHeight;

            // Calculate crop area to maintain aspect ratio
            $sourceRatio = $originalWidth / $originalHeight;
            $targetRatio = $maxWidth / $maxHeight;

            if ($sourceRatio > $targetRatio) {
                // Source is wider, crop width
                $cropWidth = $originalHeight * $targetRatio;
                $cropHeight = $originalHeight;
                $cropX = ($originalWidth - $cropWidth) / 2;
                $cropY = 0;
            } else {
                // Source is taller, crop height
                $cropWidth = $originalWidth;
                $cropHeight = $originalWidth / $targetRatio;
                $cropX = 0;
                $cropY = ($originalHeight - $cropHeight) / 2;
            }
        } else {
            // For main images, resize maintaining aspect ratio
            $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);

            if ($ratio >= 1) {
                // Don't upscale
                $newWidth = $originalWidth;
                $newHeight = $originalHeight;
                $cropX = 0;
                $cropY = 0;
                $cropWidth = $originalWidth;
                $cropHeight = $originalHeight;
            } else {
                $newWidth = (int)($originalWidth * $ratio);
                $newHeight = (int)($originalHeight * $ratio);
                $cropX = 0;
                $cropY = 0;
                $cropWidth = $originalWidth;
                $cropHeight = $originalHeight;
            }
        }

        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG and GIF
        if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefill($newImage, 0, 0, $transparent);
        }

        // Copy and resize
        imagecopyresampled(
            $newImage, $sourceImage,
            0, 0, (int)$cropX, (int)$cropY,
            $newWidth, $newHeight, (int)$cropWidth, (int)$cropHeight
        );

        // Save the image
        $extension = strtolower(pathinfo($destinationPath, PATHINFO_EXTENSION));

        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                imagejpeg($newImage, $destinationPath, $quality);
                break;
            case 'png':
                // PNG quality is 0-9, convert from 0-100
                $pngQuality = (int)(9 - ($quality / 100) * 9);
                imagepng($newImage, $destinationPath, $pngQuality);
                break;
            case 'gif':
                imagegif($newImage, $destinationPath);
                break;
            case 'webp':
                imagewebp($newImage, $destinationPath, $quality);
                break;
            default:
                // Default to JPEG
                imagejpeg($newImage, $destinationPath, $quality);
        }

        // Clean up memory
        imagedestroy($sourceImage);
        imagedestroy($newImage);
    }
}
