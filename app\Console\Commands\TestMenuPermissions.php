<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class TestMenuPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'menu:test-permissions {role?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test menu permissions for different roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $roleName = $this->argument('role');
        
        if (!$roleName) {
            $this->info('Available roles:');
            $roles = Role::all();
            foreach ($roles as $role) {
                $this->line("- {$role->name}");
            }
            $this->info("\nUsage: php artisan menu:test-permissions \"Role Name\"");
            return;
        }

        $role = Role::where('name', $roleName)->first();
        if (!$role) {
            $this->error("Role '{$roleName}' not found!");
            return;
        }

        $user = User::first();
        if (!$user) {
            $this->error('No users found in database!');
            return;
        }

        // Assign the role to the user
        $user->syncRoles([$roleName]);
        
        $this->info("Assigned role '{$roleName}' to user: {$user->name}");
        $this->info("User permissions:");
        
        $permissions = $user->getAllPermissions();
        foreach ($permissions as $permission) {
            $this->line("- {$permission->name}");
        }
        
        $this->info("\nMenu items that will be visible:");
        $this->testMenuVisibility($user);
    }

    private function testMenuVisibility($user)
    {
        // Simulate the menu filtering logic
        $menuItems = [
            'Dashboard' => null,
            'Manufacturing' => [
                'Units' => 'view-units',
                'Raw Materials' => 'view-raw-materials',
                'Finished Products' => 'view-finished-products',
                'Manufacturing Orders' => 'view-manufacturing-orders',
                'Purchases' => 'view-purchases',
            ],
            'Point of Sale' => 'access-pos',
            'Users' => 'view-users',
            'Roles & Permissions' => 'view-roles',
        ];

        foreach ($menuItems as $menuName => $permission) {
            if (is_array($permission)) {
                // Submenu
                $visibleSubItems = [];
                foreach ($permission as $subName => $subPermission) {
                    if (!$subPermission || $user->can($subPermission)) {
                        $visibleSubItems[] = $subName;
                    }
                }
                if (!empty($visibleSubItems)) {
                    $this->line("✓ {$menuName}");
                    foreach ($visibleSubItems as $subItem) {
                        $this->line("  - {$subItem}");
                    }
                }
            } else {
                // Single menu item
                if (!$permission || $user->can($permission)) {
                    $this->line("✓ {$menuName}");
                }
            }
        }
    }
}
