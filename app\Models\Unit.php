<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Unit extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'symbol',
        'description',
        'category_id',
        'conversion_factor',
        'is_base_unit',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'conversion_factor' => 'decimal:6',
        'is_base_unit' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the raw materials that use this unit.
     */
    public function rawMaterials(): HasMany
    {
        return $this->hasMany(RawMaterial::class);
    }

    /**
     * Get the finished products that use this unit.
     */
    public function finishedProducts(): HasMany
    {
        return $this->hasMany(FinishedProduct::class);
    }

    /**
     * Get the recipes that use this unit.
     */
    public function recipes(): HasMany
    {
        return $this->hasMany(Recipe::class);
    }

    /**
     * Get the category that this unit belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(UnitCategory::class, 'category_id');
    }

    /**
     * Get conversions from this unit to other units.
     */
    public function conversionsFrom(): HasMany
    {
        return $this->hasMany(UnitConversion::class, 'from_unit_id');
    }

    /**
     * Get conversions to this unit from other units.
     */
    public function conversionsTo(): HasMany
    {
        return $this->hasMany(UnitConversion::class, 'to_unit_id');
    }

    /**
     * Scope a query to only include active units.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include base units.
     */
    public function scopeBaseUnits($query)
    {
        return $query->where('is_base_unit', true);
    }

    /**
     * Scope a query to units in the same category.
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Get the unit's display name with symbol.
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->name} ({$this->symbol})";
    }

    /**
     * Check if this unit can be converted to another unit.
     */
    public function canConvertTo(Unit $targetUnit): bool
    {
        // Units in the same category can be converted
        if ($this->category_id && $this->category_id === $targetUnit->category_id) {
            return true;
        }

        // Check if there's a direct conversion rule
        return $this->conversionsFrom()->where('to_unit_id', $targetUnit->id)->where('is_active', true)->exists();
    }

    /**
     * Convert a quantity from this unit to another unit.
     */
    public function convertTo(Unit $targetUnit, float $quantity): float
    {
        if ($this->id === $targetUnit->id) {
            return $quantity; // Same unit, no conversion needed
        }

        // Check for direct conversion rule first
        $directConversion = $this->conversionsFrom()
            ->where('to_unit_id', $targetUnit->id)
            ->where('is_active', true)
            ->first();

        if ($directConversion) {
            return $directConversion->convert($quantity);
        }

        // Use category-based conversion through base unit
        if ($this->category_id && $this->category_id === $targetUnit->category_id) {
            return $this->convertThroughBaseUnit($targetUnit, $quantity);
        }

        throw new \InvalidArgumentException("Cannot convert from {$this->symbol} to {$targetUnit->symbol}");
    }

    /**
     * Convert through the base unit of the category.
     */
    private function convertThroughBaseUnit(Unit $targetUnit, float $quantity): float
    {
        // Convert to base unit first
        $baseQuantity = $quantity / $this->conversion_factor;

        // Convert from base unit to target unit
        return $baseQuantity * $targetUnit->conversion_factor;
    }

    /**
     * Get the conversion factor to another unit.
     */
    public function getConversionFactor(Unit $targetUnit): float
    {
        if ($this->id === $targetUnit->id) {
            return 1.0;
        }

        // Check for direct conversion rule
        $directConversion = $this->conversionsFrom()
            ->where('to_unit_id', $targetUnit->id)
            ->where('is_active', true)
            ->first();

        if ($directConversion) {
            return $directConversion->conversion_factor;
        }

        // Calculate through base unit
        if ($this->category_id && $this->category_id === $targetUnit->category_id) {
            return $targetUnit->conversion_factor / $this->conversion_factor;
        }

        throw new \InvalidArgumentException("Cannot get conversion factor from {$this->symbol} to {$targetUnit->symbol}");
    }

    /**
     * Get all units that this unit can convert to.
     */
    public function getConvertibleUnits()
    {
        $units = collect();

        // Add units from the same category
        if ($this->category_id) {
            $categoryUnits = Unit::inCategory($this->category_id)
                ->active()
                ->where('id', '!=', $this->id)
                ->get();
            $units = $units->merge($categoryUnits);
        }

        // Add units with direct conversion rules
        $directConversions = $this->conversionsFrom()
            ->with('toUnit')
            ->where('is_active', true)
            ->get()
            ->pluck('toUnit');
        $units = $units->merge($directConversions);

        return $units->unique('id');
    }

    /**
     * Check if this is a base unit for its category.
     */
    public function isBaseUnit(): bool
    {
        return $this->is_base_unit;
    }

    /**
     * Get the base unit for this unit's category.
     */
    public function getBaseUnit(): ?Unit
    {
        if (!$this->category_id) {
            return null;
        }

        return Unit::inCategory($this->category_id)
            ->where('is_base_unit', true)
            ->first();
    }
}
