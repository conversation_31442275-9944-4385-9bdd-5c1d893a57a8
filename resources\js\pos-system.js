/*
 * POS System
 */

'use strict';

// Ensure required global variables are available
if (typeof window.posBaseUrl === 'undefined') {
  window.posBaseUrl = window.baseUrl + 'pos/';
}

$(function () {
  // Variables
  let cart = [];
  let products = [];
  let currentPage = 1;
  let searchTimeout;
  let activeCurrency = {
    symbol: '$',
    position: 'prefix',
    decimal_places: 2
  };

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
      'X-Requested-With': 'XMLHttpRequest'
    }
  });

  // Debug: Check if CSRF token is available
  console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
  console.log('POS Base URL:', posBaseUrl);

  // Helper function for alerts
  function showAlert(options) {
    if (typeof Swal !== 'undefined') {
      return Swal.fire(options);
    } else {
      // Fallback to native alert
      alert(options.text || options.title || 'Alert');
      return Promise.resolve({ isConfirmed: true });
    }
  }

  // Initialize POS
  initializePOS();

  function initializePOS() {
    setupEventListeners();
    // Load currency first, then products
    loadCurrencySettings().then(() => {
      loadProducts();
      loadHeldTransactions();
      updateCartDisplay();
    });
  }

  // Load currency settings
  function loadCurrencySettings() {
    return $.get('/currency-settings/api/active')
    .done(function(currency) {
      activeCurrency = currency;
      console.log('Active currency loaded:', activeCurrency);
    })
    .fail(function() {
      console.warn('Failed to load currency settings, using defaults');
      // Set default currency if API fails
      activeCurrency = {
        currency_symbol: '$',
        currency_position: 'prefix',
        decimal_places: 2
      };
    });
  }

  // Format currency amount
  function formatCurrency(amount) {
    // Ensure we have valid currency data
    if (!activeCurrency || !activeCurrency.currency_symbol) {
      console.warn('Currency data not loaded, using defaults for amount:', amount);
      return '$' + parseFloat(amount || 0).toFixed(2);
    }

    // Validate amount
    const numericAmount = parseFloat(amount || 0);
    if (isNaN(numericAmount)) {
      console.warn('Invalid amount provided to formatCurrency:', amount);
      return activeCurrency.currency_symbol + '0.00';
    }

    const formattedAmount = numericAmount.toFixed(activeCurrency.decimal_places || 2);

    if (activeCurrency.currency_position === 'prefix') {
      return activeCurrency.currency_symbol + formattedAmount;
    } else {
      return formattedAmount + activeCurrency.currency_symbol;
    }
  }

  // Setup event listeners
  function setupEventListeners() {
    // Product search
    $('#product-search').on('input', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        currentPage = 1;
        loadProducts();
      }, 300);
    });

    // Refresh products
    $('#refresh-products').on('click', function() {
      loadProducts();
    });

    // Clear cart
    $('#clear-cart').on('click', function() {
      if (cart.length > 0) {
        showAlert({
          title: 'Clear Cart?',
          text: 'This will remove all items from the cart.',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Yes, clear it!',
          customClass: {
            confirmButton: 'btn btn-danger me-3',
            cancelButton: 'btn btn-outline-secondary'
          },
          buttonsStyling: false
        }).then((result) => {
          if (result.isConfirmed) {
            cart = [];
            updateCartDisplay();
          }
        });
      }
    });

    // Discount and tax calculations
    $('#discount-amount, #tax-amount').on('input', function() {
      updateCartTotals();
    });

    // Amount paid calculation
    $('#amount-paid').on('input', function() {
      calculateChange();
    });

    // Process sale
    $('#process-sale').on('click', function() {
      processSale();
    });

    // Hold transaction
    $('#hold-transaction').on('click', function() {
      holdTransaction();
    });

    // View held transactions
    $('#view-held').on('click', function() {
      showHeldTransactions();
    });

    // View sales history
    $('#view-sales').on('click', function() {
      showSalesHistory();
    });

    // Print receipt
    $('#print-receipt').on('click', function() {
      printReceipt();
    });

    // New sale
    $('#new-sale').on('click', function() {
      startNewSale();
    });

    // Sales date change
    $('#sales-date').on('change', function() {
      loadSalesHistory();
    });
  }

  // Load products
  function loadProducts() {
    const search = $('#product-search').val();

    console.log('Loading products with search:', search, 'page:', currentPage);

    $('#products-loading').show();
    $('#product-grid').hide();
    $('#no-products').addClass('d-none');

    $.get(posBaseUrl + 'products', {
      search: search,
      page: currentPage
    })
    .done(function(response) {
      console.log('Products loaded successfully:', response);
      products = response.products;
      displayProducts(response.products);
      displayPagination(response);
    })
    .fail(function(xhr, status, error) {
      console.error('Failed to load products:', xhr.responseText, status, error);
      showAlert({
        icon: 'error',
        title: 'Error!',
        text: 'Failed to load products: ' + (xhr.responseJSON?.message || error),
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    })
    .always(function() {
      $('#products-loading').hide();
    });
  }

  // Display products
  function displayProducts(products) {
    const grid = $('#product-grid');
    grid.empty();

    if (products.length === 0) {
      $('#no-products').removeClass('d-none');
      grid.hide();
      return;
    }

    grid.show();

    products.forEach(function(product) {
      // Determine image to display
      const imageUrl = product.image_url || '/assets/img/default/product-placeholder.svg';
      const imageAlt = product.image_alt || product.name;

      const productCard = `
        <div class="product-card" data-product-id="${product.id}">
          <div class="text-center mb-2">
            <div class="product-image-container mb-2">
              <img src="${imageUrl}" alt="${imageAlt}" class="product-image"
                   onerror="this.src='/assets/img/default/product-placeholder.svg'">
            </div>
            <h6 class="mb-1">${product.name}</h6>
            <small class="text-muted">${product.description || ''}</small>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <div class="h6 text-primary mb-0">${formatCurrency(product.selling_price)}</div>
              <small class="text-muted">${product.current_stock} ${product.unit}</small>
            </div>
            <button class="btn btn-primary btn-sm add-to-cart" data-product-id="${product.id}">
              <i class="ri-add-line"></i>
            </button>
          </div>
        </div>
      `;
      grid.append(productCard);
    });

    // Add to cart event
    $('.add-to-cart').on('click', function(e) {
      e.stopPropagation();
      const productId = $(this).data('product-id');
      addToCart(productId);
    });

    // Product card click event
    $('.product-card').on('click', function() {
      const productId = $(this).data('product-id');
      addToCart(productId);
    });
  }

  // Display pagination
  function displayPagination(response) {
    const pagination = $('#products-pagination');
    pagination.empty();

    if (response.last_page <= 1) return;

    // Previous button
    if (response.current_page > 1) {
      pagination.append(`
        <li class="page-item">
          <a class="page-link" href="#" data-page="${response.current_page - 1}">Previous</a>
        </li>
      `);
    }

    // Page numbers
    for (let i = 1; i <= response.last_page; i++) {
      const active = i === response.current_page ? 'active' : '';
      pagination.append(`
        <li class="page-item ${active}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `);
    }

    // Next button
    if (response.current_page < response.last_page) {
      pagination.append(`
        <li class="page-item">
          <a class="page-link" href="#" data-page="${response.current_page + 1}">Next</a>
        </li>
      `);
    }

    // Pagination click events
    $('.page-link').on('click', function(e) {
      e.preventDefault();
      currentPage = parseInt($(this).data('page'));
      loadProducts();
    });
  }

  // Add product to cart
  function addToCart(productId) {
    console.log('Adding product to cart:', productId);
    const product = products.find(p => p.id == productId);
    if (!product) {
      console.error('Product not found:', productId);
      return;
    }

    console.log('Product found:', product);

    // Check if product already in cart
    const existingItem = cart.find(item => item.product_id == productId);

    if (existingItem) {
      // Check stock availability
      if (existingItem.quantity >= product.current_stock) {
        showAlert({
          icon: 'warning',
          title: 'Insufficient Stock!',
          text: `Only ${product.current_stock} ${product.unit} available`,
          customClass: {
            confirmButton: 'btn btn-warning'
          }
        });
        return;
      }
      existingItem.quantity += 1;
      console.log('Updated existing item quantity:', existingItem.quantity);
    } else {
      const newItem = {
        product_id: parseInt(product.id),
        name: product.name,
        unit_price: parseFloat(product.selling_price) || 0,
        quantity: 1,
        unit: product.unit,
        max_stock: parseInt(product.current_stock) || 0
      };
      cart.push(newItem);
      console.log('Added new item to cart:', newItem);
    }

    console.log('Current cart:', cart);
    updateCartDisplay();
  }

  // Update cart display
  function updateCartDisplay() {
    const cartItems = $('#cart-items');
    const emptyCart = $('#empty-cart');

    console.log('Updating cart display, cart length:', cart.length);
    console.log('Cart items container:', cartItems.length);
    console.log('Empty cart element:', emptyCart.length);

    // Always remove existing cart items first
    cartItems.children('.cart-item').remove();

    if (cart.length === 0) {
      emptyCart.show();
      updateCartTotals();
      return;
    }

    emptyCart.hide();

    cart.forEach(function(item, index) {
      const quantity = parseInt(item.quantity) || 1;
      const unitPrice = parseFloat(item.unit_price) || 0;
      const maxStock = parseInt(item.max_stock) || 0;

      const isMinQuantity = quantity <= 1;
      const isMaxQuantity = quantity >= maxStock;

      const cartItem = $(`
        <div class="cart-item">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="flex-grow-1">
              <h6 class="mb-1">${item.name}</h6>
              <small class="text-muted">${formatCurrency(unitPrice)} per ${item.unit}</small>
            </div>
            <button class="btn btn-sm btn-outline-danger remove-item" data-index="${index}">
              <i class="ri-close-line"></i>
            </button>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <div class="quantity-controls">
              <button class="quantity-btn decrease-qty ${isMinQuantity ? 'disabled' : ''}" data-index="${index}" ${isMinQuantity ? 'disabled' : ''}>
                <i class="ri-subtract-line"></i>
              </button>
              <input type="number" class="quantity-input" value="${quantity}" min="1" max="${maxStock}" data-index="${index}">
              <button class="quantity-btn increase-qty ${isMaxQuantity ? 'disabled' : ''}" data-index="${index}" ${isMaxQuantity ? 'disabled' : ''}>
                <i class="ri-add-line"></i>
              </button>
            </div>
            <div class="text-end">
              <div class="fw-bold">${formatCurrency(quantity * unitPrice)}</div>
              <small class="text-muted">${quantity} ${item.unit}</small>
            </div>
          </div>
        </div>
      `);
      cartItems.append(cartItem);
    });

    console.log('Cart items appended, total cart items in DOM:', cartItems.children('.cart-item').length);

    // Cart item events - use event delegation to avoid issues with dynamically added elements
    cartItems.off('click', '.remove-item').on('click', '.remove-item', function() {
      const index = $(this).data('index');
      cart.splice(index, 1);
      updateCartDisplay();
    });

    cartItems.off('click', '.decrease-qty').on('click', '.decrease-qty', function() {
      if ($(this).prop('disabled') || $(this).hasClass('disabled')) {
        return;
      }
      const index = $(this).data('index');
      if (cart[index] && cart[index].quantity > 1) {
        cart[index].quantity--;
        updateCartDisplay();
      }
    });

    cartItems.off('click', '.increase-qty').on('click', '.increase-qty', function() {
      if ($(this).prop('disabled') || $(this).hasClass('disabled')) {
        return;
      }
      const index = $(this).data('index');
      if (cart[index] && cart[index].quantity < cart[index].max_stock) {
        cart[index].quantity++;
        updateCartDisplay();
      }
    });

    cartItems.off('change', '.quantity-input').on('change', '.quantity-input', function() {
      const index = $(this).data('index');
      const newQty = parseInt($(this).val());

      if (cart[index]) {
        if (newQty >= 1 && newQty <= cart[index].max_stock) {
          cart[index].quantity = newQty;
          updateCartDisplay();
        } else {
          // Reset to valid value if input is invalid
          $(this).val(cart[index].quantity);
          if (newQty > cart[index].max_stock) {
            showAlert({
              icon: 'warning',
              title: 'Insufficient Stock!',
              text: `Only ${cart[index].max_stock} ${cart[index].unit} available`,
              customClass: {
                confirmButton: 'btn btn-warning'
              }
            });
          }
        }
      }
    });

    updateCartTotals();
  }

  // Update cart totals
  function updateCartTotals() {
    const subtotal = cart.reduce((sum, item) => {
      const quantity = parseInt(item.quantity) || 0;
      const unitPrice = parseFloat(item.unit_price) || 0;
      return sum + (quantity * unitPrice);
    }, 0);

    const discount = parseFloat($('#discount-amount').val()) || 0;
    const tax = parseFloat($('#tax-amount').val()) || 0;
    const total = subtotal - discount + tax;

    $('#cart-subtotal').text(formatCurrency(subtotal));
    $('#cart-discount').text(formatCurrency(discount));
    $('#cart-tax').text(formatCurrency(tax));
    $('#cart-total').text(formatCurrency(total));

    // Auto-fill amount paid with total when cart is updated
    // Round to avoid floating point precision issues
    const roundedTotal = Math.round(total * 100) / 100;
    $('#amount-paid').val(roundedTotal.toFixed(activeCurrency.decimal_places || 2));

    calculateChange();
  }

  // Calculate change
  function calculateChange() {
    const totalText = $('#cart-total').text();
    // More robust parsing of currency text
    let total = 0;
    if (totalText && activeCurrency && activeCurrency.currency_symbol) {
      const cleanText = totalText.replace(activeCurrency.currency_symbol, '').replace(/,/g, '').trim();
      total = parseFloat(cleanText) || 0;
    } else {
      // Fallback: extract numbers from text
      const numbers = totalText.match(/[\d.]+/);
      total = numbers ? parseFloat(numbers[0]) : 0;
    }

    // Parse amount paid more carefully to handle different number formats
    let amountPaidInput = $('#amount-paid').val().toString().trim();
    // Remove any currency symbols and commas
    amountPaidInput = amountPaidInput.replace(/[^\d.-]/g, '');
    const amountPaid = parseFloat(amountPaidInput) || 0;

    // Round both values to avoid floating point precision issues
    const roundedTotal = Math.round(total * 100) / 100;
    const roundedAmountPaid = Math.round(amountPaid * 100) / 100;

    const change = roundedAmountPaid - roundedTotal;

    $('#change-amount').text(formatCurrency(Math.max(0, change)));

    // Update process sale button state - only check if cart has items and total is positive
    const canProcess = cart.length > 0 && roundedTotal > 0 && roundedAmountPaid >= roundedTotal;
    $('#process-sale').prop('disabled', !canProcess);
  }

  // Process sale
  function processSale() {
    if (cart.length === 0) return;

    const subtotal = cart.reduce((sum, item) => {
      const quantity = parseInt(item.quantity) || 0;
      const unitPrice = parseFloat(item.unit_price) || 0;
      return sum + (quantity * unitPrice);
    }, 0);
    const discount = parseFloat($('#discount-amount').val()) || 0;
    const tax = parseFloat($('#tax-amount').val()) || 0;
    const total = subtotal - discount + tax;

    // Parse amount paid more carefully to handle different number formats
    let amountPaidInput = $('#amount-paid').val().toString().trim();
    // Remove any currency symbols and commas
    amountPaidInput = amountPaidInput.replace(/[^\d.-]/g, '');
    const amountPaid = parseFloat(amountPaidInput) || 0;

    // Round both values to avoid floating point precision issues
    const roundedTotal = Math.round(total * 100) / 100;
    const roundedAmountPaid = Math.round(amountPaid * 100) / 100;

    console.log('Processing sale - Total:', roundedTotal, 'Amount Paid:', roundedAmountPaid);

    if (roundedAmountPaid < roundedTotal) {
      Swal.fire({
        icon: 'warning',
        title: 'Insufficient Payment!',
        text: `Amount paid (${formatCurrency(roundedAmountPaid)}) is less than total amount (${formatCurrency(roundedTotal)})`,
        customClass: {
          confirmButton: 'btn btn-warning'
        }
      });
      return;
    }

    const saleData = {
      items: cart.map(item => ({
        product_id: parseInt(item.product_id),
        quantity: parseInt(item.quantity),
        unit_price: parseFloat(item.unit_price)
      })),
      customer_name: $('#customer-name').val(),
      customer_phone: $('#customer-phone').val(),
      payment_method: $('#payment-method').val(),
      amount_paid: amountPaid,
      discount_amount: discount,
      tax_amount: tax,
      notes: ''
    };

    // Show processing
    $('#process-sale').prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-2"></i>Processing...');

    $.post(posBaseUrl + 'process-sale', saleData)
    .done(function(response) {
      // Show receipt
      showReceipt(response.receipt_data);

      // Reset form
      cart = [];
      updateCartDisplay();
      $('#customer-name, #customer-phone').val('');
      $('#discount-amount, #tax-amount, #amount-paid').val('');

      // Refresh products to update stock
      loadProducts();
    })
    .fail(function(xhr) {
      const response = xhr.responseJSON;
      Swal.fire({
        icon: 'error',
        title: 'Sale Failed!',
        text: response.message || 'Error processing sale',
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    })
    .always(function() {
      $('#process-sale').prop('disabled', false).html('<i class="ri-money-dollar-circle-line me-2"></i>Process Sale');
    });
  }

  // Show receipt
  function showReceipt(receiptData) {
    const receiptContent = `
      <div class="receipt" style="font-family: monospace; max-width: 300px; margin: 0 auto;">
        <div class="text-center mb-3">
          <h5>SALES RECEIPT</h5>
          <p class="mb-1">Receipt #: ${receiptData.sale_number}</p>
          <p class="mb-1">${receiptData.sale_date}</p>
          <p class="mb-1">Cashier: ${receiptData.cashier}</p>
          ${receiptData.customer_name ? `<p class="mb-1">Customer: ${receiptData.customer_name}</p>` : ''}
        </div>

        <hr>

        <div class="items">
          ${receiptData.items.map(item => `
            <div class="d-flex justify-content-between mb-1">
              <div>
                <div>${item.name}</div>
                <small>${parseInt(item.quantity) || 0} ${item.unit} x $${parseFloat(item.unit_price || 0).toFixed(2)}</small>
              </div>
              <div>$${parseFloat(item.line_total || 0).toFixed(2)}</div>
            </div>
          `).join('')}
        </div>

        <hr>

        <div class="totals">
          <div class="d-flex justify-content-between">
            <span>Subtotal:</span>
            <span>$${parseFloat(receiptData.subtotal || 0).toFixed(2)}</span>
          </div>
          ${parseFloat(receiptData.discount_amount || 0) > 0 ? `
            <div class="d-flex justify-content-between">
              <span>Discount:</span>
              <span>-$${parseFloat(receiptData.discount_amount || 0).toFixed(2)}</span>
            </div>
          ` : ''}
          ${parseFloat(receiptData.tax_amount || 0) > 0 ? `
            <div class="d-flex justify-content-between">
              <span>Tax:</span>
              <span>$${parseFloat(receiptData.tax_amount || 0).toFixed(2)}</span>
            </div>
          ` : ''}
          <div class="d-flex justify-content-between fw-bold">
            <span>Total:</span>
            <span>$${parseFloat(receiptData.total_amount || 0).toFixed(2)}</span>
          </div>
          <div class="d-flex justify-content-between">
            <span>Paid (${receiptData.payment_method}):</span>
            <span>$${parseFloat(receiptData.amount_paid || 0).toFixed(2)}</span>
          </div>
          <div class="d-flex justify-content-between">
            <span>Change:</span>
            <span>$${parseFloat(receiptData.change_amount || 0).toFixed(2)}</span>
          </div>
        </div>

        <hr>

        <div class="text-center">
          <p class="mb-1">Thank you for your business!</p>
          <small>Please keep this receipt for your records</small>
        </div>
      </div>
    `;

    $('#receipt-content').html(receiptContent);
    $('#receiptModal').modal('show');
  }

  // Print receipt
  function printReceipt() {
    const printContent = $('#receipt-content').html();
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Receipt</title>
          <style>
            body { font-family: monospace; margin: 20px; }
            .receipt { max-width: 300px; margin: 0 auto; }
            hr { border: 1px solid #000; }
            .d-flex { display: flex; }
            .justify-content-between { justify-content: space-between; }
            .text-center { text-align: center; }
            .fw-bold { font-weight: bold; }
            .mb-1 { margin-bottom: 0.25rem; }
            .mb-3 { margin-bottom: 1rem; }
          </style>
        </head>
        <body>
          ${printContent}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  }

  // Start new sale
  function startNewSale() {
    $('#receiptModal').modal('hide');
    cart = [];
    updateCartDisplay();
    $('#customer-name, #customer-phone').val('');
    $('#discount-amount, #tax-amount, #amount-paid').val('');
    $('#product-search').focus();
  }

  // Hold transaction
  function holdTransaction() {
    if (cart.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'Empty Cart!',
        text: 'Add items to cart before holding transaction',
        customClass: {
          confirmButton: 'btn btn-warning'
        }
      });
      return;
    }

    const holdData = {
      items: cart,
      customer_name: $('#customer-name').val(),
      notes: 'Held transaction'
    };

    $.post(posBaseUrl + 'hold-transaction', holdData)
    .done(function(response) {
      Swal.fire({
        icon: 'success',
        title: 'Transaction Held!',
        text: 'Transaction has been saved and can be retrieved later',
        customClass: {
          confirmButton: 'btn btn-success'
        }
      });

      // Clear current transaction
      cart = [];
      updateCartDisplay();
      $('#customer-name, #customer-phone').val('');
      $('#discount-amount, #tax-amount, #amount-paid').val('');

      loadHeldTransactions();
    })
    .fail(function(xhr) {
      const response = xhr.responseJSON;
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: response.message || 'Error holding transaction',
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    });
  }

  // Load held transactions
  function loadHeldTransactions() {
    $.get(posBaseUrl + 'held-transactions')
    .done(function(response) {
      $('#held-count').text(response.length);
    });
  }

  // Show held transactions
  function showHeldTransactions() {
    $.get(posBaseUrl + 'held-transactions')
    .done(function(response) {
      const heldList = $('#held-transactions-list');
      heldList.empty();

      if (response.length === 0) {
        heldList.html('<p class="text-center text-muted">No held transactions</p>');
      } else {
        response.forEach(function(transaction) {
          const transactionCard = `
            <div class="card mb-2">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                  <div>
                    <h6 class="mb-1">${transaction.customer_name || 'Walk-in Customer'}</h6>
                    <small class="text-muted">${transaction.items.length} items - ${new Date(transaction.created_at).toLocaleString()}</small>
                  </div>
                  <button class="btn btn-primary btn-sm retrieve-transaction" data-hold-id="${transaction.hold_id}">
                    Retrieve
                  </button>
                </div>
              </div>
            </div>
          `;
          heldList.append(transactionCard);
        });

        // Retrieve transaction events
        $('.retrieve-transaction').on('click', function() {
          const holdId = $(this).data('hold-id');
          retrieveTransaction(holdId);
        });
      }

      $('#heldTransactionsModal').modal('show');
    });
  }

  // Retrieve held transaction
  function retrieveTransaction(holdId) {
    $.get(posBaseUrl + 'held-transactions/' + holdId)
    .done(function(response) {
      cart = response.items;
      $('#customer-name').val(response.customer_name || '');
      updateCartDisplay();

      $('#heldTransactionsModal').modal('hide');
      loadHeldTransactions();

      Swal.fire({
        icon: 'success',
        title: 'Transaction Retrieved!',
        text: 'Held transaction has been loaded to cart',
        customClass: {
          confirmButton: 'btn btn-success'
        }
      });
    })
    .fail(function() {
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: 'Error retrieving transaction',
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    });
  }

  // Show sales history
  function showSalesHistory() {
    loadSalesHistory();
    $('#salesHistoryModal').modal('show');
  }

  // Load sales history
  function loadSalesHistory() {
    const date = $('#sales-date').val();

    $.get(posBaseUrl + 'sales-history', { date: date })
    .done(function(response) {
      // Display summary
      const summary = response.summary;
      $('#sales-summary').html(`
        <div class="text-center">
          <div class="h6 text-primary">${parseInt(summary.total_sales) || 0}</div>
          <small>Total Sales</small>
        </div>
        <div class="text-center">
          <div class="h6 text-success">$${parseFloat(summary.total_amount || 0).toFixed(2)}</div>
          <small>Total Amount</small>
        </div>
        <div class="text-center">
          <div class="h6 text-info">$${parseFloat(summary.cash_sales || 0).toFixed(2)}</div>
          <small>Cash Sales</small>
        </div>
        <div class="text-center">
          <div class="h6 text-warning">$${parseFloat(summary.card_sales || 0).toFixed(2)}</div>
          <small>Card Sales</small>
        </div>
      `);

      // Display sales list
      const salesList = $('#sales-history-list');
      salesList.empty();

      if (response.sales.length === 0) {
        salesList.html('<p class="text-center text-muted">No sales found for this date</p>');
      } else {
        const salesTable = `
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Receipt #</th>
                  <th>Customer</th>
                  <th>Items</th>
                  <th>Total</th>
                  <th>Payment</th>
                  <th>Time</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                ${response.sales.map(sale => `
                  <tr>
                    <td>${sale.sale_number}</td>
                    <td>${sale.customer_name || 'Walk-in'}</td>
                    <td>${sale.items ? sale.items.length : 0}</td>
                    <td>$${parseFloat(sale.total_amount || 0).toFixed(2)}</td>
                    <td><span class="badge bg-label-${sale.payment_method === 'cash' ? 'success' : sale.payment_method === 'card' ? 'primary' : 'info'}">${sale.payment_method}</span></td>
                    <td>${new Date(sale.sale_date).toLocaleTimeString()}</td>
                    <td>
                      <button class="btn btn-sm btn-outline-primary reprint-receipt" data-sale-id="${sale.id}">
                        <i class="ri-printer-line"></i>
                      </button>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        `;
        salesList.html(salesTable);

        // Reprint receipt events
        $('.reprint-receipt').on('click', function() {
          const saleId = $(this).data('sale-id');
          reprintReceipt(saleId);
        });
      }
    });
  }

  // Reprint receipt
  function reprintReceipt(saleId) {
    $.get(posBaseUrl + 'sales/' + saleId)
    .done(function(response) {
      showReceipt(response.receipt_data);
      $('#salesHistoryModal').modal('hide');
    })
    .fail(function() {
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: 'Error loading receipt',
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    });
  }
});
