<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions based on our manufacturing system features
        $permissions = [
            // Dashboard permissions
            'view-dashboard',
            'view-analytics',

            // User Management permissions
            'view-users',
            'create-users',
            'edit-users',
            'delete-users',
            'assign-roles',

            // Units Management permissions
            'view-units',
            'create-units',
            'edit-units',
            'delete-units',

            // Raw Materials Management permissions
            'view-raw-materials',
            'create-raw-materials',
            'edit-raw-materials',
            'delete-raw-materials',
            'view-raw-materials-movements',
            'create-raw-materials-movements',

            // Finished Products Management permissions
            'view-finished-products',
            'create-finished-products',
            'edit-finished-products',
            'delete-finished-products',
            'view-finished-products-movements',
            'create-finished-products-movements',
            'manage-recipes',

            // Manufacturing Orders permissions
            'view-manufacturing-orders',
            'create-manufacturing-orders',
            'edit-manufacturing-orders',
            'delete-manufacturing-orders',
            'start-manufacturing-orders',
            'complete-manufacturing-orders',
            'cancel-manufacturing-orders',
            'view-manufacturing-consumptions',
            'update-manufacturing-consumptions',

            // Purchases Management permissions
            'view-purchases',
            'create-purchases',
            'edit-purchases',
            'delete-purchases',
            'approve-purchases',
            'receive-purchases',
            'complete-purchases',
            'cancel-purchases',
            'view-purchase-statistics',

            // Point of Sales permissions
            'access-pos',
            'process-sales',
            'view-sales-history',
            'hold-transactions',
            'retrieve-held-transactions',
            'void-sales',
            'apply-discounts',

            // Inventory Management permissions
            'view-inventory-reports',
            'adjust-inventory',
            'view-low-stock-alerts',
            'manage-stock-movements',

            // Reports & Analytics permissions
            'view-sales-reports',
            'view-production-reports',
            'view-inventory-reports',
            'view-financial-reports',
            'export-reports',

            // System Administration permissions
            'manage-system-settings',
            'view-system-logs',
            'backup-system',
            'restore-system',
        ];

        // Create all permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $this->createRoles();
    }

    /**
     * Create roles and assign permissions
     */
    private function createRoles()
    {
        // Super Admin Role - Has all permissions
        $superAdmin = Role::firstOrCreate(['name' => 'Super Admin']);
        $superAdmin->syncPermissions(Permission::all());

        // Admin Role - Has most permissions except system administration
        $admin = Role::firstOrCreate(['name' => 'Admin']);
        $adminPermissions = [
            'view-dashboard',
            'view-analytics',
            'view-users',
            'create-users',
            'edit-users',
            'assign-roles',
            'view-units',
            'create-units',
            'edit-units',
            'delete-units',
            'view-raw-materials',
            'create-raw-materials',
            'edit-raw-materials',
            'delete-raw-materials',
            'view-raw-materials-movements',
            'create-raw-materials-movements',
            'view-finished-products',
            'create-finished-products',
            'edit-finished-products',
            'delete-finished-products',
            'view-finished-products-movements',
            'create-finished-products-movements',
            'manage-recipes',
            'view-manufacturing-orders',
            'create-manufacturing-orders',
            'edit-manufacturing-orders',
            'delete-manufacturing-orders',
            'start-manufacturing-orders',
            'complete-manufacturing-orders',
            'cancel-manufacturing-orders',
            'view-manufacturing-consumptions',
            'update-manufacturing-consumptions',
            'view-purchases',
            'create-purchases',
            'edit-purchases',
            'delete-purchases',
            'approve-purchases',
            'receive-purchases',
            'complete-purchases',
            'cancel-purchases',
            'view-purchase-statistics',
            'access-pos',
            'process-sales',
            'view-sales-history',
            'hold-transactions',
            'retrieve-held-transactions',
            'void-sales',
            'apply-discounts',
            'view-inventory-reports',
            'adjust-inventory',
            'view-low-stock-alerts',
            'manage-stock-movements',
            'view-sales-reports',
            'view-production-reports',
            'view-inventory-reports',
            'view-financial-reports',
            'export-reports',
        ];
        $admin->syncPermissions($adminPermissions);

        // Production Manager Role - Focused on manufacturing operations
        $productionManager = Role::firstOrCreate(['name' => 'Production Manager']);
        $productionManagerPermissions = [
            'view-dashboard',
            'view-analytics',
            'view-raw-materials',
            'view-raw-materials-movements',
            'view-finished-products',
            'view-finished-products-movements',
            'manage-recipes',
            'view-manufacturing-orders',
            'create-manufacturing-orders',
            'edit-manufacturing-orders',
            'start-manufacturing-orders',
            'complete-manufacturing-orders',
            'cancel-manufacturing-orders',
            'view-manufacturing-consumptions',
            'update-manufacturing-consumptions',
            'view-inventory-reports',
            'view-low-stock-alerts',
            'manage-stock-movements',
            'view-production-reports',
            'export-reports',
        ];
        $productionManager->syncPermissions($productionManagerPermissions);

        // Inventory Manager Role - Focused on inventory and purchasing
        $inventoryManager = Role::firstOrCreate(['name' => 'Inventory Manager']);
        $inventoryManagerPermissions = [
            'view-dashboard',
            'view-raw-materials',
            'create-raw-materials',
            'edit-raw-materials',
            'view-raw-materials-movements',
            'create-raw-materials-movements',
            'view-finished-products',
            'view-finished-products-movements',
            'view-purchases',
            'create-purchases',
            'edit-purchases',
            'receive-purchases',
            'complete-purchases',
            'view-purchase-statistics',
            'view-inventory-reports',
            'adjust-inventory',
            'view-low-stock-alerts',
            'manage-stock-movements',
            'view-inventory-reports',
            'export-reports',
        ];
        $inventoryManager->syncPermissions($inventoryManagerPermissions);

        // Sales Manager Role - Focused on sales operations
        $salesManager = Role::firstOrCreate(['name' => 'Sales Manager']);
        $salesManagerPermissions = [
            'view-dashboard',
            'access-pos',
            'process-sales',
            'view-sales-history',
            'hold-transactions',
            'retrieve-held-transactions',
            'void-sales',
            'apply-discounts',
            'view-finished-products',
            'view-sales-reports',
            'view-financial-reports',
            'export-reports',
        ];
        $salesManager->syncPermissions($salesManagerPermissions);

        // Production Operator Role - Basic production operations
        $productionOperator = Role::firstOrCreate(['name' => 'Production Operator']);
        $productionOperatorPermissions = [
            'view-dashboard',
            'view-raw-materials',
            'view-finished-products',
            'view-manufacturing-orders',
            'start-manufacturing-orders',
            'complete-manufacturing-orders',
            'view-manufacturing-consumptions',
            'update-manufacturing-consumptions',
            'view-low-stock-alerts',
        ];
        $productionOperator->syncPermissions($productionOperatorPermissions);

        // Cashier Role - POS operations only
        $cashier = Role::firstOrCreate(['name' => 'Cashier']);
        $cashierPermissions = [
            'access-pos',
            'process-sales',
            'view-sales-history',
            'hold-transactions',
            'retrieve-held-transactions',
            'view-finished-products',
        ];
        $cashier->syncPermissions($cashierPermissions);

        // Viewer Role - Read-only access
        $viewer = Role::firstOrCreate(['name' => 'Viewer']);
        $viewerPermissions = [
            'view-dashboard',
            'view-raw-materials',
            'view-finished-products',
            'view-manufacturing-orders',
            'view-purchases',
            'view-sales-history',
            'view-inventory-reports',
            'view-sales-reports',
            'view-production-reports',
        ];
        $viewer->syncPermissions($viewerPermissions);
    }
}
