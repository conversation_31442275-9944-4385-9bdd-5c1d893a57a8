/*
 * Units Management
 */

'use strict';

// Datatable (jquery)
$(function () {
  // Variable declaration for table
  var dt_unit_table = $('.datatables-units'),
    offCanvasForm = $('#offcanvasAddUnit');

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Units datatable
  if (dt_unit_table.length) {
    var dt_unit = dt_unit_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: baseUrl + 'units/data'
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'id' },
        { data: 'name' },
        { data: 'symbol' },
        { data: 'category_name' },
        { data: 'conversion_factor' },
        { data: 'is_base_unit' },
        { data: 'description' },
        { data: 'is_active' },
        { data: 'action' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          searchable: false,
          orderable: false,
          targets: 1,
          render: function (data, type, full, meta) {
            return `<span>${full.fake_id}</span>`;
          }
        },
        {
          // Unit name
          targets: 2,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $name = full['name'];
            return '<span class="fw-medium">' + $name + '</span>';
          }
        },
        {
          // Unit symbol
          targets: 3,
          render: function (data, type, full, meta) {
            var $symbol = full['symbol'];
            return '<span class="badge bg-label-primary">' + $symbol + '</span>';
          }
        },
        {
          // Category
          targets: 4,
          render: function (data, type, full, meta) {
            var category = full['category_name'];
            return category ? '<span class="badge bg-label-info">' + category + '</span>' : '<span class="text-muted">No Category</span>';
          }
        },
        {
          // Conversion Factor
          targets: 5,
          render: function (data, type, full, meta) {
            return parseFloat(data).toFixed(6);
          }
        },
        {
          // Base Unit
          targets: 6,
          render: function (data, type, full, meta) {
            var $is_base = full['is_base_unit'];
            return $is_base ? '<span class="badge bg-label-primary">Base Unit</span>' : '';
          }
        },
        {
          // Description
          targets: 7,
          render: function (data, type, full, meta) {
            var $description = full['description'] || 'No description';
            return '<span class="text-truncate" style="max-width: 200px;">' + $description + '</span>';
          }
        },
        {
          // Status
          targets: 8,
          className: 'text-center',
          render: function (data, type, full, meta) {
            var $status = full['is_active'];
            return `${
              $status
                ? '<span class="badge bg-label-success">Active</span>'
                : '<span class="badge bg-label-secondary">Inactive</span>'
            }`;
          }
        },
        {
          // Actions
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return (
              '<div class="d-flex align-items-center gap-50">' +
              `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddUnit"><i class="ri-edit-box-line ri-20px"></i></button>` +
              `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}"><i class="ri-delete-bin-7-line ri-20px"></i></button>` +
              '</div>'
            );
          }
        }
      ],
      order: [[2, 'asc']],
      dom:
        '<"card-header d-flex rounded-0 flex-wrap pb-md-0 pt-0"' +
        '<"me-5 ms-n2"f>' +
        '<"d-flex justify-content-start justify-content-md-end align-items-baseline"<"dt-action-buttons d-flex align-items-start align-items-md-center justify-content-sm-center gap-4"lB>>' +
        '>t' +
        '<"row mx-1"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 20, 50, 70, 100],
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: 'Search Units',
        info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
        paginate: {
          next: '<i class="ri-arrow-right-s-line"></i>',
          previous: '<i class="ri-arrow-left-s-line"></i>'
        }
      },
      // Buttons with Dropdown
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-outline-secondary dropdown-toggle me-4 waves-effect waves-light',
          text: '<i class="ri-upload-2-line ri-16px me-2"></i><span class="d-none d-sm-inline-block">Export </span>',
          buttons: [
            {
              extend: 'print',
              title: 'Units',
              text: '<i class="ri-printer-line me-1" ></i>Print',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8]
              }
            },
            {
              extend: 'csv',
              title: 'Units',
              text: '<i class="ri-file-text-line me-1" ></i>Csv',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8]
              }
            },
            {
              extend: 'excel',
              title: 'Units',
              text: '<i class="ri-file-excel-line me-1"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8]
              }
            },
            {
              extend: 'pdf',
              title: 'Units',
              text: '<i class="ri-file-pdf-line me-1"></i>Pdf',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8]
              }
            }
          ]
        },
        {
          text: '<i class="ri-add-line ri-16px me-0 me-sm-2 align-baseline"></i><span class="d-none d-sm-inline-block">Add New Unit</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddUnit'
          }
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Details of ' + data['name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== ''
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }

  // Delete Record
  $(document).on('click', '.delete-record', function () {
    var unit_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // sweetalert for confirmation of delete
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        confirmButton: 'btn btn-primary me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        // delete the data
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}units/${unit_id}`,
          success: function (response) {
            dt_unit.draw();
            Swal.fire({
              icon: 'success',
              title: 'Deleted!',
              text: response.message || 'The unit has been deleted!',
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function (xhr) {
            var response = xhr.responseJSON;
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: response.message || 'Error deleting unit',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Edit record
  $(document).on('click', '.edit-record', function () {
    var unit_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // changing the title of offcanvas
    $('#offcanvasAddUnitLabel').html('Edit Unit');

    // get data
    $.get(`${baseUrl}units/${unit_id}/edit`, function (data) {
      $('#unit_id').val(data.id);
      $('#add-unit-name').val(data.name);
      $('#add-unit-symbol').val(data.symbol);
      $('#add-unit-category').val(data.category_id);
      $('#add-unit-conversion-factor').val(data.conversion_factor);
      $('#add-unit-base').prop('checked', data.is_base_unit);
      $('#add-unit-description').val(data.description);
      $('#add-unit-active').prop('checked', data.is_active);
    });
  });

  // Reset form when adding new unit
  $('.add-new').on('click', function () {
    $('#unit_id').val('');
    $('#offcanvasAddUnitLabel').html('Add Unit');
    $('#addNewUnitForm')[0].reset();
    $('#add-unit-conversion-factor').val('1');
    $('#add-unit-active').prop('checked', true);
    $('#add-unit-base').prop('checked', false);
  });

  // Form validation and submission
  const addNewUnitForm = document.getElementById('addNewUnitForm');

  const fv = FormValidation.formValidation(addNewUnitForm, {
    fields: {
      name: {
        validators: {
          notEmpty: {
            message: 'Please enter unit name'
          }
        }
      },
      symbol: {
        validators: {
          notEmpty: {
            message: 'Please enter unit symbol'
          }
        }
      },
      conversion_factor: {
        validators: {
          notEmpty: {
            message: 'Please enter conversion factor'
          },
          numeric: {
            message: 'Conversion factor must be a number'
          },
          greaterThan: {
            min: 0.000001,
            message: 'Conversion factor must be greater than 0'
          }
        }
      }
    },
    plugins: {
      trigger: new FormValidation.plugins.Trigger(),
      bootstrap5: new FormValidation.plugins.Bootstrap5({
        eleValidClass: '',
        rowSelector: function (field, ele) {
          return '.mb-5';
        }
      }),
      submitButton: new FormValidation.plugins.SubmitButton(),
      autoFocus: new FormValidation.plugins.AutoFocus()
    }
  }).on('core.form.valid', function () {
    // Submit form via AJAX
    var formData = new FormData(addNewUnitForm);

    $.ajax({
      type: 'POST',
      url: baseUrl + 'units',
      data: formData,
      processData: false,
      contentType: false,
      success: function (response) {
        dt_unit.draw();
        offCanvasForm.offcanvas('hide');

        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function (xhr) {
        var response = xhr.responseJSON;
        if (response.errors) {
          // Handle validation errors
          Object.keys(response.errors).forEach(function(key) {
            fv.updateFieldStatus(key, 'Invalid', 'notEmpty');
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: response.message || 'Something went wrong',
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      }
    });
  });

  // Load categories on page load
  function loadCategories() {
    $.get(`${baseUrl}units/categories`, function (data) {
      var categorySelect = $('#add-unit-category');
      categorySelect.empty();
      categorySelect.append('<option value="">Select Category</option>');

      data.forEach(function(category) {
        categorySelect.append(`<option value="${category.id}">${category.name}</option>`);
      });
    });
  }

  // Load categories when page loads
  loadCategories();
});
