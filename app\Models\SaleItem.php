<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sale_id',
        'finished_product_id',
        'quantity',
        'unit_price',
        'line_total',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'line_total' => 'decimal:2',
    ];

    /**
     * Get the sale that this item belongs to.
     */
    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    /**
     * Get the finished product for this sale item.
     */
    public function finishedProduct(): BelongsTo
    {
        return $this->belongsTo(FinishedProduct::class);
    }

    /**
     * Get the unit price with currency symbol.
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return '$' . number_format($this->unit_price, 2);
    }

    /**
     * Get the line total with currency symbol.
     */
    public function getFormattedLineTotalAttribute(): string
    {
        return '$' . number_format($this->line_total, 2);
    }

    /**
     * Get the quantity with unit symbol.
     */
    public function getFormattedQuantityAttribute(): string
    {
        return $this->quantity . ' ' . ($this->finishedProduct->unit->symbol ?? '');
    }

    /**
     * Boot the model to automatically calculate line total.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($saleItem) {
            if ($saleItem->quantity && $saleItem->unit_price) {
                $saleItem->line_total = $saleItem->quantity * $saleItem->unit_price;
            }
        });
    }
}
