{"menu": [{"name": "Dashboard", "icon": "menu-icon tf-icons ri-home-smile-line", "slug": "dashboard", "url": "/"}, {"name": "Manufacturing", "icon": "menu-icon tf-icons ri-settings-3-line", "slug": "manufacturing", "submenu": [{"url": "units", "name": "Units", "slug": "units.index"}, {"url": "raw-materials", "name": "Raw Materials", "slug": "raw-materials.index"}, {"url": "finished-products", "name": "Finished Products", "slug": "finished-products.index"}, {"url": "manufacturing-orders", "name": "Manufacturing Orders", "slug": "manufacturing-orders.index"}, {"url": "purchases", "name": "Purchases", "slug": "purchases.index"}]}, {"menuHeader": "Apps & Pages"}, {"url": "app/email", "name": "Email", "icon": "menu-icon tf-icons ri-mail-open-line", "slug": "app-email"}, {"url": "app/chat", "name": "Cha<PERSON>", "icon": "menu-icon tf-icons ri-wechat-line", "slug": "app-chat"}, {"url": "app/calendar", "name": "Calendar", "icon": "menu-icon tf-icons ri-calendar-line", "slug": "app-calendar"}, {"url": "app/kanban", "name": "Ka<PERSON><PERSON>", "icon": "menu-icon tf-icons ri-drag-drop-line", "slug": "app-kanban"}, {"name": "Users", "icon": "menu-icon tf-icons ri-user-line", "slug": "app-user", "submenu": [{"url": "laravel/user-management", "name": "User Management (ACL)", "slug": "laravel-example-user-management"}, {"url": "app/user/list", "name": "List", "slug": "app-user-list"}, {"name": "View", "slug": "app-user-view", "submenu": [{"url": "app/user/view/account", "name": "Account", "slug": "app-user-view-account"}, {"url": "app/user/view/security", "name": "Security", "slug": "app-user-view-security"}, {"url": "app/user/view/billing", "name": "Billing & Plans", "slug": "app-user-view-billing"}, {"url": "app/user/view/notifications", "name": "Notifications", "slug": "app-user-view-notifications"}, {"url": "app/user/view/connections", "name": "Connections", "slug": "app-user-view-connections"}]}]}, {"name": "Roles & Permissions", "icon": "menu-icon tf-icons ri-lock-2-line", "slug": "app-access", "submenu": [{"url": "roles", "name": "Roles (ACL)", "slug": "roles.index"}, {"url": "app/access-roles", "name": "Roles", "slug": "app-access-roles"}, {"url": "app/access-permission", "name": "Permission", "slug": "app-access-permission"}]}, {"name": "Pages", "icon": "menu-icon tf-icons ri-layout-left-line", "slug": "pages", "submenu": [{"name": "User Profile", "slug": "pages-profile", "submenu": [{"url": "pages/profile-user", "name": "Profile", "slug": "pages-profile-user"}, {"url": "pages/profile-teams", "name": "Teams", "slug": "pages-profile-teams"}, {"url": "pages/profile-projects", "name": "Projects", "slug": "pages-profile-projects"}, {"url": "pages/profile-connections", "name": "Connections", "slug": "pages-profile-connections"}]}, {"name": "Account <PERSON><PERSON>", "slug": "pages-account-settings", "submenu": [{"url": "pages/account-settings-account", "name": "Account", "slug": "pages-account-settings-account"}, {"url": "pages/account-settings-security", "name": "Security", "slug": "pages-account-settings-security"}, {"url": "pages/account-settings-billing", "name": "Billing & Plans", "slug": "pages-account-settings-billing"}, {"url": "pages/account-settings-notifications", "name": "Notifications", "slug": "pages-account-settings-notifications"}, {"url": "pages/account-settings-connections", "name": "Connections", "slug": "pages-account-settings-connections"}]}, {"url": "pages/faq", "name": "FAQ", "slug": "pages-faq"}, {"url": "pages/pricing", "name": "Pricing", "slug": "pages-pricing"}, {"name": "Misc", "slug": "pages-misc", "submenu": [{"url": "pages/misc-error", "name": "Error", "slug": "pages-misc-error", "target": "_blank"}, {"url": "pages/misc-under-maintenance", "name": "Under Maintenance", "slug": "pages-misc-under-maintenance", "target": "_blank"}, {"url": "pages/misc-comingsoon", "name": "Coming Soon", "slug": "pages-misc-comingsoon", "target": "_blank"}, {"url": "pages/misc-not-authorized", "name": "Not Authorized", "slug": "pages-misc-not-authorized", "target": "_blank"}, {"url": "pages/misc-server-error", "name": "Server Error", "slug": "pages-misc-server-error", "target": "_blank"}]}]}, {"name": "Authentications", "icon": "menu-icon tf-icons ri-shield-keyhole-line", "slug": "auth", "submenu": [{"name": "<PERSON><PERSON>", "slug": "auth-login", "submenu": [{"url": "auth/login-basic", "name": "Basic", "slug": "auth-login-basic", "target": "_blank"}, {"url": "auth/login-cover", "name": "Cover", "slug": "auth-login-cover", "target": "_blank"}]}, {"name": "Register", "slug": "auth-register", "submenu": [{"url": "auth/register-basic", "name": "Basic", "slug": "auth-register-basic", "target": "_blank"}, {"url": "auth/register-cover", "name": "Cover", "slug": "auth-register-cover", "target": "_blank"}, {"url": "auth/register-multisteps", "name": "Multi-steps", "slug": "auth-register-multisteps", "target": "_blank"}]}, {"name": "<PERSON><PERSON><PERSON>", "slug": "auth-verify-email", "submenu": [{"url": "auth/verify-email-basic", "name": "Basic", "slug": "auth-verify-email-basic", "target": "_blank"}, {"url": "auth/verify-email-cover", "name": "Cover", "slug": "auth-verify-email-cover", "target": "_blank"}]}, {"name": "Reset Password", "slug": "auth-reset-password", "submenu": [{"url": "auth/reset-password-basic", "name": "Basic", "slug": "auth-reset-password-basic", "target": "_blank"}, {"url": "auth/reset-password-cover", "name": "Cover", "slug": "auth-reset-password-cover", "target": "_blank"}]}, {"name": "Forgot Password", "slug": "auth-forgot-password", "submenu": [{"url": "auth/forgot-password-basic", "name": "Basic", "slug": "auth-forgot-password-basic", "target": "_blank"}, {"url": "auth/forgot-password-cover", "name": "Cover", "slug": "auth-forgot-password-cover", "target": "_blank"}]}]}]}