<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CurrencySetting;
use Spatie\Permission\Models\Permission;

class CurrencySettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create manage-settings permission if it doesn't exist
        Permission::firstOrCreate(['name' => 'manage-settings']);

        // Create default currency setting if none exists
        if (CurrencySetting::count() === 0) {
            CurrencySetting::create([
                'currency_name' => 'US Dollar',
                'currency_code' => 'USD',
                'currency_symbol' => '$',
                'currency_position' => 'prefix',
                'decimal_places' => 2,
                'is_active' => true
            ]);
        }
    }
}
