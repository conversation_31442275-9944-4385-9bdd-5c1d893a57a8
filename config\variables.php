<?php
// Variables
return [
  "creatorName" => "BenSalem-Amine",
  "creatorUrl" => "#",
  "templateName" => "Manufacturing App",
  "templateSuffix" => "Manufacturing App",
  "templateVersion" => "1.0.0",
  "templateFree" => false,
  "templateDescription" => "Manufacturing App is a comprehensive web application built using Laravel 11, Blade templating, and the Materialize HTML Admin Template with Bootstrap 5 integration.",
  "templateKeyword" => "dashboard, material, material design, bootstrap 5 dashboard, bootstrap 5 design, bootstrap 5",
  "licenseUrl" => "#",
  "livePreview" => "#",
  "productPage" => "#",
  "support" => "#",
  "moreThemes" => "#",
  "documentation" => "#",
  "generator" => "",
  "changelog" => "#",
  "repository" => "#",
  "gitRepo" => "#",
  "gitRepoAccess" => "#",
  "facebookUrl" => "#",
  "twitterUrl" => "#",
  "githubUrl" => "#",
  "dribbbleUrl" => "#",
  "instagramUrl" => "#"
];
