<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number', 50)->unique(); // Auto-generated order number
            $table->foreignId('finished_product_id')->constrained('finished_products')->onDelete('restrict');
            $table->decimal('planned_quantity', 10, 3);
            $table->decimal('produced_quantity', 10, 3)->default(0);
            $table->enum('status', ['planned', 'in_progress', 'completed', 'cancelled'])->default('planned');
            $table->date('planned_date');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->string('responsible_person')->nullable();
            $table->text('notes')->nullable();
            $table->decimal('estimated_cost', 12, 2)->nullable(); // Calculated based on recipe
            $table->decimal('actual_cost', 12, 2)->nullable(); // Actual cost after completion
            $table->integer('estimated_time_minutes')->nullable(); // Estimated production time
            $table->integer('actual_time_minutes')->nullable(); // Actual production time
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance with custom short names
            $table->index(['status', 'planned_date'], 'mfg_orders_status_date_idx');
            $table->index('finished_product_id', 'mfg_orders_product_idx');
            $table->index('order_number', 'mfg_orders_number_idx');
            $table->index('created_by', 'mfg_orders_creator_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_orders');
    }
};
