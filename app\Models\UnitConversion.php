<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UnitConversion extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'from_unit_id',
        'to_unit_id',
        'conversion_factor',
        'formula',
        'notes',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'conversion_factor' => 'decimal:6',
        'is_active' => 'boolean',
    ];

    /**
     * Get the source unit for this conversion.
     */
    public function fromUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'from_unit_id');
    }

    /**
     * Get the target unit for this conversion.
     */
    public function toUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'to_unit_id');
    }

    /**
     * Scope a query to only include active conversions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Convert a quantity using this conversion.
     */
    public function convert(float $quantity): float
    {
        if ($this->formula) {
            // Handle complex conversions with formula
            return $this->applyFormula($quantity);
        }

        return $quantity * $this->conversion_factor;
    }

    /**
     * Apply a formula for complex conversions.
     */
    private function applyFormula(float $quantity): float
    {
        // For now, just use the conversion factor
        // This can be extended to handle complex formulas like temperature conversions
        return $quantity * $this->conversion_factor;
    }

    /**
     * Get the conversion description.
     */
    public function getDescriptionAttribute(): string
    {
        return "1 {$this->fromUnit->symbol} = {$this->conversion_factor} {$this->toUnit->symbol}";
    }
}
