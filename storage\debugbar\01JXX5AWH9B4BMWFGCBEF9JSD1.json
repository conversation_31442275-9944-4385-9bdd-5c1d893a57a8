{"__meta": {"id": "01JXX5AWH9B4BMWFGCBEF9JSD1", "datetime": "2025-06-16 20:01:49", "utime": **********.648602, "method": "GET", "uri": "/purchases?id=&supplier_name=Aymen&supplier_contact=44416197&supplier_email=&supplier_address=Soukra&purchase_date=2025-06-16&expected_delivery_date=2025-06-16&delivery_fee=&handling_fee=&other_fees=&notes=", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.10934, "end": **********.648706, "duration": 1.5393660068511963, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": **********.10934, "relative_start": 0, "end": **********.501445, "relative_end": **********.501445, "duration": 0.****************, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.50152, "relative_start": 0.****************, "end": **********.648717, "relative_end": 1.0967254638671875e-05, "duration": 1.****************, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.527065, "relative_start": 0.****************, "end": **********.549953, "relative_end": **********.549953, "duration": 0.****************, "duration_str": "22.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.804863, "relative_start": 0.****************, "end": **********.607326, "relative_end": **********.607326, "duration": 0.****************, "duration_str": "802ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: pages.purchases.index", "start": **********.944595, "relative_start": 0.****************, "end": **********.944595, "relative_end": **********.944595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.layoutMaster", "start": **********.356068, "relative_start": 1.****************, "end": **********.356068, "relative_end": **********.356068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.contentNavbarLayout", "start": **********.357465, "relative_start": 1.2481250762939453, "end": **********.357465, "relative_end": **********.357465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenuData", "start": **********.419142, "relative_start": 1.3098020553588867, "end": **********.419142, "relative_end": **********.419142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenu", "start": **********.474725, "relative_start": 1.3653850555419922, "end": **********.474725, "relative_end": **********.474725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: _partials.macros", "start": **********.551323, "relative_start": 1.4419829845428467, "end": **********.551323, "relative_end": **********.551323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.submenu", "start": **********.553478, "relative_start": 1.4441380500793457, "end": **********.553478, "relative_end": **********.553478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.submenu", "start": **********.555821, "relative_start": 1.4464809894561768, "end": **********.555821, "relative_end": **********.555821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.submenu", "start": **********.557094, "relative_start": 1.447754144668579, "end": **********.557094, "relative_end": **********.557094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.submenu", "start": **********.558554, "relative_start": 1.449213981628418, "end": **********.558554, "relative_end": **********.558554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.navbar.navbar", "start": **********.560002, "relative_start": 1.4506621360778809, "end": **********.560002, "relative_end": **********.560002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.footer.footer", "start": **********.569264, "relative_start": 1.4599239826202393, "end": **********.569264, "relative_end": **********.569264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.commonMaster", "start": **********.571423, "relative_start": 1.462083101272583, "end": **********.571423, "relative_end": **********.571423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.styles", "start": **********.574615, "relative_start": 1.4652750492095947, "end": **********.574615, "relative_end": **********.574615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scriptsIncludes", "start": **********.585051, "relative_start": 1.4757111072540283, "end": **********.585051, "relative_end": **********.585051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scripts", "start": **********.594127, "relative_start": 1.4847869873046875, "end": **********.594127, "relative_end": **********.594127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 4575944, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "pages.purchases.index", "param_count": null, "params": [], "start": **********.94423, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/pages/purchases/index.blade.phppages.purchases.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Fpages%2Fpurchases%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "layouts.layoutMaster", "param_count": null, "params": [], "start": **********.355866, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/layoutMaster.blade.phplayouts.layoutMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FlayoutMaster.blade.php&line=1", "ajax": false, "filename": "layoutMaster.blade.php", "line": "?"}}, {"name": "layouts.contentNavbarLayout", "param_count": null, "params": [], "start": **********.357285, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/contentNavbarLayout.blade.phplayouts.contentNavbarLayout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcontentNavbarLayout.blade.php&line=1", "ajax": false, "filename": "contentNavbarLayout.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenuData", "param_count": null, "params": [], "start": **********.418872, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenuData.blade.phplayouts.sections.menu.verticalMenuData", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenuData.blade.php&line=1", "ajax": false, "filename": "verticalMenuData.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenu", "param_count": null, "params": [], "start": **********.474543, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenu.blade.phplayouts.sections.menu.verticalMenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenu.blade.php&line=1", "ajax": false, "filename": "verticalMenu.blade.php", "line": "?"}}, {"name": "_partials.macros", "param_count": null, "params": [], "start": **********.551102, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/_partials/macros.blade.php_partials.macros", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2F_partials%2Fmacros.blade.php&line=1", "ajax": false, "filename": "macros.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.submenu", "param_count": null, "params": [], "start": **********.553301, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/submenu.blade.phplayouts.sections.menu.submenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2Fsubmenu.blade.php&line=1", "ajax": false, "filename": "submenu.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.submenu", "param_count": null, "params": [], "start": **********.555624, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/submenu.blade.phplayouts.sections.menu.submenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2Fsubmenu.blade.php&line=1", "ajax": false, "filename": "submenu.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.submenu", "param_count": null, "params": [], "start": **********.556806, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/submenu.blade.phplayouts.sections.menu.submenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2Fsubmenu.blade.php&line=1", "ajax": false, "filename": "submenu.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.submenu", "param_count": null, "params": [], "start": **********.558277, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/submenu.blade.phplayouts.sections.menu.submenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2Fsubmenu.blade.php&line=1", "ajax": false, "filename": "submenu.blade.php", "line": "?"}}, {"name": "layouts.sections.navbar.navbar", "param_count": null, "params": [], "start": **********.559807, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/navbar/navbar.blade.phplayouts.sections.navbar.navbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fnavbar%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}}, {"name": "layouts.sections.footer.footer", "param_count": null, "params": [], "start": **********.569076, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/footer/footer.blade.phplayouts.sections.footer.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Ffooter%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "layouts.commonMaster", "param_count": null, "params": [], "start": **********.571247, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/commonMaster.blade.phplayouts.commonMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcommonMaster.blade.php&line=1", "ajax": false, "filename": "commonMaster.blade.php", "line": "?"}}, {"name": "layouts.sections.styles", "param_count": null, "params": [], "start": **********.574424, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/styles.blade.phplayouts.sections.styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "layouts.sections.scriptsIncludes", "param_count": null, "params": [], "start": **********.584846, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scriptsIncludes.blade.phplayouts.sections.scriptsIncludes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2FscriptsIncludes.blade.php&line=1", "ajax": false, "filename": "scriptsIncludes.blade.php", "line": "?"}}, {"name": "layouts.sections.scripts", "param_count": null, "params": [], "start": **********.593854, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scripts.blade.phplayouts.sections.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.036309999999999995, "accumulated_duration_str": "36.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `manu_sessions` where `id` = 'raKObcV5G0EndfIjdXLs6wFf1IlWHAYw3gfGtUEF' limit 1", "type": "query", "params": [], "bindings": ["raKObcV5G0EndfIjdXLs6wFf1IlWHAYw3gfGtUEF"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.6258771, "duration": 0.01262, "duration_str": "12.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "manufacturing", "explain": null, "start_percent": 0, "width_percent": 34.756}, {"sql": "select * from `manu_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.673559, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "manufacturing", "explain": null, "start_percent": 34.756, "width_percent": 3.36}, {"sql": "select * from `manu_purchases`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 24}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.70521, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "PurchaseController.php:24", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FPurchaseController.php&line=24", "ajax": false, "filename": "PurchaseController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 38.116, "width_percent": 8.152}, {"sql": "select * from `manu_users` where `manu_users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.735718, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "PurchaseController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FPurchaseController.php&line=24", "ajax": false, "filename": "PurchaseController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 46.268, "width_percent": 4.599}, {"sql": "select count(*) as aggregate from `manu_purchases` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.747068, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "PurchaseController.php:26", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FPurchaseController.php&line=26", "ajax": false, "filename": "PurchaseController.php", "line": "26"}, "connection": "manufacturing", "explain": null, "start_percent": 50.868, "width_percent": 3.443}, {"sql": "select count(*) as aggregate from `manu_purchases` where `status` = 'ordered'", "type": "query", "params": [], "bindings": ["ordered"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.7680728, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "PurchaseController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FPurchaseController.php&line=27", "ajax": false, "filename": "PurchaseController.php", "line": "27"}, "connection": "manufacturing", "explain": null, "start_percent": 54.31, "width_percent": 3.14}, {"sql": "select count(*) as aggregate from `manu_purchases` where `status` = 'received'", "type": "query", "params": [], "bindings": ["received"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.786096, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "PurchaseController.php:28", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PurchaseController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\PurchaseController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FPurchaseController.php&line=28", "ajax": false, "filename": "PurchaseController.php", "line": "28"}, "connection": "manufacturing", "explain": null, "start_percent": 57.45, "width_percent": 2.782}, {"sql": "select * from `manu_cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.813617, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 60.231, "width_percent": 12.751}, {"sql": "select `manu_permissions`.*, `manu_model_has_permissions`.`model_id` as `pivot_model_id`, `manu_model_has_permissions`.`permission_id` as `pivot_permission_id`, `manu_model_has_permissions`.`model_type` as `pivot_model_type` from `manu_permissions` inner join `manu_model_has_permissions` on `manu_permissions`.`id` = `manu_model_has_permissions`.`permission_id` where `manu_model_has_permissions`.`model_id` in (1) and `manu_model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.845504, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "manufacturing", "explain": null, "start_percent": 72.983, "width_percent": 10.603}, {"sql": "select `manu_roles`.*, `manu_model_has_roles`.`model_id` as `pivot_model_id`, `manu_model_has_roles`.`role_id` as `pivot_role_id`, `manu_model_has_roles`.`model_type` as `pivot_model_type` from `manu_roles` inner join `manu_model_has_roles` on `manu_roles`.`id` = `manu_model_has_roles`.`role_id` where `manu_model_has_roles`.`model_id` in (1) and `manu_model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.862551, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "manufacturing", "explain": null, "start_percent": 83.586, "width_percent": 16.414}]}, "models": {"data": {"App\\Models\\Purchase": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FPurchase.php&line=1", "ajax": false, "filename": "Purchase.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 38, "messages": [{"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1844768502 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844768502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.884908, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1533307295 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533307295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.888926, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1594152382 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594152382\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.893699, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-891930217 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891930217\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.901588, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-478303063 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478303063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.908, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-274895746 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274895746\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.916723, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-796426465 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-796426465\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.92073, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-757964214 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757964214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.92514, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1382659261 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382659261\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.943153, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1242578057 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1242578057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.362393, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-765832803 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765832803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.367058, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-947857402 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947857402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.37042, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-12059850 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12059850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.374469, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1232453980 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232453980\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.380448, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1065692828 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065692828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.390607, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1548459774 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548459774\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.395353, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1453144574 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453144574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.401807, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2047915794 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047915794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418297, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2020448769 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020448769\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.4225, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1749649720 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749649720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.425532, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1076364920 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076364920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.428731, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-734420179 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734420179\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.435732, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-142121851 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142121851\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439599, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2146681690 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146681690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.443257, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-598502140 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598502140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.448045, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1455454444 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455454444\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.453378, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-66145966 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66145966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.473524, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1781112860 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781112860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.478449, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-184556816 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184556816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.483382, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-157633214 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157633214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.487753, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1431025351 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431025351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.491165, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-432452643 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432452643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.495143, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1550968934 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550968934\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.500466, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-755148355 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755148355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.505079, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-605678414 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605678414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.507486, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1698554076 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1698554076\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.510181, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1861936188 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861936188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527543, "xdebug_link": null}, {"message": "[\n  ability => manage-settings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-921442262 data-indent-pad=\"  \"><span class=sf-dump-note>manage-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921442262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54521, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/purchases?delivery_fee=&expected_delivery_date=2025-06-16&handling_fee=&id=&no...", "action_name": "purchases.index", "controller_action": "App\\Http\\Controllers\\PurchaseController@index", "uri": "GET purchases", "controller": "App\\Http\\Controllers\\PurchaseController@index<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FPurchaseController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/purchases", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FPurchaseController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PurchaseController.php:21-42</a>", "middleware": "web, auth", "duration": "1.56s", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1915818428 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>supplier_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Aymen</span>\"\n  \"<span class=sf-dump-key>supplier_contact</span>\" => \"<span class=sf-dump-str title=\"8 characters\">44416197</span>\"\n  \"<span class=sf-dump-key>supplier_email</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>supplier_address</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Soukra</span>\"\n  \"<span class=sf-dump-key>purchase_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-16</span>\"\n  \"<span class=sf-dump-key>expected_delivery_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-16</span>\"\n  \"<span class=sf-dump-key>delivery_fee</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>handling_fee</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>other_fees</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915818428\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1223108323 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1223108323\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2134813945 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://127.0.0.1:8000/purchases?id=&amp;supplier_name=Aymen&amp;supplier_contact=44416197&amp;supplier_email=&amp;supplier_address=Soukra&amp;purchase_date=2025-06-16&amp;expected_delivery_date=2025-06-16&amp;delivery_fee=&amp;handling_fee=&amp;other_fees=&amp;notes=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1603 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; sidebar_state=true; XSRF-TOKEN=eyJpdiI6ImR4L3p3Q3F1RTVyVFN5ZDlSZW5LdUE9PSIsInZhbHVlIjoia0hJbk5nYW0ybXY4TC9aMzFkdXhscHlFT1lyL011UFBnUFdSMXVpMGQ1NEI2QVdsb2Nldkt6QmFHTFhmRzlmTGliLzRqSnpDaUE5VjlLTzl5UVViVXZSa3VscWs0cWhnYWkrMmNtbVRsdlduRVd6Zi9rZDRFWC8vQURvMzExUkQiLCJtYWMiOiIzMmMwZTBlNjlhMGJlZmJjYzM4ODEwM2MzNTYwYzFkMjU4ZmMwZGM0ZDU0NzE0MTU0NjgzMTY4MmQ2N2ZiY2Y0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik1XeGNwSW1lK2hGc25ZM3FFekdndnc9PSIsInZhbHVlIjoieVhGTUFMS3lLYkZvMnhNdzA0alcvckZzQitwQnRWUWp2TitvaFBJcTBDREd1OXpPcHY4NGdabE9rQmtQSHlMeGxPdytsbmlZQ0tFSXFFaXZVS004dVNVcFlyUE0wNDFab2M2ZXJNanBXUzJJd3VNbkJiaWdVUlcySTlSNUptcGMiLCJtYWMiOiI1MDRjNDA5ZTI0ZTcyODBmMzVkNjg3NTE3MmUzNjI1OTkzZjM4NTlkYjYzNzliYmY0OWNmMzhhZWFmNzI3ZDg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134813945\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1464875564 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sidebar_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ytlvGr9FVqWmg1LCwVBAzmmGPYKgqR7qUj5RxonJ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">raKObcV5G0EndfIjdXLs6wFf1IlWHAYw3gfGtUEF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464875564\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1100021764 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 20:01:48 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100021764\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1932660432 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ytlvGr9FVqWmg1LCwVBAzmmGPYKgqR7qUj5RxonJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/translations/fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932660432\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/purchases?delivery_fee=&expected_delivery_date=2025-06-16&handling_fee=&id=&no...", "action_name": "purchases.index", "controller_action": "App\\Http\\Controllers\\PurchaseController@index"}, "badge": null}}