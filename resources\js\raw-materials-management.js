/*
 * Raw Materials Management
 */

'use strict';

// Datatable (jquery)
$(function () {
  // Variable declaration for table
  var dt_raw_material_table = $('.datatables-raw-materials'),
    select2 = $('.select2'),
    offCanvasForm = $('#offcanvasAddRawMaterial');

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Image upload handling
  const imageInput = $('#add-material-image');
  const imagePreview = $('#add-material-image-preview');
  const imagePreviewContainer = $('.image-preview-container');
  const removeImageBtn = $('#remove-material-image');

  // Handle image selection
  imageInput.on('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        Swal.fire({
          icon: 'error',
          title: __('Error!'),
          text: __('Please select a valid image file (JPEG, PNG, GIF, WebP)'),
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        });
        imageInput.val('');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        Swal.fire({
          icon: 'error',
          title: __('Error!'),
          text: __('Image size must be less than 5MB'),
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        });
        imageInput.val('');
        return;
      }

      // Show preview
      const reader = new FileReader();
      reader.onload = function(e) {
        imagePreview.attr('src', e.target.result);
        imagePreviewContainer.show();
      };
      reader.readAsDataURL(file);
    }
  });

  // Handle image removal
  removeImageBtn.on('click', function() {
    imageInput.val('');
    imagePreview.attr('src', '');
    imagePreviewContainer.hide();
  });

  // Load units for dropdown
  function loadUnits() {
    $.get(baseUrl + 'units/active', function(data) {
      var unitSelect = $('#add-material-unit');
      unitSelect.empty().append(`<option value="">${__('Select Unit')}</option>`);

      data.forEach(function(unit) {
        unitSelect.append(`<option value="${unit.id}">${unit.name} (${unit.symbol})</option>`);
      });
    });
  }

  // Load units on page load
  loadUnits();

  // Raw Materials datatable
  if (dt_raw_material_table.length) {
    var dt_raw_material = dt_raw_material_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: baseUrl + 'raw-materials/data'
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'id' },
        { data: 'name' },
        { data: 'current_stock' },
        { data: 'minimum_stock' },
        { data: 'unit_price' },
        { data: 'supplier' },
        { data: 'is_active' },
        { data: 'action' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          searchable: false,
          orderable: false,
          targets: 1,
          render: function (data, type, full, meta) {
            return `<span>${full.fake_id}</span>`;
          }
        },
        {
          // Material name with image and description
          targets: 2,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $name = full['name'];
            var $description = full['description'] || '';
            var $imageUrl = full['image_url'] || '/assets/img/default/raw-material-placeholder.svg';
            var $imageAlt = full['image_alt'] || $name;

            return '<div class="d-flex align-items-center">' +
              '<div class="avatar avatar-sm me-3">' +
              '<img src="' + $imageUrl + '" alt="' + $imageAlt + '" class="table-image-thumbnail" ' +
              'onerror="this.src=\'/assets/img/default/raw-material-placeholder.svg\'">' +
              '</div>' +
              '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $name + '</span>' +
              ($description ? '<small class="text-muted">' + $description + '</small>' : '') +
              '</div>' +
              '</div>';
          }
        },
        {
          // Current stock with unit and status
          targets: 3,
          render: function (data, type, full, meta) {
            var $stock = parseFloat(full['current_stock']).toFixed(3);
            var $unit = full['unit'] ? full['unit']['symbol'] : '';
            var $status = full['stock_status'];
            var $statusColor = full['stock_status_color'];

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $stock + ' ' + $unit + '</span>' +
              '<span class="badge bg-label-' + $statusColor + ' badge-sm">' + $status + '</span>' +
              '</div>';
          }
        },
        {
          // Minimum stock
          targets: 4,
          render: function (data, type, full, meta) {
            var $minStock = parseFloat(full['minimum_stock']).toFixed(3);
            var $unit = full['unit'] ? full['unit']['symbol'] : '';
            return '<span>' + $minStock + ' ' + $unit + '</span>';
          }
        },
        {
          // Unit price
          targets: 5,
          render: function (data, type, full, meta) {
            var $price = full['unit_price'] ? (window.formatCurrency ? window.formatCurrency(full['unit_price']) : '$' + parseFloat(full['unit_price']).toFixed(2)) : 'N/A';
            return '<span>' + $price + '</span>';
          }
        },
        {
          // Supplier
          targets: 6,
          render: function (data, type, full, meta) {
            var $supplier = full['supplier'] || __('N/A');
            return '<span class="text-truncate" style="max-width: 150px;">' + $supplier + '</span>';
          }
        },
        {
          // Status
          targets: 7,
          className: 'text-center',
          render: function (data, type, full, meta) {
            var $status = full['is_active'];
            return `${
              $status
                ? '<span class="badge bg-label-success">' + __('Active') + '</span>'
                : '<span class="badge bg-label-secondary">' + __('Inactive') + '</span>'
            }`;
          }
        },
        {
          // Actions
          targets: -1,
          title: __('Actions'),
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return (
              '<div class="d-flex align-items-center gap-50">' +
              `<button class="btn btn-sm btn-icon view-movements btn-text-info rounded-pill waves-effect" data-id="${full['id']}" data-name="${full['name']}" title="${__('View Movements')}"><i class="ri-history-line ri-20px"></i></button>` +
              `<button class="btn btn-sm btn-icon add-stock btn-text-success rounded-pill waves-effect" data-id="${full['id']}" data-name="${full['name']}" data-unit="${full['unit'] ? full['unit']['symbol'] : ''}" title="${__('Add Stock')}"><i class="ri-add-circle-line ri-20px"></i></button>` +
              `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddRawMaterial" title="${__('Edit')}"><i class="ri-edit-box-line ri-20px"></i></button>` +
              `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" title="${__('Delete')}"><i class="ri-delete-bin-7-line ri-20px"></i></button>` +
              '</div>'
            );
          }
        }
      ],
      order: [[2, 'asc']],
      dom:
        '<"card-header d-flex rounded-0 flex-wrap pb-md-0 pt-0"' +
        '<"me-5 ms-n2"f>' +
        '<"d-flex justify-content-start justify-content-md-end align-items-baseline"<"dt-action-buttons d-flex align-items-start align-items-md-center justify-content-sm-center gap-4"lB>>' +
        '>t' +
        '<"row mx-1"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 20, 50, 70, 100],
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: __('Search Materials'),
        info: __('Displaying _START_ to _END_ of _TOTAL_ entries'),
        paginate: {
          next: '<i class="ri-arrow-right-s-line"></i>',
          previous: '<i class="ri-arrow-left-s-line"></i>'
        }
      },
      // Buttons with Dropdown
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-outline-secondary dropdown-toggle me-4 waves-effect waves-light',
          text: '<i class="ri-upload-2-line ri-16px me-2"></i><span class="d-none d-sm-inline-block">' + __('Export') + ' </span>',
          buttons: [
            {
              extend: 'print',
              title: __('Raw Materials'),
              text: '<i class="ri-printer-line me-1" ></i>' + __('Print'),
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'csv',
              title: __('Raw Materials'),
              text: '<i class="ri-file-text-line me-1" ></i>' + __('Csv'),
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'excel',
              title: __('Raw Materials'),
              text: '<i class="ri-file-excel-line me-1"></i>' + __('Excel'),
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'pdf',
              title: __('Raw Materials'),
              text: '<i class="ri-file-pdf-line me-1"></i>' + __('Pdf'),
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            }
          ]
        },
        {
          text: '<i class="ri-add-line ri-16px me-0 me-sm-2 align-baseline"></i><span class="d-none d-sm-inline-block">' + __('Add New Material') + '</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddRawMaterial'
          }
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return __('Details of') + ' ' + data['name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== ''
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }

  // Delete Record
  $(document).on('click', '.delete-record', function () {
    var material_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // sweetalert for confirmation of delete
    Swal.fire({
      title: __('Are you sure?'),
      text: __("You won't be able to revert this!"),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: __('Yes, delete it!'),
      customClass: {
        confirmButton: 'btn btn-primary me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        // delete the data
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}raw-materials/${material_id}`,
          success: function (response) {
            dt_raw_material.draw();
            Swal.fire({
              icon: 'success',
              title: __('Deleted!'),
              text: response.message || __('The raw material has been deleted!'),
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function (xhr) {
            var response = xhr.responseJSON;
            Swal.fire({
              icon: 'error',
              title: __('Error!'),
              text: response.message || __('Error deleting raw material'),
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Edit record
  $(document).on('click', '.edit-record', function () {
    var material_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // changing the title of offcanvas
    $('#offcanvasAddRawMaterialLabel').html(__('Edit Raw Material'));

    // get data
    $.get(`${baseUrl}raw-materials/${material_id}/edit`, function (data) {
      $('#raw_material_id').val(data.id);
      $('#add-material-name').val(data.name);
      $('#add-material-description').val(data.description);
      $('#add-material-unit').val(data.unit_id);
      $('#add-material-current-stock').val(data.current_stock);
      $('#add-material-minimum-stock').val(data.minimum_stock);
      $('#add-material-unit-price').val(data.unit_price);
      $('#add-material-supplier').val(data.supplier);
      $('#add-material-image-alt').val(data.image_alt);
      $('#add-material-active').prop('checked', data.is_active);

      // Handle existing image
      if (data.image_path) {
        imagePreview.attr('src', data.image_url);
        imagePreviewContainer.show();
      } else {
        imagePreview.attr('src', '');
        imagePreviewContainer.hide();
      }

      // Clear file input
      imageInput.val('');
    });
  });

  // Reset form when adding new material
  $('.add-new').on('click', function () {
    $('#raw_material_id').val('');
    $('#offcanvasAddRawMaterialLabel').html(__('Add Raw Material'));
    $('#addNewRawMaterialForm')[0].reset();
    $('#add-material-active').prop('checked', true);

    // Reset image preview
    imageInput.val('');
    imagePreview.attr('src', '');
    imagePreviewContainer.hide();

    loadUnits(); // Reload units
  });

  // Form validation and submission
  const addNewRawMaterialForm = document.getElementById('addNewRawMaterialForm');

  if (addNewRawMaterialForm) {
    const fv = FormValidation.formValidation(addNewRawMaterialForm, {
    fields: {
      name: {
        validators: {
          notEmpty: {
            message: __('Please enter material name')
          }
        }
      },
      unit_id: {
        validators: {
          notEmpty: {
            message: __('Please select a unit')
          }
        }
      },
      current_stock: {
        validators: {
          notEmpty: {
            message: __('Please enter current stock')
          },
          numeric: {
            message: __('Current stock must be a number')
          }
        }
      },
      minimum_stock: {
        validators: {
          notEmpty: {
            message: __('Please enter minimum stock')
          },
          numeric: {
            message: __('Minimum stock must be a number')
          }
        }
      }
    },
    plugins: {
      trigger: new FormValidation.plugins.Trigger(),
      bootstrap5: new FormValidation.plugins.Bootstrap5({
        eleValidClass: '',
        rowSelector: function (field, ele) {
          return '.mb-5';
        }
      }),
      submitButton: new FormValidation.plugins.SubmitButton(),
      autoFocus: new FormValidation.plugins.AutoFocus()
    }
  }).on('core.form.valid', function () {
    // Submit form via AJAX
    var formData = new FormData(addNewRawMaterialForm);

    $.ajax({
      type: 'POST',
      url: baseUrl + 'raw-materials',
      data: formData,
      processData: false,
      contentType: false,
      success: function (response) {
        dt_raw_material.draw();
        offCanvasForm.offcanvas('hide');

        Swal.fire({
          icon: 'success',
          title: __('Success!'),
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function (xhr) {
        var response = xhr.responseJSON;
        if (response.errors) {
          // Handle validation errors
          Object.keys(response.errors).forEach(function(key) {
            fv.updateFieldStatus(key, 'Invalid', 'notEmpty');
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: __('Error!'),
            text: response.message || __('Something went wrong'),
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      }
    });
  });
  }

  // View Movements button click
  $(document).on('click', '.view-movements', function () {
    var materialId = $(this).data('id');
    var materialName = $(this).data('name');

    // Load movements data
    $.get(`${baseUrl}raw-materials/${materialId}/movements`, function (response) {
      var material = response.material;
      var movements = response.movements;

      // Update modal title and material info
      $('#movementsModalLabel').text(__('Stock Movements') + ' - ' + material.name);
      $('#movements-material-name').text(material.name);
      $('#movements-current-stock').text(material.current_stock + ' ' + material.unit);

      // Clear and populate movements table
      var movementsTableBody = $('#movements-table-body');
      movementsTableBody.empty();

      if (movements.length === 0) {
        movementsTableBody.append(`
          <tr>
            <td colspan="7" class="text-center text-muted">${__('No movements found')}</td>
          </tr>
        `);
      } else {
        movements.forEach(function(movement) {
          var typeClass = movement.is_positive ? 'text-success' : 'text-danger';
          var typeIcon = movement.is_positive ? 'ri-arrow-up-line' : 'ri-arrow-down-line';
          var quantitySign = movement.is_positive ? '+' : '-';

          movementsTableBody.append(`
            <tr>
              <td>${movement.movement_date}</td>
              <td>
                <span class="${typeClass}">
                  <i class="${typeIcon}"></i> ${movement.movement_type_label}
                </span>
              </td>
              <td class="${typeClass}">${quantitySign}${movement.absolute_quantity}</td>
              <td>${movement.unit_price || '-'}</td>
              <td>${movement.stock_before}</td>
              <td>${movement.stock_after}</td>
              <td>
                <small class="text-muted">${movement.reason}</small>
                ${movement.notes ? '<br><small class="text-muted">' + movement.notes + '</small>' : ''}
                ${movement.document_reference ? '<br><small class="text-primary">' + movement.document_reference + '</small>' : ''}
              </td>
            </tr>
          `);
        });
      }

      // Show modal
      $('#movementsModal').modal('show');
    }).fail(function() {
      Swal.fire({
        icon: 'error',
        title: __('Error!'),
        text: __('Failed to load movements data'),
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    });
  });

  // Add Stock button click
  $(document).on('click', '.add-stock', function () {
    var materialId = $(this).data('id');
    var materialName = $(this).data('name');
    var materialUnit = $(this).data('unit');

    // Update modal title and material info
    $('#addStockModalLabel').text(__('Add Stock') + ' - ' + materialName);
    $('#add-stock-material-name').text(materialName);
    $('#add-stock-unit-label').text('(' + materialUnit + ')');
    $('#add-stock-material-id').val(materialId);

    // Reset form
    $('#addStockForm')[0].reset();

    // Show modal
    $('#addStockModal').modal('show');
  });

  // Add Stock form submission
  $(document).on('submit', '#addStockForm', function (e) {
    e.preventDefault();

    var materialId = $('#add-stock-material-id').val();
    var formData = new FormData(this);

    $.ajax({
      type: 'POST',
      url: `${baseUrl}raw-materials/${materialId}/add-stock`,
      data: formData,
      processData: false,
      contentType: false,
      success: function (response) {
        dt_raw_material.draw();
        $('#addStockModal').modal('hide');

        Swal.fire({
          icon: 'success',
          title: __('Success!'),
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function (xhr) {
        var response = xhr.responseJSON;
        if (response.errors) {
          // Display validation errors
          var errorMessages = Object.values(response.errors).flat().join('<br>');
          Swal.fire({
            icon: 'error',
            title: __('Validation Error!'),
            html: errorMessages,
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: __('Error!'),
            text: response.message || __('Something went wrong'),
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      }
    });
  });
});
