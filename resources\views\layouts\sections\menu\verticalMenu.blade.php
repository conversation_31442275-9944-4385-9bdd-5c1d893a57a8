@php
use Illuminate\Support\Facades\Route;
$configData = Helper::appClasses();

// Define menu structure with permission checks
$menuItems = [
    // Dashboard - Always visible for authenticated users
    [
        'name' => 'Dashboard',
        'icon' => 'menu-icon tf-icons ri-home-smile-line',
        'slug' => 'dashboard',
        'url' => '/',
        'permission' => null // Always visible
    ],

    // Manufacturing Section
    [
        'menuHeader' => 'Manufacturing',
        'permission' => 'view-units|view-raw-materials|view-finished-products|view-manufacturing-orders|view-purchases'
    ],
    
    [
                'url' => 'raw-materials',
                'name' => 'Raw Materials',
                'slug' => 'raw-materials.index',
                'permission' => 'view-raw-materials',
                'icon' => 'menu-icon tf-icons ri-shopping-cart-line',
    ],
    [
                'url' => 'finished-products',
                'name' => 'Finished Products',
                'slug' => 'finished-products.index',
                'permission' => 'view-finished-products',
                'icon' => 'menu-icon tf-icons ri-shopping-cart-line',
    ],
    [
                'url' => 'manufacturing-orders',
                'name' => 'Manufacturing Orders',
                'slug' => 'manufacturing-orders.index',
                'permission' => 'view-manufacturing-orders',
                'icon' => 'menu-icon tf-icons ri-shopping-cart-line',
    ],
    [
                'url' => 'purchases',
                'name' => 'Purchases',
                'slug' => 'purchases.index',
                'permission' => 'view-purchases',
                'icon' => 'menu-icon tf-icons ri-shopping-cart-line',
    ],
       

    // POS Section
    [
        'name' => 'Point of Sale',
        'icon' => 'menu-icon tf-icons ri-shopping-cart-line',
        'slug' => 'pos',
        'url' => 'pos',
        'permission' => 'access-pos'
    ],

    // Menu Header for Admin Section
    [
        'menuHeader' => 'Administration',
        'permission' => 'view-users|view-roles|manage-settings' // Show header if user can see any admin feature
    ],

    // User Management
    
            [
                'url' => 'users',
                'name' => 'User Management',
                'slug' => 'laravel-example-user-management',
                'permission' => 'view-users'
            ] ,
    

    // Roles & Permissions
    
            [
                'url' => 'roles',
                'name' => 'Roles Management',
                'slug' => 'roles.index',
                'permission' => 'view-roles'
            ] ,
       

    // Settings
    [
        'name' => 'Settings',
        'icon' => 'menu-icon tf-icons ri-settings-3-line',
        'slug' => 'app-settings',
        'permission' => 'manage-settings',
        'submenu' => [
            [
                'url' => 'currency-settings',
                'name' => 'Currency Settings',
                'slug' => 'currency-settings.index',
                'permission' => 'manage-settings'
],[
                'url' => 'units',
                'name' => 'Units',
                'slug' => 'units.index',
                'permission' => 'view-units'
            ]
        ]
    ]
];

// Function to check if user has permission
if (!function_exists('checkMenuPermission')) {
    function checkMenuPermission($permission) {
        if (!$permission) return true; // No permission required

        // Super Admin has access to everything
        if (auth()->user()->hasRole('Super Admin')) {
            return true;
        }

        if (strpos($permission, '|') !== false) {
            // Multiple permissions (OR condition)
            $permissions = explode('|', $permission);
            foreach ($permissions as $perm) {
                if (auth()->user()->can(trim($perm))) {
                    return true;
                }
            }
            return false;
        }

        return auth()->user()->can($permission);
    }
}

// Filter menu items based on permissions
$filteredMenu = [];
foreach ($menuItems as $item) {
    // Check if it's a menu header
    if (isset($item['menuHeader'])) {
        if (checkMenuPermission($item['permission'] ?? null)) {
            $filteredMenu[] = $item;
        }
        continue;
    }

    // Check if item has submenu
    if (isset($item['submenu'])) {
        // Filter submenu items
        $filteredSubmenu = [];
        foreach ($item['submenu'] as $subItem) {
            if (checkMenuPermission($subItem['permission'] ?? null)) {
                $filteredSubmenu[] = $subItem;
            }
        }

        // Only include parent if it has visible submenu items
        if (!empty($filteredSubmenu)) {
            $item['submenu'] = $filteredSubmenu;
            $filteredMenu[] = $item;
        }
    } else {
        // Single menu item
        if (checkMenuPermission($item['permission'] ?? null)) {
            $filteredMenu[] = $item;
        }
    }
}

// Convert to object format for compatibility with existing menu system
$menuData = (object) ['menu' => array_map(function($item) {
    return (object) array_map(function($value) {
        if (is_array($value)) {
            return array_map(function($subItem) {
                return (object) $subItem;
            }, $value);
        }
        return $value;
    }, $item);
}, $filteredMenu)];

$verticalMenuData = $menuData;
@endphp

<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">

  <!-- ! Hide app brand if navbar-full -->
  @if(!isset($navbarFull))
  <div class="app-brand demo">
    <a href="{{url('/')}}" class="app-brand-link">
      <span class="app-brand-logo demo">@include('_partials.macros',["width"=>25,"withbg"=>'var(--bs-primary)'])</span>
      <span class="app-brand-text demo menu-text fw-semibold ms-2">{{config('variables.templateName')}}</span>
    </a>

    <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.47365 11.7183C8.11707 12.0749 8.11707 12.6531 8.47365 13.0097L12.071 16.607C12.4615 16.9975 12.4615 17.6305 12.071 18.021C11.6805 18.4115 11.0475 18.4115 10.657 18.021L5.83009 13.1941C5.37164 12.7356 5.37164 11.9924 5.83009 11.5339L10.657 6.707C11.0475 6.31653 11.6805 6.31653 12.071 6.707C12.4615 7.09747 12.4615 7.73053 12.071 8.121L8.47365 11.7183Z" fill-opacity="0.9" />
        <path d="M14.3584 11.8336C14.0654 12.1266 14.0654 12.6014 14.3584 12.8944L18.071 16.607C18.4615 16.9975 18.4615 17.6305 18.071 18.021C17.6805 18.4115 17.0475 18.4115 16.657 18.021L11.6819 13.0459C11.3053 12.6693 11.3053 12.0587 11.6819 11.6821L16.657 6.707C17.0475 6.31653 17.6805 6.31653 18.071 6.707C18.4615 7.09747 18.4615 7.73053 18.071 8.121L14.3584 11.8336Z" fill-opacity="0.4" />
      </svg>
    </a>
  </div>
  @endif

  <div class="menu-inner-shadow"></div>

  <ul class="menu-inner py-1">
    @if(isset($verticalMenuData) && isset($verticalMenuData->menu))
      @foreach ($verticalMenuData->menu as $menu)

      {{-- adding active and open class if child is active --}}

      {{-- menu headers --}}
      @if (isset($menu->menuHeader))
        <li class="menu-header mt-5">
            <span class="menu-header-text">{{ __($menu->menuHeader) }}</span>
        </li>
      @else

      {{-- active menu method --}}
      @php
      $activeClass = null;
      $currentRouteName = Route::currentRouteName();

      if ($currentRouteName === $menu->slug) {
        $activeClass = 'active';
      }
      elseif (isset($menu->submenu)) {
        if (gettype($menu->slug) === 'array') {
          foreach($menu->slug as $slug){
            if (str_contains($currentRouteName,$slug) and strpos($currentRouteName,$slug) === 0) {
              $activeClass = 'active open';
            }
          }
        }
        else{
          if (str_contains($currentRouteName,$menu->slug) and strpos($currentRouteName,$menu->slug) === 0) {
            $activeClass = 'active open';
          }
        }
      }
      @endphp

      {{-- main menu --}}
      <li class="menu-item {{$activeClass}}">
        <a href="{{ isset($menu->url) ? url($menu->url) : 'javascript:void(0);' }}" class="{{ isset($menu->submenu) ? 'menu-link menu-toggle' : 'menu-link' }}" @if (isset($menu->target) and !empty($menu->target)) target="_blank" @endif>
          @isset($menu->icon)
            <i class="{{ $menu->icon }}"></i>
          @endisset
          <div>{{ isset($menu->name) ? __($menu->name) : '' }}</div>
          @isset($menu->badge)
            <div class="badge bg-{{ $menu->badge[0] }} rounded-pill ms-auto">{{ $menu->badge[1] }}</div>
          @endisset
        </a>

        {{-- submenu --}}
        @isset($menu->submenu)
          @include('layouts.sections.menu.submenu',['menu' => $menu->submenu])
        @endisset
      </li>
      @endif
    @endforeach
    @endif
  </ul>

</aside>
