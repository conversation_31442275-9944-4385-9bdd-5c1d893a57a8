/*
 * Manufacturing Orders Management
 */

'use strict';

// Datatable (jquery)
$(function () {
  // Variable declaration for table
  var dt_manufacturing_order_table = $('.datatables-manufacturing-orders'),
    offCanvasForm = $('#offcanvasAddOrder'),
    orderDetailsModal = $('#orderDetailsModal'),
    completeOrderModal = $('#completeOrderModal');

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Initialize flatpickr for date picker
  if (document.getElementById('add-order-date')) {
    flatpickr('#add-order-date', {
      dateFormat: 'Y-m-d',
      minDate: 'today'
    });
  }

  // Variables for multi-product management
  let productItems = [];
  let finishedProducts = [];

  // Load finished products when page loads
  loadFinishedProducts();

  // Order type change handler
  $('#add-order-type').on('change', function() {
    const orderType = $(this).val();
    toggleOrderTypeFields(orderType);
  });

  // Add product item functionality
  $('#add-product-item').on('click', function() {
    addProductItem();
  });

  // Load finished products
  function loadFinishedProducts() {
    $.get(baseUrl + 'finished-products/active', function(data) {
      finishedProducts = data;

      // Update single product dropdown
      const singleSelect = $('#add-order-product');
      singleSelect.empty().append('<option value="">Select Product</option>');
      data.forEach(product => {
        singleSelect.append(`<option value="${product.id}" data-cost="${product.production_cost}" data-unit="${product.unit?.symbol || ''}">${product.name}</option>`);
      });
    }).fail(function() {
      console.error('Failed to load finished products');
    });
  }

  // Toggle order type fields
  function toggleOrderTypeFields(orderType) {
    if (orderType === 'multi') {
      $('#single-product-fields').hide();
      $('#single-time-field').hide();
      $('#multi-product-fields').show();
      $('#multi-time-field').show();

      // Add initial product item if none exists
      if ($('.product-item').length === 0) {
        addProductItem();
      }
    } else {
      $('#single-product-fields').show();
      $('#single-time-field').show();
      $('#multi-product-fields').hide();
      $('#multi-time-field').hide();

      // Clear multi-product items
      $('#product-items-container').empty();
      productItems = [];
    }
  }

  // Add product item
  function addProductItem() {
    const itemIndex = productItems.length;
    const itemHtml = `
      <div class="product-item border rounded p-3 mb-3" data-index="${itemIndex}">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Product ${itemIndex + 1}</h6>
          <button type="button" class="btn btn-sm btn-outline-danger remove-product-item">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>

        <div class="row g-3">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <select class="form-select product-select" name="items[${itemIndex}][finished_product_id]" required>
                <option value="">Select Product</option>
                ${finishedProducts.map(product =>
                  `<option value="${product.id}" data-cost="${product.production_cost}" data-unit="${product.unit?.symbol || ''}">${product.name}</option>`
                ).join('')}
              </select>
              <label>Finished Product</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <input type="number" step="0.001" class="form-control quantity-input"
                     name="items[${itemIndex}][planned_quantity]" placeholder="0.000" required min="0.001">
              <label>Planned Quantity</label>
            </div>
            <small class="text-muted unit-display"></small>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <input type="number" class="form-control time-input"
                     name="items[${itemIndex}][estimated_time_minutes]" placeholder="0" min="0">
              <label>Est. Time (min)</label>
            </div>
          </div>
        </div>

        <div class="row g-3 mt-2">
          <div class="col-md-9">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control" name="items[${itemIndex}][notes]"
                        placeholder="Item notes..." rows="2"></textarea>
              <label>Notes</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="d-flex align-items-center h-100">
              <div class="text-end">
                <small class="text-muted">Est. Cost:</small>
                <div class="h6 mb-0 estimated-cost">0.00</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    $('#product-items-container').append(itemHtml);
    productItems.push({
      finished_product_id: '',
      planned_quantity: 0,
      estimated_time_minutes: 0,
      notes: ''
    });

    // Update item indices
    updateProductItemIndices();
  }

  // Remove product item
  $(document).on('click', '.remove-product-item', function() {
    const $item = $(this).closest('.product-item');
    const index = parseInt($item.data('index'));

    $item.remove();
    productItems.splice(index, 1);

    updateProductItemIndices();
    calculateTotals();
  });

  // Product selection change
  $(document).on('change', '.product-select', function() {
    const $select = $(this);
    const $item = $select.closest('.product-item');
    const selectedOption = $select.find('option:selected');
    const unit = selectedOption.data('unit') || '';
    const cost = selectedOption.data('cost') || 0;

    $item.find('.unit-display').text(unit ? `Unit: ${unit}` : '');

    // Update estimated cost
    calculateItemCost($item);
  });

  // Quantity input changes
  $(document).on('input', '.quantity-input', function() {
    const $item = $(this).closest('.product-item');
    calculateItemCost($item);
    calculateTotals();
  });

  // Time input changes
  $(document).on('input', '.time-input', function() {
    calculateTotals();
  });

  // Calculate estimated cost for an item
  function calculateItemCost($item) {
    const quantity = parseFloat($item.find('.quantity-input').val()) || 0;
    const selectedOption = $item.find('.product-select option:selected');
    const unitCost = parseFloat(selectedOption.data('cost')) || 0;
    const estimatedCost = quantity * unitCost;

    $item.find('.estimated-cost').text(estimatedCost.toFixed(2));
  }

  // Calculate totals for multi-product orders
  function calculateTotals() {
    if ($('#add-order-type').val() !== 'multi') {
      return;
    }

    let totalTime = 0;
    let totalCost = 0;

    $('.product-item').each(function() {
      const time = parseInt($(this).find('.time-input').val()) || 0;
      const cost = parseFloat($(this).find('.estimated-cost').text()) || 0;
      totalTime += time;
      totalCost += cost;
    });

    $('#add-order-total-time').val(totalTime);

    // You could add a total cost display here if needed
  }

  // Update item indices after adding/removing items
  function updateProductItemIndices() {
    $('.product-item').each(function(index) {
      $(this).attr('data-index', index);
      $(this).find('h6').text(`Product ${index + 1}`);

      // Update input names
      $(this).find('select[name*="finished_product_id"]').attr('name', `items[${index}][finished_product_id]`);
      $(this).find('input[name*="planned_quantity"]').attr('name', `items[${index}][planned_quantity]`);
      $(this).find('input[name*="estimated_time_minutes"]').attr('name', `items[${index}][estimated_time_minutes]`);
      $(this).find('textarea[name*="notes"]').attr('name', `items[${index}][notes]`);
    });
  }

  // Load finished products for dropdown
  function loadFinishedProducts() {
    $.get(baseUrl + 'finished-products/active', function(data) {
      var productSelect = $('#add-order-product');
      productSelect.empty().append('<option value="">Select Product</option>');

      data.forEach(function(product) {
        productSelect.append(`<option value="${product.id}"
          data-cost="${product.production_cost || 0}"
          data-max-qty="${product.max_producible_quantity || 0}">
          ${product.name} (${product.current_stock} ${product.unit.symbol})
        </option>`);
      });
    });
  }

  // Load products on page load
  loadFinishedProducts();

  // Form validation for manufacturing order
  const addNewOrderForm = document.getElementById('addNewOrderForm');
  if (addNewOrderForm) {
    const fv = FormValidation.formValidation(addNewOrderForm, {
      fields: {
        finished_product_id: {
          validators: {
            notEmpty: {
              message: 'Please select a finished product'
            }
          }
        },
        planned_quantity: {
          validators: {
            notEmpty: {
              message: 'Please enter planned quantity'
            },
            numeric: {
              message: 'Planned quantity must be a number'
            },
            greaterThan: {
              message: 'Planned quantity must be greater than 0',
              min: 0.001
            }
          }
        },
        planned_date: {
          validators: {
            notEmpty: {
              message: 'Please select planned date'
            },
            date: {
              format: 'YYYY-MM-DD',
              message: 'Please enter a valid date'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: function (field, ele) {
            return '.mb-5';
          }
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    }).on('core.form.valid', function () {
      // Validate multi-product items if needed
      if ($('#add-order-type').val() === 'multi') {
        if ($('.product-item').length === 0) {
          Swal.fire({
            icon: 'warning',
            title: 'Warning!',
            text: 'Please add at least one product to manufacture.',
            customClass: {
              confirmButton: 'btn btn-warning'
            }
          });
          return;
        }

        // Validate that all items have required fields
        let hasErrors = false;
        $('.product-item').each(function() {
          const productId = $(this).find('.product-select').val();
          const quantity = $(this).find('.quantity-input').val();

          if (!productId || !quantity) {
            hasErrors = true;
            return false;
          }
        });

        if (hasErrors) {
          Swal.fire({
            icon: 'warning',
            title: 'Warning!',
            text: 'Please fill in all required fields for all products.',
            customClass: {
              confirmButton: 'btn btn-warning'
            }
          });
          return;
        }
      }

      // Submit form via AJAX
      var formData = new FormData(addNewOrderForm);

      $.ajax({
        type: 'POST',
        url: baseUrl + 'manufacturing-orders',
        data: formData,
        processData: false,
        contentType: false,
        success: function (response) {
          dt_manufacturing_order.draw();
          offCanvasForm.offcanvas('hide');

          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: response.message,
            customClass: {
              confirmButton: 'btn btn-success'
            }
          });

          // Reset form
          resetOrderForm();
        },
        error: function (xhr) {
          var response = xhr.responseJSON;
          if (response.errors) {
            // Handle validation errors
            Object.keys(response.errors).forEach(function(key) {
              if (key.startsWith('items.')) {
                // Handle item validation errors
                console.error('Item validation error:', key, response.errors[key]);
              } else {
                fv.updateFieldStatus(key, 'Invalid', 'notEmpty');
              }
            });
          } else {
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: response.message || 'Something went wrong',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        }
      });
    });
  }

  // Show product info when product is selected
  $('#add-order-product').on('change', function() {
    var selectedOption = $(this).find('option:selected');
    var cost = selectedOption.data('cost') || 0;
    var maxQty = selectedOption.data('max-qty') || 0;

    if ($(this).val()) {
      $('#product-cost').text(window.formatCurrency ? window.formatCurrency(cost) : '$' + parseFloat(cost).toFixed(2));
      $('#product-max-qty').text(maxQty + ' units');
      $('#product-info').show();
    } else {
      $('#product-info').hide();
    }
  });

  // Manufacturing Orders datatable
  if (dt_manufacturing_order_table.length) {
    var dt_manufacturing_order = dt_manufacturing_order_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: baseUrl + 'manufacturing-orders/data'
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'order_number' },
        { data: 'finished_product' },
        { data: 'planned_quantity' },
        { data: 'completion_percentage' },
        { data: 'planned_date' },
        { data: 'status' },
        { data: 'action' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          // Order number with overdue indicator
          targets: 1,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $orderNumber = full['order_number'];
            var $isOverdue = full['is_overdue'];

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $orderNumber + '</span>' +
              ($isOverdue ? '<small class="text-danger"><i class="ri-alarm-warning-line"></i> Overdue</small>' : '') +
              '</div>';
          }
        },
        {
          // Product name with unit
          targets: 2,
          render: function (data, type, full, meta) {
            var $product = full['finished_product'];
            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $product.name + '</span>' +
              '<small class="text-muted">Unit: ' + $product.unit.symbol + '</small>' +
              '</div>';
          }
        },
        {
          // Planned vs produced quantity
          targets: 3,
          render: function (data, type, full, meta) {
            var $planned = parseFloat(full['planned_quantity']).toFixed(3);
            var $produced = parseFloat(full['produced_quantity']).toFixed(3);
            var $unit = full['finished_product']['unit']['symbol'];

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $planned + ' ' + $unit + '</span>' +
              '<small class="text-muted">Produced: ' + $produced + ' ' + $unit + '</small>' +
              '</div>';
          }
        },
        {
          // Progress bar
          targets: 4,
          render: function (data, type, full, meta) {
            var $percentage = full['completion_percentage'];
            var $remaining = full['remaining_quantity'];
            var $unit = full['finished_product']['unit']['symbol'];

            var progressColor = 'primary';
            if ($percentage >= 100) progressColor = 'success';
            else if ($percentage >= 50) progressColor = 'info';
            else if ($percentage > 0) progressColor = 'warning';

            return '<div class="d-flex flex-column">' +
              '<div class="progress mb-1" style="height: 6px;">' +
              '<div class="progress-bar bg-' + progressColor + '" style="width: ' + $percentage + '%"></div>' +
              '</div>' +
              '<small class="text-muted">' + $percentage.toFixed(1) + '% (' + $remaining.toFixed(3) + ' ' + $unit + ' remaining)</small>' +
              '</div>';
          }
        },
        {
          // Planned date with time info
          targets: 5,
          render: function (data, type, full, meta) {
            var $date = new Date(full['planned_date']).toLocaleDateString();
            var $estimatedTime = full['estimated_time_formatted'];
            var $actualTime = full['actual_time_formatted'];

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $date + '</span>' +
              '<small class="text-muted">Est: ' + $estimatedTime + '</small>' +
              ($actualTime !== 'N/A' ? '<small class="text-info">Act: ' + $actualTime + '</small>' : '') +
              '</div>';
          }
        },
        {
          // Status with action indicators
          targets: 6,
          className: 'text-center',
          render: function (data, type, full, meta) {
            var $status = full['status'];
            var $statusLabel = full['status_label'];
            var $statusColor = full['status_color'];
            var $canStart = full['can_start'];
            var $canComplete = full['can_complete'];

            var statusBadge = '<span class="badge bg-label-' + $statusColor + '">' + $statusLabel + '</span>';

            if ($canStart) {
              statusBadge += '<br><small class="text-success"><i class="ri-play-circle-line"></i> Can Start</small>';
            } else if (full['status'] === 'planned') {
              var reason = full['cannot_start_reason'] || 'Cannot start';
              statusBadge += '<br><small class="text-danger" title="' + reason + '"><i class="ri-stop-circle-line"></i> Cannot Start</small>';
            }
            if ($canComplete) {
              statusBadge += '<br><small class="text-warning"><i class="ri-check-circle-line"></i> Can Complete</small>';
            } else if (full['status'] === 'in_progress' && full['cannot_complete_reason']) {
              statusBadge += '<br><small class="text-danger" title="' + full['cannot_complete_reason'] + '"><i class="ri-alert-line"></i> Cannot Complete</small>';
            }

            return statusBadge;
          }
        },
        {
          // Actions
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            var actions = '<div class="d-flex align-items-center gap-50">';

            // Edit button (only for planned orders)
            if (full['status'] === 'planned') {
              actions += `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddOrder" title="Edit Order"><i class="ri-edit-box-line ri-20px"></i></button>`;
            }

            // Delete button (only for planned orders)
            if (full['status'] === 'planned') {
              actions += `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" title="Delete Order"><i class="ri-delete-bin-7-line ri-20px"></i></button>`;
            }

            // More actions dropdown
            actions += '<div class="dropdown">';
            actions += '<button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false" title="More Actions"><i class="ri-more-2-line ri-20px"></i></button>';
            actions += '<ul class="dropdown-menu dropdown-menu-end">';

            // View details - always available
            actions += `<li><a href="javascript:;" class="dropdown-item view-details" data-id="${full['id']}"><i class="ri-eye-line me-2"></i>View Details</a></li>`;

            // Print order - always available
            actions += `<li><a href="${baseUrl}manufacturing-orders/${full['id']}/print" target="_blank" class="dropdown-item"><i class="ri-printer-line me-2"></i>Print Order</a></li>`;

            // Start order (if can start)
            if (full['can_start'] === true) {
              actions += `<li><a href="javascript:;" class="dropdown-item start-order" data-id="${full['id']}"><i class="ri-play-line me-2"></i>Start Order</a></li>`;
            } else if (full['status'] === 'planned') {
              // Show "Cannot Start" option for planned orders that can't be started
              var reason = full['cannot_start_reason'] || 'Insufficient raw materials or missing recipe';
              actions += `<li><a href="javascript:;" class="dropdown-item text-muted disabled" title="${reason}"><i class="ri-stop-line me-2"></i>Cannot Start</a></li>`;
            }

            // Manage consumption (if in progress)
            if (full['status'] === 'in_progress') {
              actions += `<li><a href="javascript:;" class="dropdown-item manage-consumption" data-id="${full['id']}" data-number="${full['order_number']}"><i class="ri-settings-3-line me-2"></i>Manage Consumption</a></li>`;
            }

            // Complete order (if can complete)
            if (full['can_complete'] === true) {
              actions += `<li><a href="javascript:;" class="dropdown-item complete-order" data-id="${full['id']}"><i class="ri-check-line me-2"></i>Complete Order</a></li>`;
            } else if (full['status'] === 'in_progress' && full['cannot_complete_reason']) {
              // Show "Cannot Complete" option for in-progress orders that can't be completed
              var reason = full['cannot_complete_reason'];
              actions += `<li><a href="javascript:;" class="dropdown-item text-muted disabled" title="${reason}"><i class="ri-stop-line me-2"></i>Cannot Complete</a></li>`;
            }

            // Divider if there are action items
            var hasActions = full['can_start'] || full['status'] === 'in_progress' || full['can_complete'] || full['status'] !== 'completed';
            if (hasActions) {
              actions += '<li><hr class="dropdown-divider"></li>';
            }

            // Cancel order (if not completed or cancelled)
            if (full['status'] !== 'completed' && full['status'] !== 'cancelled') {
              actions += `<li><a href="javascript:;" class="dropdown-item text-danger cancel-order" data-id="${full['id']}"><i class="ri-close-line me-2"></i>Cancel Order</a></li>`;
            }

            actions += '</ul></div></div>';

            return actions;
          }
        }
      ],
      order: [[1, 'desc']],
      dom:
        '<"card-header d-flex rounded-0 flex-wrap pb-md-0 pt-0"' +
        '<"me-5 ms-n2"f>' +
        '<"d-flex justify-content-start justify-content-md-end align-items-baseline"<"dt-action-buttons d-flex align-items-start align-items-md-center justify-content-sm-center gap-4"lB>>' +
        '>t' +
        '<"row mx-1"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 20, 50, 70, 100],
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: 'Search Orders',
        info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
        paginate: {
          next: '<i class="ri-arrow-right-s-line"></i>',
          previous: '<i class="ri-arrow-left-s-line"></i>'
        }
      },
      // Buttons with Dropdown
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-outline-secondary dropdown-toggle me-4 waves-effect waves-light',
          text: '<i class="ri-upload-2-line ri-16px me-2"></i><span class="d-none d-sm-inline-block">Export </span>',
          buttons: [
            {
              extend: 'print',
              title: 'Manufacturing Orders',
              text: '<i class="ri-printer-line me-1" ></i>Print',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            },
            {
              extend: 'csv',
              title: 'Manufacturing Orders',
              text: '<i class="ri-file-text-line me-1" ></i>Csv',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            },
            {
              extend: 'excel',
              title: 'Manufacturing Orders',
              text: '<i class="ri-file-excel-line me-1"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            },
            {
              extend: 'pdf',
              title: 'Manufacturing Orders',
              text: '<i class="ri-file-pdf-line me-1"></i>Pdf',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6]
              }
            }
          ]
        },
        {
          text: '<i class="ri-add-line ri-16px me-0 me-sm-2 align-baseline"></i><span class="d-none d-sm-inline-block">Add New Order</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddOrder'
          }
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Details of ' + data['order_number'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== ''
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }

  // Delete Record
  $(document).on('click', '.delete-record', function () {
    var order_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        confirmButton: 'btn btn-primary me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}manufacturing-orders/${order_id}`,
          success: function (response) {
            dt_manufacturing_order.draw();
            Swal.fire({
              icon: 'success',
              title: 'Deleted!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function (xhr) {
            var response = xhr.responseJSON;
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Edit record
  $(document).on('click', '.edit-record', function () {
    var order_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    $('#offcanvasAddOrderLabel').html('Edit Manufacturing Order');

    $.get(`${baseUrl}manufacturing-orders/${order_id}/edit`, function (data) {
      $('#order_id').val(data.id);
      $('#add-order-product').val(data.finished_product_id).trigger('change');
      $('#add-order-quantity').val(data.planned_quantity);
      $('#add-order-date').val(data.planned_date);
      $('#add-order-responsible').val(data.responsible_person);
      $('#add-order-time').val(data.estimated_time_minutes);
      $('#add-order-notes').val(data.notes);
    });
  });

  // Reset order form
  function resetOrderForm() {
    $('#addNewOrderForm')[0].reset();
    $('#add-order-type').val('single').trigger('change');
    $('#product-items-container').empty();
    productItems = [];
    $('#product-info').hide();
  }

  // Reset form when adding new order
  $('.add-new').on('click', function () {
    $('#offcanvasAddOrderLabel').html('Add Manufacturing Order');
    resetOrderForm();
  });

  // Start Order
  $(document).on('click', '.start-order', function () {
    var order_id = $(this).data('id');

    Swal.fire({
      title: 'Start Manufacturing Order?',
      text: "This will create material consumption records and change status to 'In Progress'.",
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, start it!',
      customClass: {
        confirmButton: 'btn btn-success me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        $.ajax({
          type: 'POST',
          url: `${baseUrl}manufacturing-orders/${order_id}/start`,
          success: function (response) {
            dt_manufacturing_order.draw();
            Swal.fire({
              icon: 'success',
              title: 'Started!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function (xhr) {
            var response = xhr.responseJSON;
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Cancel Order
  $(document).on('click', '.cancel-order', function () {
    var order_id = $(this).data('id');

    Swal.fire({
      title: 'Cancel Manufacturing Order?',
      text: "This action cannot be undone!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, cancel it!',
      customClass: {
        confirmButton: 'btn btn-danger me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        $.ajax({
          type: 'POST',
          url: `${baseUrl}manufacturing-orders/${order_id}/cancel`,
          success: function (response) {
            dt_manufacturing_order.draw();
            Swal.fire({
              icon: 'success',
              title: 'Cancelled!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function (xhr) {
            var response = xhr.responseJSON;
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: response.message,
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // View Details
  $(document).on('click', '.view-details', function () {
    var order_id = $(this).data('id');

    $.get(`${baseUrl}manufacturing-orders/${order_id}`)
      .done(function (data) {
        // Build order details HTML
        var html = buildOrderDetailsHTML(data);
        $('#order-details-content').html(html);

        // Add action buttons based on order status
        var actionButtons = buildActionButtons(data);
        $('#order-actions').html(actionButtons);

        $('#orderDetailsModal').modal('show');
      })
      .fail(function (xhr) {
        var response = xhr.responseJSON;
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: response && response.message ? response.message : 'Error loading order details',
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      });
  });

  // Complete Order
  $(document).on('click', '.complete-order', function () {
    var order_id = $(this).data('id');
    $('#complete-order-id').val(order_id);
    completeOrderModal.modal('show');
  });

  // Confirm Complete Order
  $('#confirm-complete-order').on('click', function () {
    var order_id = $('#complete-order-id').val();
    var formData = {
      produced_quantity: $('#complete-produced-quantity').val(),
      actual_time_minutes: $('#complete-actual-time').val(),
      notes: $('#complete-notes').val()
    };

    $.ajax({
      type: 'POST',
      url: `${baseUrl}manufacturing-orders/${order_id}/complete`,
      data: formData,
      success: function (response) {
        completeOrderModal.modal('hide');
        dt_manufacturing_order.draw();

        Swal.fire({
          icon: 'success',
          title: 'Completed!',
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function (xhr) {
        var response = xhr.responseJSON;
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      }
    });
  });

  // Build order details HTML
  function buildOrderDetailsHTML(order) {
    // Safe property access with fallbacks
    var orderNumber = order.order_number || 'N/A';
    var productName = order.finished_product ? order.finished_product.name : 'N/A';
    var unitSymbol = order.finished_product && order.finished_product.unit ? order.finished_product.unit.symbol : '';
    var plannedQuantity = order.planned_quantity || 0;
    var producedQuantity = order.produced_quantity || 0;
    var statusColor = order.status_color || 'secondary';
    var statusLabel = order.status_label || order.status || 'Unknown';
    var plannedDate = order.planned_date ? new Date(order.planned_date).toLocaleDateString() : 'N/A';
    var responsiblePerson = order.responsible_person || 'N/A';
    var estimatedTime = order.estimated_time_formatted || 'N/A';
    var actualTime = order.actual_time_formatted || 'N/A';
    var estimatedCost = order.estimated_cost || 0;
    var actualCost = order.actual_cost || 0;
    var startedAt = order.started_at ? new Date(order.started_at).toLocaleString() : 'N/A';
    var completedAt = order.completed_at ? new Date(order.completed_at).toLocaleString() : 'N/A';
    var createdBy = order.created_by ? order.created_by.name : 'N/A';

    var html = `
      <div class="row">
        <div class="col-md-6">
          <h6 class="fw-medium">Order Information</h6>
          <table class="table table-borderless table-sm">
            <tr><td class="text-muted">Order Number:</td><td class="fw-medium">${orderNumber}</td></tr>
            <tr><td class="text-muted">Product:</td><td>${productName}</td></tr>
            <tr><td class="text-muted">Planned Quantity:</td><td>${plannedQuantity} ${unitSymbol}</td></tr>
            <tr><td class="text-muted">Produced Quantity:</td><td>${producedQuantity} ${unitSymbol}</td></tr>
            <tr><td class="text-muted">Status:</td><td><span class="badge bg-${statusColor}">${statusLabel}</span></td></tr>
            <tr><td class="text-muted">Planned Date:</td><td>${plannedDate}</td></tr>
            <tr><td class="text-muted">Responsible Person:</td><td>${responsiblePerson}</td></tr>
          </table>
        </div>
        <div class="col-md-6">
          <h6 class="fw-medium">Production Details</h6>
          <table class="table table-borderless table-sm">
            <tr><td class="text-muted">Estimated Time:</td><td>${estimatedTime}</td></tr>
            <tr><td class="text-muted">Actual Time:</td><td>${actualTime}</td></tr>
            <tr><td class="text-muted">Estimated Cost:</td><td>${window.formatCurrency ? window.formatCurrency(estimatedCost) : '$' + estimatedCost.toFixed(2)}</td></tr>
            <tr><td class="text-muted">Actual Cost:</td><td>${window.formatCurrency ? window.formatCurrency(actualCost) : '$' + actualCost.toFixed(2)}</td></tr>
            <tr><td class="text-muted">Started At:</td><td>${startedAt}</td></tr>
            <tr><td class="text-muted">Completed At:</td><td>${completedAt}</td></tr>
            <tr><td class="text-muted">Created By:</td><td>${createdBy}</td></tr>
          </table>
        </div>
      </div>`;

    // Add notes if available
    if (order.notes) {
      html += `
        <div class="row mt-4">
          <div class="col-12">
            <h6 class="fw-medium">Notes</h6>
            <div class="alert alert-info">
              ${order.notes}
            </div>
          </div>
        </div>`;
    }

    // Add consumption details if available
    if (order.consumptions && order.consumptions.length > 0) {
      html += `
        <div class="row mt-4">
          <div class="col-12">
            <h6 class="fw-medium">Material Consumption</h6>
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Material</th>
                    <th>Planned Qty</th>
                    <th>Actual Qty</th>
                    <th>Unit Cost</th>
                    <th>Total Cost</th>
                    <th>Consumed At</th>
                  </tr>
                </thead>
                <tbody>`;

      order.consumptions.forEach(function(consumption) {
        var materialName = consumption.raw_material ? consumption.raw_material.name : 'N/A';
        var materialUnit = consumption.raw_material && consumption.raw_material.unit ? consumption.raw_material.unit.symbol : '';
        var plannedQty = consumption.planned_quantity || 0;
        var actualQty = consumption.actual_quantity || 'N/A';
        var unitCost = consumption.unit_cost || 0;
        var totalCost = consumption.total_cost || 0;
        var consumedAt = consumption.consumed_at ? new Date(consumption.consumed_at).toLocaleString() : 'N/A';

        html += `
          <tr>
            <td>${materialName}</td>
            <td>${plannedQty} ${materialUnit}</td>
            <td>${actualQty} ${materialUnit}</td>
            <td>${window.formatCurrency ? window.formatCurrency(unitCost) : '$' + unitCost.toFixed(2)}</td>
            <td>${window.formatCurrency ? window.formatCurrency(totalCost) : '$' + totalCost.toFixed(2)}</td>
            <td>${consumedAt}</td>
          </tr>`;
      });

      html += `
                </tbody>
              </table>
            </div>
          </div>
        </div>`;
    }

    // Add finished product movements if available
    if (order.finished_product_movements && order.finished_product_movements.length > 0) {
      html += `
        <div class="row mt-4">
          <div class="col-12">
            <h6 class="fw-medium">Production Output</h6>
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Quantity</th>
                    <th>Type</th>
                    <th>Reference</th>
                    <th>Notes</th>
                  </tr>
                </thead>
                <tbody>`;

      order.finished_product_movements.forEach(function(movement) {
        var createdAt = movement.created_at ? new Date(movement.created_at).toLocaleString() : 'N/A';
        var quantity = movement.quantity || 0;
        var unitSymbol = order.finished_product && order.finished_product.unit ? order.finished_product.unit.symbol : '';
        var reference = movement.reference || 'N/A';
        var reason = movement.reason || 'N/A';

        html += `
          <tr>
            <td>${createdAt}</td>
            <td>${quantity} ${unitSymbol}</td>
            <td><span class="badge bg-success">Production</span></td>
            <td>${reference}</td>
            <td>${reason}</td>
          </tr>`;
      });

      html += `
                </tbody>
              </table>
            </div>
          </div>
        </div>`;
    }

    return html;
  }

  // Build action buttons based on order status
  function buildActionButtons(order) {
    var buttons = '';

    // Start order button
    if (order.can_start) {
      buttons += `<button type="button" class="btn btn-success me-2 start-order" data-id="${order.id}">Start Order</button>`;
    } else if (order.status === 'planned') {
      // Show "Cannot Start" badge for planned orders that can't be started
      var reason = order.cannot_start_reason || 'Insufficient raw materials or missing recipe';
      buttons += `<span class="badge bg-label-danger me-2" title="${reason}">Cannot Start</span>`;
    }

    // Manage consumption button
    if (order.status === 'in_progress') {
      buttons += `<button type="button" class="btn btn-warning me-2 manage-consumption" data-id="${order.id}" data-number="${order.order_number}">Manage Consumption</button>`;
    }

    // Complete order button
    if (order.can_complete) {
      buttons += `<button type="button" class="btn btn-primary me-2 complete-order" data-id="${order.id}">Complete Order</button>`;
    }

    // Cancel order button
    if (order.status !== 'completed' && order.status !== 'cancelled') {
      buttons += `<button type="button" class="btn btn-danger cancel-order" data-id="${order.id}">Cancel Order</button>`;
    }

    return buttons;
  }
});