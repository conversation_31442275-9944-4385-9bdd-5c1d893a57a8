<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class MenuPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions for menu items
        $permissions = [
            // Manufacturing permissions
            'view-units',
            'create-units',
            'edit-units',
            'delete-units',

            'view-raw-materials',
            'create-raw-materials',
            'edit-raw-materials',
            'delete-raw-materials',

            'view-finished-products',
            'create-finished-products',
            'edit-finished-products',
            'delete-finished-products',

            'view-manufacturing-orders',
            'create-manufacturing-orders',
            'edit-manufacturing-orders',
            'delete-manufacturing-orders',

            'view-purchases',
            'create-purchases',
            'edit-purchases',
            'delete-purchases',

            // POS permissions
            'access-pos',
            'process-sales',

            // User management permissions
            'view-users',
            'create-users',
            'edit-users',
            'delete-users',

            // Role management permissions
            'view-roles',
            'create-roles',
            'edit-roles',
            'delete-roles',
            'manage-super-admin-roles',
        ];

        // Create permissions if they don't exist
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $this->createRoles();
    }

    /**
     * Create roles and assign permissions
     */
    private function createRoles()
    {
        // Super Admin - All permissions
        $superAdmin = Role::firstOrCreate(['name' => 'Super Admin']);
        $superAdmin->syncPermissions(Permission::all());

        // Admin - Most permissions except super admin functions
        $admin = Role::firstOrCreate(['name' => 'Admin']);
        $admin->syncPermissions([
            'view-units', 'create-units', 'edit-units', 'delete-units',
            'view-raw-materials', 'create-raw-materials', 'edit-raw-materials', 'delete-raw-materials',
            'view-finished-products', 'create-finished-products', 'edit-finished-products', 'delete-finished-products',
            'view-manufacturing-orders', 'create-manufacturing-orders', 'edit-manufacturing-orders', 'delete-manufacturing-orders',
            'view-purchases', 'create-purchases', 'edit-purchases', 'delete-purchases',
            'access-pos', 'process-sales',
            'view-users', 'create-users', 'edit-users',
            'view-roles', 'create-roles', 'edit-roles', 'delete-roles',
        ]);

        // Production Manager - Manufacturing and inventory management
        $productionManager = Role::firstOrCreate(['name' => 'Production Manager']);
        $productionManager->syncPermissions([
            'view-units', 'create-units', 'edit-units',
            'view-raw-materials', 'create-raw-materials', 'edit-raw-materials',
            'view-finished-products', 'create-finished-products', 'edit-finished-products',
            'view-manufacturing-orders', 'create-manufacturing-orders', 'edit-manufacturing-orders',
            'view-purchases', 'create-purchases', 'edit-purchases',
        ]);

        // Inventory Manager - Inventory and purchasing
        $inventoryManager = Role::firstOrCreate(['name' => 'Inventory Manager']);
        $inventoryManager->syncPermissions([
            'view-units',
            'view-raw-materials', 'create-raw-materials', 'edit-raw-materials',
            'view-finished-products', 'create-finished-products', 'edit-finished-products',
            'view-purchases', 'create-purchases', 'edit-purchases',
        ]);

        // Sales Manager - POS and sales
        $salesManager = Role::firstOrCreate(['name' => 'Sales Manager']);
        $salesManager->syncPermissions([
            'view-finished-products',
            'access-pos', 'process-sales',
        ]);

        // Production Operator - Limited manufacturing access
        $productionOperator = Role::firstOrCreate(['name' => 'Production Operator']);
        $productionOperator->syncPermissions([
            'view-units',
            'view-raw-materials',
            'view-finished-products',
            'view-manufacturing-orders', 'edit-manufacturing-orders',
        ]);

        // Cashier - POS only
        $cashier = Role::firstOrCreate(['name' => 'Cashier']);
        $cashier->syncPermissions([
            'view-finished-products',
            'access-pos', 'process-sales',
        ]);

        // Viewer - Read-only access
        $viewer = Role::firstOrCreate(['name' => 'Viewer']);
        $viewer->syncPermissions([
            'view-units',
            'view-raw-materials',
            'view-finished-products',
            'view-manufacturing-orders',
            'view-purchases',
        ]);

        $this->command->info('Roles and permissions created successfully!');
    }
}
