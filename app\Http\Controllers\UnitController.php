<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Unit;
use App\Models\UnitCategory;
use Illuminate\Support\Facades\Validator;

class UnitController extends Controller
{
    /**
     * Display the units management page.
     */
    public function index()
    {
        $units = Unit::with('category')->get();
        $totalUnits = $units->count();
        $activeUnits = Unit::active()->count();
        $inactiveUnits = $totalUnits - $activeUnits;
        $totalCategories = UnitCategory::count();
        $baseUnits = Unit::baseUnits()->count();

        return view('pages.units.index', [
            'totalUnits' => $totalUnits,
            'activeUnits' => $activeUnits,
            'inactiveUnits' => $inactiveUnits,
            'totalCategories' => $totalCategories,
            'baseUnits' => $baseUnits,
        ]);
    }

    /**
     * Get units data for DataTable.
     */
    public function getData(Request $request): JsonResponse
    {
        $columns = [
            1 => 'id',
            2 => 'name',
            3 => 'symbol',
            4 => 'description',
            5 => 'is_active',
        ];

        $totalData = Unit::count();
        $totalFiltered = $totalData;

        $limit = $request->input('length');
        $start = $request->input('start');
        $order = $columns[$request->input('order.0.column')] ?? 'id';
        $dir = $request->input('order.0.dir');

        if (empty($request->input('search.value'))) {
            $units = Unit::with('category')
                ->offset($start)
                ->limit($limit)
                ->orderBy($order, $dir)
                ->get();
        } else {
            $search = $request->input('search.value');

            $units = Unit::with('category')
                ->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('symbol', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%")
                      ->orWhereHas('category', function($categoryQuery) use ($search) {
                          $categoryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                })
                ->offset($start)
                ->limit($limit)
                ->orderBy($order, $dir)
                ->get();

            $totalFiltered = Unit::where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('symbol', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%")
                      ->orWhereHas('category', function($categoryQuery) use ($search) {
                          $categoryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                })
                ->count();
        }

        $data = [];

        if (!empty($units)) {
            $ids = $start;

            foreach ($units as $unit) {
                $nestedData['id'] = $unit->id;
                $nestedData['fake_id'] = ++$ids;
                $nestedData['name'] = $unit->name;
                $nestedData['symbol'] = $unit->symbol;
                $nestedData['description'] = $unit->description;
                $nestedData['category'] = $unit->category;
                $nestedData['category_name'] = $unit->category ? $unit->category->name : 'No Category';
                $nestedData['conversion_factor'] = $unit->conversion_factor;
                $nestedData['is_base_unit'] = $unit->is_base_unit;
                $nestedData['is_active'] = $unit->is_active;

                $data[] = $nestedData;
            }
        }

        return response()->json([
            'draw' => intval($request->input('draw')),
            'recordsTotal' => intval($totalData),
            'recordsFiltered' => intval($totalFiltered),
            'code' => 200,
            'data' => $data,
        ]);
    }

    /**
     * Store a newly created unit or update existing one.
     */
    public function store(Request $request): JsonResponse
    {
        // Convert checkbox value to boolean
        $requestData = $request->all();
        if (isset($requestData['is_active'])) {
            $requestData['is_active'] = in_array($requestData['is_active'], ['on', '1', 'true', true, 1], true);
        } else {
            $requestData['is_active'] = false;
        }

        $validator = Validator::make($requestData, [
            'name' => 'required|string|max:50|unique:units,name,' . $request->id,
            'symbol' => 'required|string|max:10|unique:units,symbol,' . $request->id,
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:unit_categories,id',
            'conversion_factor' => 'required|numeric|min:0.000001',
            'is_base_unit' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $unitId = $request->id;

        // Handle is_base_unit checkbox
        if (isset($requestData['is_base_unit'])) {
            $requestData['is_base_unit'] = in_array($requestData['is_base_unit'], ['on', '1', 'true', true, 1], true);
        } else {
            $requestData['is_base_unit'] = false;
        }

        if ($unitId) {
            // Update existing unit
            $unit = Unit::findOrFail($unitId);
            $unit->update([
                'name' => $requestData['name'],
                'symbol' => $requestData['symbol'],
                'description' => $requestData['description'],
                'category_id' => $requestData['category_id'],
                'conversion_factor' => $requestData['conversion_factor'],
                'is_base_unit' => $requestData['is_base_unit'],
                'is_active' => $requestData['is_active'],
            ]);

            return response()->json(['message' => 'Unit updated successfully']);
        } else {
            // Create new unit
            Unit::create([
                'name' => $requestData['name'],
                'symbol' => $requestData['symbol'],
                'description' => $requestData['description'],
                'category_id' => $requestData['category_id'],
                'conversion_factor' => $requestData['conversion_factor'],
                'is_base_unit' => $requestData['is_base_unit'],
                'is_active' => $requestData['is_active'],
            ]);

            return response()->json(['message' => 'Unit created successfully']);
        }
    }

    /**
     * Show the form for editing the specified unit.
     */
    public function edit($id): JsonResponse
    {
        $unit = Unit::with('category')->findOrFail($id);
        return response()->json($unit);
    }

    /**
     * Remove the specified unit from storage.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $unit = Unit::findOrFail($id);

            // Check if unit is being used
            if ($unit->rawMaterials()->count() > 0 || $unit->finishedProducts()->count() > 0 || $unit->recipes()->count() > 0) {
                return response()->json([
                    'message' => 'Cannot delete unit. It is being used by raw materials, finished products, or recipes.'
                ], 422);
            }

            $unit->delete();
            return response()->json(['message' => 'Unit deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error deleting unit'], 500);
        }
    }

    /**
     * Get active units for dropdowns.
     */
    public function getActiveUnits(): JsonResponse
    {
        $units = Unit::with('category')
            ->active()
            ->orderBy('name')
            ->get(['id', 'name', 'symbol', 'category_id', 'conversion_factor', 'is_base_unit']);
        return response()->json($units);
    }

    /**
     * Get active categories for dropdowns.
     */
    public function getActiveCategories(): JsonResponse
    {
        $categories = UnitCategory::active()->orderBy('name')->get(['id', 'name', 'description']);
        return response()->json($categories);
    }
}
