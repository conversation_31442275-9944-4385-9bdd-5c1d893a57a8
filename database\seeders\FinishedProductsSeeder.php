<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FinishedProduct;
use App\Models\Unit;

class FinishedProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get units for reference
        $pcs = Unit::where('symbol', 'pcs')->first();
        $kg = Unit::where('symbol', 'kg')->first();
        $dz = Unit::where('symbol', 'dz')->first();

        $finishedProducts = [
            // Plats de crevettes (Shrimp dishes)
            [
                'name' => 'Crevettes panées',
                'description' => 'Crevettes croustillantes panées au Panko',
                'unit_id' => $pcs->id,
                'current_stock' => 12.000,
                'minimum_stock' => 3.000,
                'selling_price' => 18.50,
                'is_active' => true,
            ],
            [
                'name' => 'Crevettes à l\'ail',
                'description' => 'Crevettes sautées au beurre à l\'ail et herbes fraîches',
                'unit_id' => $pcs->id,
                'current_stock' => 8.000,
                'minimum_stock' => 2.000,
                'selling_price' => 22.00,
                'is_active' => true,
            ],
            [
                'name' => 'Tempura de crevettes',
                'description' => 'Crevettes en tempura légère et croustillante',
                'unit_id' => $pcs->id,
                'current_stock' => 6.000,
                'minimum_stock' => 2.000,
                'selling_price' => 24.50,
                'is_active' => true,
            ],
            [
                'name' => 'Crevettes gratinées',
                'description' => 'Crevettes gratinées au fromage et épinards',
                'unit_id' => $pcs->id,
                'current_stock' => 5.000,
                'minimum_stock' => 1.000,
                'selling_price' => 26.00,
                'is_active' => true,
            ],

            // Plats de poisson (Fish dishes)
            [
                'name' => 'Filet de saumon grillé',
                'description' => 'Filet de saumon grillé aux herbes fraîches',
                'unit_id' => $pcs->id,
                'current_stock' => 8.000,
                'minimum_stock' => 2.000,
                'selling_price' => 28.00,
                'is_active' => true,
            ],
            [
                'name' => 'Poisson blanc meunière',
                'description' => 'Filet de poisson blanc à la meunière',
                'unit_id' => $pcs->id,
                'current_stock' => 10.000,
                'minimum_stock' => 3.000,
                'selling_price' => 24.00,
                'is_active' => true,
            ],
            [
                'name' => 'Fish and chips',
                'description' => 'Poisson pané à la chapelure avec frites',
                'unit_id' => $pcs->id,
                'current_stock' => 15.000,
                'minimum_stock' => 5.000,
                'selling_price' => 19.50,
                'is_active' => true,
            ],
            [
                'name' => 'Saumon en croûte',
                'description' => 'Saumon en croûte d\'herbes et fromage',
                'unit_id' => $pcs->id,
                'current_stock' => 4.000,
                'minimum_stock' => 1.000,
                'selling_price' => 32.00,
                'is_active' => true,
            ],

            // Fruits de mer premium (Premium seafood)
            [
                'name' => 'Saint-Jacques poêlées',
                'description' => 'Noix de Saint-Jacques poêlées au beurre',
                'unit_id' => $pcs->id,
                'current_stock' => 6.000,
                'minimum_stock' => 1.000,
                'selling_price' => 38.00,
                'is_active' => true,
            ],
            [
                'name' => 'Langoustines grillées',
                'description' => 'Langoustines grillées à l\'ail et persil',
                'unit_id' => $pcs->id,
                'current_stock' => 4.000,
                'minimum_stock' => 1.000,
                'selling_price' => 42.00,
                'is_active' => true,
            ],
            [
                'name' => 'Plateau de fruits de mer',
                'description' => 'Assortiment de fruits de mer frais',
                'unit_id' => $pcs->id,
                'current_stock' => 2.000,
                'minimum_stock' => 0.000,
                'selling_price' => 65.00,
                'is_active' => true,
            ],
            [
                'name' => 'Cassolette de Saint-Jacques',
                'description' => 'Saint-Jacques en cassolette crémeuse aux champignons',
                'unit_id' => $pcs->id,
                'current_stock' => 3.000,
                'minimum_stock' => 1.000,
                'selling_price' => 35.00,
                'is_active' => true,
            ],

            // Plats de calamars et crabe (Squid and crab dishes)
            [
                'name' => 'Calamars frits',
                'description' => 'Anneaux de calamars frits croustillants',
                'unit_id' => $pcs->id,
                'current_stock' => 10.000,
                'minimum_stock' => 3.000,
                'selling_price' => 16.50,
                'is_active' => true,
            ],
            [
                'name' => 'Calamars à la plancha',
                'description' => 'Calamars grillés à la plancha aux herbes',
                'unit_id' => $pcs->id,
                'current_stock' => 8.000,
                'minimum_stock' => 2.000,
                'selling_price' => 20.00,
                'is_active' => true,
            ],
            [
                'name' => 'Salade de crabe',
                'description' => 'Salade fraîche à la chair de crabe',
                'unit_id' => $pcs->id,
                'current_stock' => 5.000,
                'minimum_stock' => 1.000,
                'selling_price' => 28.50,
                'is_active' => true,
            ],
            [
                'name' => 'Crabe farci',
                'description' => 'Crabe farci à la chair de crabe et herbes',
                'unit_id' => $pcs->id,
                'current_stock' => 3.000,
                'minimum_stock' => 1.000,
                'selling_price' => 45.00,
                'is_active' => true,
            ],

            // Plats gratinés et au four (Gratin and baked dishes)
            [
                'name' => 'Gratin de fruits de mer',
                'description' => 'Gratin de fruits de mer au gruyère',
                'unit_id' => $pcs->id,
                'current_stock' => 6.000,
                'minimum_stock' => 2.000,
                'selling_price' => 29.00,
                'is_active' => true,
            ],
            [
                'name' => 'Quiche aux fruits de mer',
                'description' => 'Quiche lorraine aux fruits de mer et épinards',
                'unit_id' => $pcs->id,
                'current_stock' => 8.000,
                'minimum_stock' => 2.000,
                'selling_price' => 22.50,
                'is_active' => true,
            ],
            [
                'name' => 'Soufflé au crabe',
                'description' => 'Soufflé léger à la chair de crabe et fromage',
                'unit_id' => $pcs->id,
                'current_stock' => 4.000,
                'minimum_stock' => 1.000,
                'selling_price' => 31.00,
                'is_active' => true,
            ],

            // Entrées et amuse-bouches (Appetizers)
            [
                'name' => 'Bouchées à la reine aux crevettes',
                'description' => 'Bouchées feuilletées garnies de crevettes',
                'unit_id' => $dz->id,
                'current_stock' => 5.000,
                'minimum_stock' => 2.000,
                'selling_price' => 24.00,
                'is_active' => true,
            ],
            [
                'name' => 'Verrines de Saint-Jacques',
                'description' => 'Verrines apéritives aux Saint-Jacques',
                'unit_id' => $dz->id,
                'current_stock' => 3.000,
                'minimum_stock' => 1.000,
                'selling_price' => 36.00,
                'is_active' => true,
            ],
            [
                'name' => 'Tartines de crabe',
                'description' => 'Tartines grillées à la chair de crabe',
                'unit_id' => $pcs->id,
                'current_stock' => 12.000,
                'minimum_stock' => 4.000,
                'selling_price' => 8.50,
                'is_active' => true,
            ],
        ];

        foreach ($finishedProducts as $product) {
            FinishedProduct::create($product);
        }
    }
}
