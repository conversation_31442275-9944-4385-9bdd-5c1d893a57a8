<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UnitCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'base_unit_id',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the units that belong to this category.
     */
    public function units(): HasMany
    {
        return $this->hasMany(Unit::class, 'category_id');
    }

    /**
     * Get the active units that belong to this category.
     */
    public function activeUnits(): HasMany
    {
        return $this->units()->where('is_active', true);
    }

    /**
     * Get the base unit for this category.
     */
    public function baseUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'base_unit_id');
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the category's display name with unit count.
     */
    public function getDisplayNameAttribute(): string
    {
        $unitCount = $this->units()->count();
        return "{$this->name} ({$unitCount} units)";
    }

    /**
     * Check if this category has a base unit set.
     */
    public function hasBaseUnit(): bool
    {
        return !is_null($this->base_unit_id);
    }
}
