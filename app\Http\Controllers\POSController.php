<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\FinishedProduct;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\FinishedProductMovement;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class POSController extends Controller
{
    /**
     * Display the POS interface.
     */
    public function index()
    {
        return view('pages.pos.index');
    }

    /**
     * Get products for POS with search and pagination.
     */
    public function getProducts(Request $request): JsonResponse
    {
        // Debug authentication
        Log::info('POS getProducts called', [
            'user_id' => Auth::id(),
            'user_name' => Auth::user()?->name,
            'is_authenticated' => Auth::check(),
            'request_headers' => $request->headers->all()
        ]);

        $search = $request->input('search', '');
        $category = $request->input('category', '');
        $page = $request->input('page', 1);
        $perPage = 12;

        $query = FinishedProduct::with('unit')
            ->where('is_active', true)
            ->where('current_stock', '>', 0);

        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%");
            });
        }

        $total = $query->count();
        $products = $query->orderBy('name')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        $productsData = $products->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'description' => $product->description,
                'selling_price' => $product->selling_price,
                'current_stock' => $product->current_stock,
                'unit' => $product->unit->symbol,
                'image_url' => $product->image_url,
                'image_alt' => $product->image_alt_text,
            ];
        });

        return response()->json([
            'products' => $productsData,
            'total' => $total,
            'current_page' => $page,
            'per_page' => $perPage,
            'last_page' => ceil($total / $perPage),
        ]);
    }

    /**
     * Process a sale transaction.
     */
    public function processSale(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:finished_products,id',
            'items.*.quantity' => 'required|numeric|min:0.001',
            'items.*.unit_price' => 'required|numeric|min:0',
            'customer_name' => 'nullable|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'payment_method' => 'required|in:cash,card,transfer',
            'amount_paid' => 'required|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Calculate totals
            $subtotal = 0;
            $itemsData = [];

            foreach ($request->items as $item) {
                $product = FinishedProduct::findOrFail($item['product_id']);

                // Check stock availability
                if ($product->current_stock < $item['quantity']) {
                    return response()->json([
                        'message' => "Insufficient stock for {$product->name}. Available: {$product->current_stock}"
                    ], 422);
                }

                $lineTotal = $item['quantity'] * $item['unit_price'];
                $subtotal += $lineTotal;

                $itemsData[] = [
                    'product' => $product,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'line_total' => $lineTotal,
                ];
            }

            $discountAmount = $request->discount_amount ?? 0;
            $taxAmount = $request->tax_amount ?? 0;
            $totalAmount = $subtotal - $discountAmount + $taxAmount;

            // Validate payment amount
            if ($request->amount_paid < $totalAmount) {
                return response()->json([
                    'message' => 'Payment amount is insufficient'
                ], 422);
            }

            // Create sale record
            $sale = Sale::create([
                'sale_number' => $this->generateSaleNumber(),
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'amount_paid' => $request->amount_paid,
                'change_amount' => $request->amount_paid - $totalAmount,
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
                'cashier_id' => Auth::id(),
                'sale_date' => now(),
            ]);

            // Create sale items and update stock
            foreach ($itemsData as $itemData) {
                // Create sale item
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'finished_product_id' => $itemData['product']->id,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'line_total' => $itemData['line_total'],
                ]);

                // Update product stock
                $product = $itemData['product'];
                $newStock = $product->current_stock - $itemData['quantity'];
                $product->update(['current_stock' => $newStock]);

                // Create stock movement
                FinishedProductMovement::create([
                    'finished_product_id' => $product->id,
                    'movement_type' => 'outbound',
                    'quantity' => -$itemData['quantity'],
                    'unit_cost' => $itemData['unit_price'],
                    'total_value' => $itemData['line_total'],
                    'document_reference' => $sale->sale_number,
                    'reason' => 'POS Sale',
                    'notes' => "Sale to: " . ($request->customer_name ?? 'Walk-in Customer'),
                    'stock_before' => $product->current_stock + $itemData['quantity'],
                    'stock_after' => $newStock,
                    'user_id' => Auth::id(),
                ]);
            }

            DB::commit();

            // Load sale with items for receipt
            $sale->load(['items.finishedProduct.unit', 'cashier']);

            return response()->json([
                'message' => 'Sale processed successfully',
                'sale' => $sale,
                'receipt_data' => $this->formatReceiptData($sale)
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error processing sale: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate unique sale number.
     */
    private function generateSaleNumber(): string
    {
        $date = now()->format('Ymd');
        $lastSale = Sale::whereDate('created_at', now()->toDateString())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastSale ? (intval(substr($lastSale->sale_number, -4)) + 1) : 1;

        return 'POS-' . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Format receipt data for printing.
     */
    private function formatReceiptData(Sale $sale): array
    {
        return [
            'sale_number' => $sale->sale_number,
            'sale_date' => $sale->sale_date->format('Y-m-d H:i:s'),
            'customer_name' => $sale->customer_name ?? 'Walk-in Customer',
            'customer_phone' => $sale->customer_phone,
            'cashier' => $sale->cashier->name,
            'items' => $sale->items->map(function($item) {
                return [
                    'name' => $item->finishedProduct->name,
                    'quantity' => $item->quantity,
                    'unit' => $item->finishedProduct->unit->symbol,
                    'unit_price' => $item->unit_price,
                    'line_total' => $item->line_total,
                ];
            }),
            'subtotal' => $sale->subtotal,
            'discount_amount' => $sale->discount_amount,
            'tax_amount' => $sale->tax_amount,
            'total_amount' => $sale->total_amount,
            'amount_paid' => $sale->amount_paid,
            'change_amount' => $sale->change_amount,
            'payment_method' => $sale->payment_method,
            'notes' => $sale->notes,
        ];
    }

    /**
     * Get sales history.
     */
    public function getSalesHistory(Request $request): JsonResponse
    {
        $date = $request->input('date', now()->toDateString());

        $sales = Sale::with(['items.finishedProduct', 'cashier'])
            ->whereDate('sale_date', $date)
            ->orderBy('sale_date', 'desc')
            ->get();

        $summary = [
            'total_sales' => $sales->count(),
            'total_amount' => $sales->sum('total_amount'),
            'total_discount' => $sales->sum('discount_amount'),
            'total_tax' => $sales->sum('tax_amount'),
            'cash_sales' => $sales->where('payment_method', 'cash')->sum('total_amount'),
            'card_sales' => $sales->where('payment_method', 'card')->sum('total_amount'),
            'transfer_sales' => $sales->where('payment_method', 'transfer')->sum('total_amount'),
        ];

        return response()->json([
            'sales' => $sales,
            'summary' => $summary
        ]);
    }

    /**
     * Get sale details for reprint.
     */
    public function getSaleDetails($id): JsonResponse
    {
        $sale = Sale::with(['items.finishedProduct.unit', 'cashier'])->findOrFail($id);

        return response()->json([
            'sale' => $sale,
            'receipt_data' => $this->formatReceiptData($sale)
        ]);
    }

    /**
     * Hold/suspend a transaction.
     */
    public function holdTransaction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array|min:1',
            'customer_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Store in session or database (for simplicity, using session)
        $holdId = 'hold_' . time() . '_' . Auth::id();
        session()->put($holdId, [
            'items' => $request->items,
            'customer_name' => $request->customer_name,
            'notes' => $request->notes,
            'created_at' => now(),
        ]);

        return response()->json([
            'message' => 'Transaction held successfully',
            'hold_id' => $holdId
        ]);
    }

    /**
     * Get held transactions.
     */
    public function getHeldTransactions(): JsonResponse
    {
        $heldTransactions = collect(session()->all())
            ->filter(function($value, $key) {
                return str_starts_with($key, 'hold_') && is_array($value);
            })
            ->map(function($value, $key) {
                return array_merge($value, ['hold_id' => $key]);
            })
            ->values();

        return response()->json($heldTransactions);
    }

    /**
     * Retrieve held transaction.
     */
    public function retrieveHeldTransaction($holdId): JsonResponse
    {
        $transaction = session()->get($holdId);

        if (!$transaction) {
            return response()->json(['message' => 'Transaction not found'], 404);
        }

        // Remove from session
        session()->forget($holdId);

        return response()->json($transaction);
    }
}
