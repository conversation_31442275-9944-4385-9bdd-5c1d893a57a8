@extends('layouts/layoutMaster')

@section('title', __('Manufacturing Orders Kanban'))

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/jkanban/jkanban.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/quill/typography.scss',
  'resources/assets/vendor/libs/quill/katex.scss',
  'resources/assets/vendor/libs/quill/editor.scss'
])
@endsection

@section('page-style')
@vite('resources/assets/vendor/scss/pages/app-kanban.scss')
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/jkanban/jkanban.js',
  'resources/assets/vendor/libs/quill/katex.js',
  'resources/assets/vendor/libs/quill/quill.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/manufacturing-orders-management.js',
  'resources/js/manufacturing-orders-kanban.js'
])
@endsection

@section('content')
<div class="app-kanban">
  <!-- Header with view toggle -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h4 class="mb-0">{{ __('Manufacturing Orders') }}</h4>
        <div class="d-flex gap-2">
          <a href="{{ route('manufacturing-orders.index') }}" class="btn btn-outline-secondary">
            <i class="ri-table-line me-1"></i>{{ __('Table View') }}
          </a>
          <button class="btn btn-primary" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddOrder">
            <i class="ri-add-line me-1"></i>{{ __('Add Order') }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-body d-flex justify-content-between align-items-center">
          <div class="card-title mb-0">
            <h5 class="mb-1 me-2">{{ $totalOrders }}</h5>
            <p class="text-muted mb-0">{{ __('Total Orders') }}</p>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-shopping-cart-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-body d-flex justify-content-between align-items-center">
          <div class="card-title mb-0">
            <h5 class="mb-1 me-2">{{ $plannedOrders }}</h5>
            <p class="text-muted mb-0">{{ __('Planned') }}</p>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-calendar-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-body d-flex justify-content-between align-items-center">
          <div class="card-title mb-0">
            <h5 class="mb-1 me-2">{{ $inProgressOrders }}</h5>
            <p class="text-muted mb-0">{{ __('In Progress') }}</p>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-settings-3-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-body d-flex justify-content-between align-items-center">
          <div class="card-title mb-0">
            <h5 class="mb-1 me-2">{{ $completedOrders }}</h5>
            <p class="text-muted mb-0">{{ __('Completed') }}</p>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Kanban Wrapper -->
  <div class="kanban-wrapper"></div>

  <!-- Edit Order Sidebar -->
  <div class="offcanvas offcanvas-end kanban-update-item-sidebar">
    <div class="offcanvas-header border-bottom">
      <h5 class="offcanvas-title">{{ __('Order Details') }}</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body pt-2">
      <div class="nav-align-top">
        <ul class="nav nav-tabs mb-2">
          <li class="nav-item">
            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#tab-details">
              <i class="ri-information-line me-2"></i>
              <span class="align-middle">{{ __('Details') }}</span>
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tab-consumption">
              <i class="ri-list-check me-2"></i>
              <span class="align-middle">{{ __('Materials') }}</span>
            </button>
          </li>
        </ul>
      </div>
      <div class="tab-content px-0 pb-0">
        <!-- Order Details -->
        <div class="tab-pane fade show active" id="tab-details" role="tabpanel">
          <div id="order-details-content">
            <!-- Content will be loaded dynamically -->
          </div>
          <div id="order-actions" class="mt-4">
            <!-- Action buttons will be loaded dynamically -->
          </div>
        </div>
        <!-- Materials/Consumption -->
        <div class="tab-pane fade" id="tab-consumption" role="tabpanel">
          <div id="order-consumption-content">
            <!-- Content will be loaded dynamically -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

  <!-- Offcanvas to add new manufacturing order -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddOrder" aria-labelledby="offcanvasAddOrderLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddOrderLabel" class="offcanvas-title">{{ __('Add Manufacturing Order') }}</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 h-100">
      <form class="add-new-order pt-0" id="addNewOrderForm">
        <input type="hidden" name="id" id="order_id">

        <div class="form-floating form-floating-outline mb-5">
          <select id="add-order-product" class="form-select" name="finished_product_id">
            <option value="">{{ __('Select Product') }}</option>
          </select>
          <label for="add-order-product">{{ __('Finished Product') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="number" step="0.001" class="form-control" id="add-order-quantity" placeholder="0.000" name="planned_quantity" />
          <label for="add-order-quantity">{{ __('Planned Quantity') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-order-date" placeholder="YYYY-MM-DD" name="planned_date" />
          <label for="add-order-date">{{ __('Planned Date') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-order-responsible" placeholder="John Doe" name="responsible_person" />
          <label for="add-order-responsible">{{ __('Responsible Person') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="number" class="form-control" id="add-order-time" placeholder="120" name="estimated_time_minutes" />
          <label for="add-order-time">{{ __('Estimated Time (minutes)') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <textarea class="form-control" id="add-order-notes" placeholder="{{ __('Order notes...') }}" name="notes" rows="3"></textarea>
          <label for="add-order-notes">{{ __('Notes') }}</label>
        </div>

        <div class="mb-5" id="product-info" style="display: none;">
          <div class="alert alert-info">
            <h6 class="alert-heading mb-2">{{ __('Product Information') }}</h6>
            <div class="row">
              <div class="col-6">
                <small class="text-muted">{{ __('Production Cost') }}:</small>
                <div class="fw-medium" id="product-cost">{{ Helper::formatCurrency(0) }}</div>
              </div>
              <div class="col-6">
                <small class="text-muted">{{ __('Max Producible') }}:</small>
                <div class="fw-medium" id="product-max-qty">0 {{ __('units') }}</div>
              </div>
            </div>
          </div>
        </div>

        <button type="submit" class="btn btn-primary me-sm-3 me-1 data-submit">{{ __('Submit') }}</button>
        <button type="reset" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">{{ __('Cancel') }}</button>
      </form>
    </div>
  </div>

  <!-- Complete Order Modal -->
  <div class="modal fade" id="completeOrderModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ __('Complete Manufacturing Order') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="completeOrderForm">
            <input type="hidden" id="complete-order-id">

            <div class="form-floating form-floating-outline mb-4">
              <input type="number" step="0.001" class="form-control" id="complete-produced-quantity" name="produced_quantity" required>
              <label for="complete-produced-quantity">{{ __('Produced Quantity') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <input type="number" class="form-control" id="complete-actual-time" name="actual_time_minutes">
              <label for="complete-actual-time">{{ __('Actual Time (minutes)') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <textarea class="form-control" id="complete-notes" name="notes" rows="3"></textarea>
              <label for="complete-notes">{{ __('Completion Notes') }}</label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
          <button type="button" class="btn btn-success" id="confirm-complete-order">{{ __('Complete Order') }}</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Consumption Management Modal -->
  <div class="modal fade" id="consumptionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="consumptionModalLabel">{{ __('Material Consumption') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="consumptionForm">
            <input type="hidden" id="consumption-order-id">

            <div class="mb-4">
              <h6 class="fw-medium">{{ __('Order') }}: <span id="consumption-order-number" class="text-primary"></span></h6>
            </div>

            <div id="consumption-items">
              <!-- Consumption items will be dynamically added here -->
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
          <button type="button" class="btn btn-primary" id="save-consumption">{{ __('Save Consumption') }}</button>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
