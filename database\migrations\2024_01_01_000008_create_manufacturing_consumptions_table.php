<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_consumptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('manufacturing_order_id');
            $table->unsignedBigInteger('raw_material_id');
            $table->decimal('planned_quantity', 10, 3); // From recipe
            $table->decimal('actual_quantity', 10, 3); // Actually consumed
            $table->decimal('unit_cost', 10, 2); // Cost per unit at time of consumption
            $table->decimal('total_cost', 12, 2); // actual_quantity * unit_cost
            $table->text('notes')->nullable();
            $table->timestamp('consumed_at')->useCurrent();
            $table->unsignedBigInteger('recorded_by');
            $table->timestamps();

            // Foreign key constraints with custom short names
            $table->foreign('manufacturing_order_id', 'mfg_consumptions_order_fk')
                  ->references('id')->on('manufacturing_orders')->onDelete('cascade');
            $table->foreign('raw_material_id', 'mfg_consumptions_material_fk')
                  ->references('id')->on('raw_materials')->onDelete('restrict');
            $table->foreign('recorded_by', 'mfg_consumptions_user_fk')
                  ->references('id')->on('users')->onDelete('restrict');

            // Composite unique key to prevent duplicate entries for same order + material
            $table->unique(['manufacturing_order_id', 'raw_material_id'], 'mfg_consumptions_order_material_unique');

            // Indexes for performance with custom short names
            $table->index('manufacturing_order_id', 'mfg_consumptions_order_idx');
            $table->index('raw_material_id', 'mfg_consumptions_material_idx');
            $table->index('consumed_at', 'mfg_consumptions_date_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_consumptions');
    }
};
