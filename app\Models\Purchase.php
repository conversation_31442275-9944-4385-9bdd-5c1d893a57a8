<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Purchase extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'purchase_number',
        'supplier_name',
        'supplier_contact',
        'supplier_email',
        'supplier_address',
        'purchase_date',
        'expected_delivery_date',
        'actual_delivery_date',
        'subtotal',
        'delivery_fee',
        'handling_fee',
        'other_fees',
        'total_amount',
        'status',
        'notes',
        'created_by',
        'received_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'purchase_date' => 'date',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'handling_fee' => 'decimal:2',
        'other_fees' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * The purchase statuses available.
     */
    const STATUSES = [
        'pending' => 'Pending',
        'ordered' => 'Ordered',
        'shipped' => 'Shipped',
        'received' => 'Received',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
    ];

    /**
     * Get the user who created this purchase.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who received this purchase.
     */
    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Get the purchase items for this purchase.
     */
    public function items(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * Get the status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'warning',
            'ordered' => 'info',
            'shipped' => 'primary',
            'received' => 'success',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get the total items count.
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Get the total fees.
     */
    public function getTotalFeesAttribute(): float
    {
        return $this->delivery_fee + $this->handling_fee + $this->other_fees;
    }

    /**
     * Check if the purchase is overdue.
     */
    public function isOverdue(): bool
    {
        if (!$this->expected_delivery_date || in_array($this->status, ['received', 'completed', 'cancelled'])) {
            return false;
        }
        return $this->expected_delivery_date->isPast();
    }

    /**
     * Check if the purchase can be received.
     */
    public function canReceive(): bool
    {
        return in_array($this->status, ['ordered', 'shipped']);
    }

    /**
     * Check if the purchase can be completed.
     */
    public function canComplete(): bool
    {
        return $this->status === 'received';
    }

    /**
     * Check if the purchase can be cancelled.
     */
    public function canCancel(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get formatted purchase date.
     */
    public function getFormattedPurchaseDateAttribute(): string
    {
        return $this->purchase_date->format('M d, Y');
    }

    /**
     * Get formatted expected delivery date.
     */
    public function getFormattedExpectedDeliveryAttribute(): string
    {
        return $this->expected_delivery_date ? $this->expected_delivery_date->format('M d, Y') : 'N/A';
    }

    /**
     * Get formatted actual delivery date.
     */
    public function getFormattedActualDeliveryAttribute(): string
    {
        return $this->actual_delivery_date ? $this->actual_delivery_date->format('M d, Y') : 'N/A';
    }

    /**
     * Scope a query to only include pending purchases.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include ordered purchases.
     */
    public function scopeOrdered($query)
    {
        return $query->where('status', 'ordered');
    }

    /**
     * Scope a query to only include shipped purchases.
     */
    public function scopeShipped($query)
    {
        return $query->where('status', 'shipped');
    }

    /**
     * Scope a query to only include received purchases.
     */
    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    /**
     * Scope a query to only include completed purchases.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to filter by supplier.
     */
    public function scopeBySupplier($query, $supplier)
    {
        return $query->where('supplier_name', 'LIKE', "%{$supplier}%");
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('purchase_date', [$startDate, $endDate]);
    }

    /**
     * Boot the model to auto-generate purchase numbers.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchase) {
            if (!$purchase->purchase_number) {
                $date = now()->format('Ymd');
                $lastPurchase = static::whereDate('created_at', now()->toDateString())
                    ->orderBy('id', 'desc')
                    ->first();
                
                $sequence = $lastPurchase ? (intval(substr($lastPurchase->purchase_number, -4)) + 1) : 1;
                
                $purchase->purchase_number = 'PO-' . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
            }
        });
    }
}
