@extends('layouts/layoutMaster')

@section('title', 'Raw Materials Management - Manufacturing App')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('page-style')
<link rel="stylesheet" href="{{ asset('assets/css/image-upload.css') }}">
@endsection

<!-- Vend<PERSON> Sc<PERSON> -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite([
  'resources/js/raw-materials-management.js'
])
@endsection

@section('content')

<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Total Materials') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{$totalRawMaterials}}</h4>
              <p class="text-success mb-1">(100%)</p>
            </div>
            <small class="mb-0">{{ __('All raw materials') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-archive-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Active Materials') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$activeRawMaterials}}</h4>
              <p class="text-success mb-1">({{$totalRawMaterials > 0 ? round(($activeRawMaterials/$totalRawMaterials)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">{{ __('Currently in use') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Low Stock Alert') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$lowStockItems}}</h4>
              <p class="text-danger mb-1">({{$totalRawMaterials > 0 ? round(($lowStockItems/$totalRawMaterials)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">{{ __('Need restocking') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-alert-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Total Value') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{ Helper::formatCurrency($totalValue) }}</h4>
              <p class="text-info mb-1">({{ __('Inventory') }})</p>
            </div>
            <small class="mb-0">{{ __('Current stock value') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-money-dollar-circle-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Raw Materials List Table -->
<div class="card">
  <div class="card-header pb-0">
    <h5 class="card-title mb-0">{{ __('Raw Materials Management') }}</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-raw-materials table">
      <thead>
        <tr>
          <th></th>
          <th>Id</th>
          <th>{{ __('Name') }}</th>
          <th>{{ __('Current Stock') }}</th>
          <th>{{ __('Min Stock') }}</th>
          <th>{{ __('Unit Price') }}</th>
          <th>{{ __('Supplier') }}</th>
          <th>{{ __('Status') }}</th>
          <th>{{ __('Actions') }}</th>
        </tr>
      </thead>
    </table>
  </div>

  <!-- Offcanvas to add new raw material -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddRawMaterial" aria-labelledby="offcanvasAddRawMaterialLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddRawMaterialLabel" class="offcanvas-title">{{ __('Add Raw Material') }}</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 h-100">
      <form class="add-new-raw-material pt-0" id="addNewRawMaterialForm" enctype="multipart/form-data">
        <input type="hidden" name="id" id="raw_material_id">

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-material-name" placeholder="{{ __('Steel Rod') }}" name="name" aria-label="{{ __('Material Name') }}" />
          <label for="add-material-name">{{ __('Material Name') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <textarea class="form-control" id="add-material-description" placeholder="{{ __('Material description...') }}" name="description" rows="3"></textarea>
          <label for="add-material-description">{{ __('Description') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <select id="add-material-unit" class="form-select" name="unit_id">
            <option value="">{{ __('Select Unit') }}</option>
          </select>
          <label for="add-material-unit">{{ __('Unit') }}</label>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline mb-5">
              <input type="number" step="0.001" class="form-control" id="add-material-current-stock" placeholder="0.000" name="current_stock" />
              <label for="add-material-current-stock">{{ __('Current Stock') }}</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline mb-5">
              <input type="number" step="0.001" class="form-control" id="add-material-minimum-stock" placeholder="0.000" name="minimum_stock" />
              <label for="add-material-minimum-stock">{{ __('Minimum Stock') }}</label>
            </div>
          </div>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="number" step="0.01" class="form-control" id="add-material-unit-price" placeholder="0.00" name="unit_price" />
          <label for="add-material-unit-price">{{ __('Unit Price') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-material-supplier" placeholder="{{ __('Supplier name') }}" name="supplier" />
          <label for="add-material-supplier">{{ __('Supplier') }}</label>
        </div>

        <!-- Image Upload Section -->
        <div class="mb-5">
          <label class="form-label">{{ __('Material Image') }}</label>
          <div class="row">
            <div class="col-md-8">
              <input type="file" class="form-control" id="add-material-image" name="image" accept="image/*">
              <div class="form-text">{{ __('Supported formats: JPEG, PNG, GIF, WebP. Max size: 5MB') }}</div>
            </div>
            <div class="col-md-4">
              <div class="image-preview-container" style="display: none;">
                <img id="add-material-image-preview" src="" alt="{{ __('Image Preview') }}" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                <button type="button" class="btn btn-sm btn-outline-danger mt-1 w-100" id="remove-material-image">
                  <i class="mdi mdi-delete-outline"></i> {{ __('Remove') }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-material-image-alt" placeholder="{{ __('Image description') }}" name="image_alt" />
          <label for="add-material-image-alt">{{ __('Image Description') }} ({{ __('Optional') }})</label>
        </div>

        <div class="mb-5">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="add-material-active" name="is_active" checked>
            <label class="form-check-label" for="add-material-active">{{ __('Active') }}</label>
          </div>
        </div>

        <button type="submit" class="btn btn-primary me-sm-3 me-1 data-submit">{{ __('Submit') }}</button>
        <button type="reset" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">{{ __('Cancel') }}</button>
      </form>
    </div>
  </div>

  <!-- View Movements Modal -->
  <div class="modal fade" id="movementsModal" tabindex="-1" aria-labelledby="movementsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="movementsModalLabel">{{ __('Stock Movements') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <h6 class="mb-1">{{ __('Material') }}: <span id="movements-material-name" class="fw-bold"></span></h6>
            </div>
            <div class="col-md-6 text-end">
              <h6 class="mb-1">{{ __('Current Stock') }}: <span id="movements-current-stock" class="fw-bold text-primary"></span></h6>
            </div>
          </div>

          <div class="table-responsive">
            <table class="table table-bordered">
              <thead class="table-light">
                <tr>
                  <th>{{ __('Date') }}</th>
                  <th>{{ __('Type') }}</th>
                  <th>{{ __('Quantity') }}</th>
                  <th>{{ __('Unit Price') }}</th>
                  <th>{{ __('Stock Before') }}</th>
                  <th>{{ __('Stock After') }}</th>
                  <th>{{ __('Details') }}</th>
                </tr>
              </thead>
              <tbody id="movements-table-body">
                <!-- Movements will be loaded here -->
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Stock Modal -->
  <div class="modal fade" id="addStockModal" tabindex="-1" aria-labelledby="addStockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addStockModalLabel">{{ __('Add Stock') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form id="addStockForm">
          <div class="modal-body">
            <input type="hidden" id="add-stock-material-id" name="material_id">

            <div class="mb-3">
              <h6 class="mb-1">{{ __('Material') }}: <span id="add-stock-material-name" class="fw-bold"></span></h6>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <input type="number" step="0.001" class="form-control" id="add-stock-quantity" name="quantity" placeholder="0.000" required>
              <label for="add-stock-quantity">{{ __('Quantity to Add') }} <span id="add-stock-unit-label"></span></label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <input type="number" step="0.01" class="form-control" id="add-stock-unit-price" name="unit_price" placeholder="0.00">
              <label for="add-stock-unit-price">{{ __('Unit Price') }} ({{ __('Optional') }})</label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <input type="text" class="form-control" id="add-stock-reason" name="reason" placeholder="{{ __('Reason for stock addition') }}" required>
              <label for="add-stock-reason">{{ __('Reason') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <input type="text" class="form-control" id="add-stock-document" name="document_reference" placeholder="{{ __('Document reference') }}">
              <label for="add-stock-document">{{ __('Document Reference') }} ({{ __('Optional') }})</label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <textarea class="form-control" id="add-stock-notes" name="notes" placeholder="{{ __('Additional notes') }}" rows="3"></textarea>
              <label for="add-stock-notes">{{ __('Notes') }} ({{ __('Optional') }})</label>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
            <button type="submit" class="btn btn-primary">{{ __('Add Stock') }}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection
