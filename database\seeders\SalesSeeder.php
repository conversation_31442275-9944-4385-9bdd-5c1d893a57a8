<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\FinishedProduct;
use App\Models\User;

class SalesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for cashier
        $user = User::first();

        // Get some finished products (French seafood dishes)
        $crevettesAil = FinishedProduct::where('name', 'Crevettes à l\'ail')->first();
        $saumonGrille = FinishedProduct::where('name', 'Filet de saumon grillé')->first();
        $crevettesGratinees = FinishedProduct::where('name', 'Crevettes gratinées')->first();
        $poissonMeuniere = FinishedProduct::where('name', 'Poisson blanc meunière')->first();
        $calamarsfrits = FinishedProduct::where('name', 'Calamars frits')->first();

        // Sale 1 - Today
        $sale1 = Sale::create([
            'customer_name' => '<PERSON>',
            'customer_phone' => '+33-1-45-67-89-12',
            'subtotal' => 66.50,
            'discount_amount' => 3.00,
            'tax_amount' => 5.32,
            'total_amount' => 68.82,
            'amount_paid' => 70.00,
            'change_amount' => 1.18,
            'payment_method' => 'cash',
            'notes' => 'Commande pour fête d\'anniversaire',
            'cashier_id' => $user->id,
            'sale_date' => now(),
        ]);

        // Sale 1 Items
        SaleItem::create([
            'sale_id' => $sale1->id,
            'finished_product_id' => $saumonGrille->id,
            'quantity' => 1.000,
            'unit_price' => 28.00,
        ]);

        SaleItem::create([
            'sale_id' => $sale1->id,
            'finished_product_id' => $crevettesAil->id,
            'quantity' => 1.000,
            'unit_price' => 22.00,
        ]);

        SaleItem::create([
            'sale_id' => $sale1->id,
            'finished_product_id' => $calamarsfrits->id,
            'quantity' => 1.000,
            'unit_price' => 16.50,
        ]);

        // Sale 2 - Today
        $sale2 = Sale::create([
            'customer_name' => null, // Walk-in customer
            'customer_phone' => null,
            'subtotal' => 24.00,
            'discount_amount' => 0.00,
            'tax_amount' => 1.92,
            'total_amount' => 25.92,
            'amount_paid' => 26.00,
            'change_amount' => 0.08,
            'payment_method' => 'cash',
            'notes' => null,
            'cashier_id' => $user->id,
            'sale_date' => now()->subHours(2),
        ]);

        // Sale 2 Items
        SaleItem::create([
            'sale_id' => $sale2->id,
            'finished_product_id' => $poissonMeuniere->id,
            'quantity' => 1.000,
            'unit_price' => 24.00,
        ]);

        // Sale 3 - Today (Card payment)
        $sale3 = Sale::create([
            'customer_name' => 'Pierre Martin',
            'customer_phone' => '+33-1-56-78-90-12',
            'subtotal' => 52.00,
            'discount_amount' => 0.00,
            'tax_amount' => 4.16,
            'total_amount' => 56.16,
            'amount_paid' => 56.16,
            'change_amount' => 0.00,
            'payment_method' => 'card',
            'notes' => 'Commande pour réunion de bureau',
            'cashier_id' => $user->id,
            'sale_date' => now()->subHours(4),
        ]);

        // Sale 3 Items
        SaleItem::create([
            'sale_id' => $sale3->id,
            'finished_product_id' => $crevettesGratinees->id,
            'quantity' => 2.000,
            'unit_price' => 26.00,
        ]);


    }
}
