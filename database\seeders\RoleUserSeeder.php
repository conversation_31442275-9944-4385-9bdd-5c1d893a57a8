<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;

class RoleUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the test user and assign Super Admin role
        $testUser = User::where('email', '<EMAIL>')->first();
        if ($testUser) {
            $testUser->assignRole('Super Admin');
        }

        // Create additional users with different roles for testing
        $this->createUsersWithRoles();
    }

    /**
     * Create users with different roles for testing
     */
    private function createUsersWithRoles()
    {
        // Admin User
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('Admin');

        // Production Manager
        $productionManager = User::create([
            'name' => 'John Production',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $productionManager->assignRole('Production Manager');

        // Inventory Manager
        $inventoryManager = User::create([
            'name' => 'Sarah Inventory',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $inventoryManager->assignRole('Inventory Manager');

        // Sales Manager
        $salesManager = User::create([
            'name' => 'Mike Sales',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $salesManager->assignRole('Sales Manager');

        // Production Operator
        $productionOperator = User::create([
            'name' => 'Emma Operator',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $productionOperator->assignRole('Production Operator');

        // Cashier
        $cashier = User::create([
            'name' => 'Lisa Cashier',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $cashier->assignRole('Cashier');

        // Viewer
        $viewer = User::create([
            'name' => 'David Viewer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $viewer->assignRole('Viewer');

        // Additional Production Operators
        $operator2 = User::create([
            'name' => 'Tom Wilson',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $operator2->assignRole('Production Operator');

        $operator3 = User::create([
            'name' => 'Anna Brown',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $operator3->assignRole('Production Operator');

        // Additional Cashiers
        $cashier2 = User::create([
            'name' => 'Jennifer Garcia',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $cashier2->assignRole('Cashier');
    }
}
