<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ManufacturingOrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'manufacturing_order_id',
        'finished_product_id',
        'planned_quantity',
        'produced_quantity',
        'estimated_cost',
        'actual_cost',
        'estimated_time_minutes',
        'actual_time_minutes',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'planned_quantity' => 'decimal:3',
        'produced_quantity' => 'decimal:3',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'estimated_time_minutes' => 'integer',
        'actual_time_minutes' => 'integer',
    ];

    /**
     * Get the manufacturing order that this item belongs to.
     */
    public function manufacturingOrder(): BelongsTo
    {
        return $this->belongsTo(ManufacturingOrder::class);
    }

    /**
     * Get the finished product for this item.
     */
    public function finishedProduct(): BelongsTo
    {
        return $this->belongsTo(FinishedProduct::class);
    }

    /**
     * Get the completion percentage for this item.
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->planned_quantity <= 0) {
            return 0;
        }
        return min(100, ($this->produced_quantity / $this->planned_quantity) * 100);
    }

    /**
     * Get the remaining quantity to produce.
     */
    public function getRemainingQuantityAttribute(): float
    {
        return max(0, $this->planned_quantity - $this->produced_quantity);
    }

    /**
     * Check if this item can be produced with current stock.
     */
    public function canProduce($quantity = null): bool
    {
        $quantityToProduce = $quantity ?? $this->planned_quantity;
        return $this->finishedProduct->canProduce($quantityToProduce);
    }

    /**
     * Get the maximum quantity that can be produced with current stock.
     */
    public function getMaxProducibleQuantityAttribute(): float
    {
        return $this->finishedProduct->max_producible_quantity;
    }
}
