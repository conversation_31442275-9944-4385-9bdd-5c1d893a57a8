<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UnitCategory;

class UnitCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Weight',
                'description' => 'Units for measuring weight and mass',
                'is_active' => true,
            ],
            [
                'name' => 'Volume',
                'description' => 'Units for measuring volume and capacity',
                'is_active' => true,
            ],
            [
                'name' => 'Length',
                'description' => 'Units for measuring length and distance',
                'is_active' => true,
            ],
            [
                'name' => 'Area',
                'description' => 'Units for measuring area and surface',
                'is_active' => true,
            ],
            [
                'name' => 'Count',
                'description' => 'Units for counting items',
                'is_active' => true,
            ],
            [
                'name' => 'Time',
                'description' => 'Units for measuring time duration',
                'is_active' => true,
            ],
            [
                'name' => 'Temperature',
                'description' => 'Units for measuring temperature',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            UnitCategory::create($category);
        }
    }
}
