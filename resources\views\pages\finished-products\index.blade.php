@extends('layouts/layoutMaster')

@section('title', 'Finished Products Management - Manufacturing App')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss'
])
@endsection

@section('page-style')
<link rel="stylesheet" href="{{ asset('assets/css/image-upload.css') }}">
@endsection

<!-- <PERSON><PERSON><PERSON>ts -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite([
  'resources/js/finished-products-management.js',
  'resources/js/recipe-management.js'
])
@endsection

@section('content')

<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Total Products') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{$totalProducts}}</h4>
              <p class="text-success mb-1">(100%)</p>
            </div>
            <small class="mb-0">{{ __('All finished products') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-product-hunt-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Active Products') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$activeProducts}}</h4>
              <p class="text-success mb-1">({{$totalProducts > 0 ? round(($activeProducts/$totalProducts)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">{{ __('Ready for production') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Low Stock Alert') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$lowStockProducts}}</h4>
              <p class="text-danger mb-1">({{$totalProducts > 0 ? round(($lowStockProducts/$totalProducts)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">{{ __('Need production') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-alert-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Total Value') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{ Helper::formatCurrency($totalValue) }}</h4>
              <p class="text-info mb-1">({{ __('Inventory') }})</p>
            </div>
            <small class="mb-0">{{ __('Current stock value') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-money-dollar-circle-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Finished Products List Table -->
<div class="card">
  <div class="card-header pb-0">
    <h5 class="card-title mb-0">{{ __('Finished Products Management') }}</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-finished-products table">
      <thead>
        <tr>
          <th></th>
          <th>Id</th>
          <th>{{ __('Product') }}</th>
          <th>{{ __('Stock') }}</th>
          <th>{{ __('Price') }}</th>
          <th>{{ __('Production') }}</th>
          <th>{{ __('Recipe') }}</th>
          <th>{{ __('Status') }}</th>
          <th>{{ __('Actions') }}</th>
        </tr>
      </thead>
    </table>
  </div>

  <!-- Offcanvas to add new finished product -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddFinishedProduct" aria-labelledby="offcanvasAddFinishedProductLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddFinishedProductLabel" class="offcanvas-title">{{ __('Add Finished Product') }}</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 h-100">
      <form class="add-new-finished-product pt-0" id="addNewFinishedProductForm" enctype="multipart/form-data">
        <input type="hidden" name="id" id="finished_product_id">

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-product-name" placeholder="{{ __('Chocolate Cake') }}" name="name" aria-label="{{ __('Product Name') }}" />
          <label for="add-product-name">{{ __('Product Name') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <textarea class="form-control" id="add-product-description" placeholder="{{ __('Product description...') }}" name="description" rows="3"></textarea>
          <label for="add-product-description">{{ __('Description') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <select id="add-product-unit" class="form-select" name="unit_id">
            <option value="">{{ __('Select Unit') }}</option>
          </select>
          <label for="add-product-unit">{{ __('Unit') }}</label>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline mb-5">
              <input type="number" step="0.001" class="form-control" id="add-product-current-stock" placeholder="0.000" name="current_stock" />
              <label for="add-product-current-stock">{{ __('Current Stock') }}</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline mb-5">
              <input type="number" step="0.001" class="form-control" id="add-product-minimum-stock" placeholder="0.000" name="minimum_stock" />
              <label for="add-product-minimum-stock">{{ __('Minimum Stock') }}</label>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline mb-5">
              <input type="number" step="0.01" class="form-control" id="add-product-selling-price" placeholder="0.00" name="selling_price" />
              <label for="add-product-selling-price">{{ __('Selling Price') }}</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating form-floating-outline mb-5">
              <input type="number" class="form-control" id="add-product-production-time" placeholder="60" name="production_time_minutes" />
              <label for="add-product-production-time">{{ __('Production Time (min)') }}</label>
            </div>
          </div>
        </div>

        <!-- Image Upload Section -->
        <div class="mb-5">
          <label class="form-label">{{ __('Product Image') }}</label>
          <div class="row">
            <div class="col-md-8">
              <input type="file" class="form-control" id="add-product-image" name="image" accept="image/*">
              <div class="form-text">{{ __('Supported formats: JPEG, PNG, GIF, WebP. Max size: 5MB') }}</div>
            </div>
            <div class="col-md-4">
              <div class="image-preview-container" style="display: none;">
                <img id="add-product-image-preview" src="" alt="{{ __('Image Preview') }}" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                <button type="button" class="btn btn-sm btn-outline-danger mt-1 w-100" id="remove-product-image">
                  <i class="mdi mdi-delete-outline"></i> {{ __('Remove') }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-product-image-alt" placeholder="{{ __('Image description') }}" name="image_alt" />
          <label for="add-product-image-alt">{{ __('Image Description') }} ({{ __('Optional') }})</label>
        </div>

        <div class="mb-5">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="add-product-active" name="is_active" checked>
            <label class="form-check-label" for="add-product-active">{{ __('Active') }}</label>
          </div>
        </div>

        <button type="submit" class="btn btn-primary me-sm-3 me-1 data-submit">{{ __('Submit') }}</button>
        <button type="reset" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">{{ __('Cancel') }}</button>
      </form>
    </div>
  </div>

  <!-- Recipe Management Modal -->
  <div class="modal fade" id="recipeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="recipeModalLabel">{{ __('Manage Recipe') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="recipeForm">
            <input type="hidden" id="recipe-product-id">

            <div class="mb-4">
              <h6 class="fw-medium">{{ __('Product') }}: <span id="recipe-product-name" class="text-primary"></span></h6>
            </div>

            <div id="recipe-items">
              <!-- Recipe items will be dynamically added here -->
            </div>

            <button type="button" class="btn btn-outline-primary btn-sm" id="add-recipe-item">
              <i class="ri-add-line me-1"></i>{{ __('Add Ingredient') }}
            </button>

            <div class="mt-4 p-3 bg-light rounded">
              <div class="row">
                <div class="col-md-6">
                  <small class="text-muted">{{ __('Total Cost per Unit:') }}</small>
                  <div class="fw-medium" id="total-cost">{{ Helper::formatCurrency(0) }}</div>
                </div>
                <div class="col-md-6">
                  <small class="text-muted">{{ __('Profit Margin:') }}</small>
                  <div class="fw-medium" id="profit-margin">{{ Helper::formatCurrency(0) }}</div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
          <button type="button" class="btn btn-primary" id="save-recipe">{{ __('Save Recipe') }}</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Movements History Modal -->
  <div class="modal fade" id="movementsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="movementsModalLabel">{{ __('Stock Movements') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-4">
            <h6 class="fw-medium">{{ __('Product') }}: <span id="movements-product-name" class="text-primary"></span></h6>
            <small class="text-muted">{{ __('Current Stock') }}: <span id="movements-current-stock" class="fw-medium"></span></small>
          </div>

          <div class="table-responsive">
            <table class="table table-sm" id="movementsTable">
              <thead>
                <tr>
                  <th>{{ __('Date') }}</th>
                  <th>{{ __('Type') }}</th>
                  <th>{{ __('Quantity') }}</th>
                  <th>{{ __('Stock Before') }}</th>
                  <th>{{ __('Stock After') }}</th>
                  <th>{{ __('Value') }}</th>
                  <th>{{ __('Reference') }}</th>
                  <th>{{ __('Reason') }}</th>
                  <th>{{ __('User') }}</th>
                </tr>
              </thead>
              <tbody id="movements-table-body">
                <!-- Movements will be loaded here -->
              </tbody>
            </table>
          </div>

          <div id="no-movements" class="text-center py-4" style="display: none;">
            <div class="avatar avatar-lg mx-auto mb-3">
              <div class="avatar-initial bg-label-secondary rounded-3">
                <i class="ri-file-list-line ri-36px"></i>
              </div>
            </div>
            <h6 class="mb-1">{{ __('No movements found') }}</h6>
            <small class="text-muted">{{ __('This product has no stock movement history yet.') }}</small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Order Creation Modal -->
  <div class="modal fade" id="quickOrderModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="quickOrderModalLabel">{{ __('Create Manufacturing Order') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="quickOrderForm">
            <input type="hidden" id="quick-order-product-id">

            <div class="form-floating form-floating-outline mb-5">
              <input type="text" class="form-control" id="quick-order-product-name" readonly>
              <label for="quick-order-product-name">{{ __('Finished Product') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-5">
              <input type="number" step="0.001" min="0.001" class="form-control" id="quick-order-quantity" placeholder="0.000" name="planned_quantity" required>
              <label for="quick-order-quantity">{{ __('Planned Quantity') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-5">
              <input type="text" class="form-control" id="quick-order-date" placeholder="YYYY-MM-DD" name="planned_date" required>
              <label for="quick-order-date">{{ __('Planned Date') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-5">
              <input type="text" class="form-control" id="quick-order-responsible" placeholder="{{ __('John Doe') }}" name="responsible_person">
              <label for="quick-order-responsible">{{ __('Responsible Person') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-5">
              <input type="number" class="form-control" id="quick-order-time" placeholder="120" name="estimated_time_minutes">
              <label for="quick-order-time">{{ __('Estimated Time (minutes)') }}</label>
            </div>

            <div class="form-floating form-floating-outline mb-5">
              <textarea class="form-control" id="quick-order-notes" placeholder="{{ __('Order notes...') }}" name="notes" rows="3"></textarea>
              <label for="quick-order-notes">{{ __('Notes') }}</label>
            </div>

            <div id="product-info" class="alert alert-info" style="display: none;">
              <div class="row">
                <div class="col-md-6">
                  <small><strong>{{ __('Current Stock') }}:</strong> <span id="product-current-stock">-</span></small>
                </div>
                <div class="col-md-6">
                  <small><strong>{{ __('Unit') }}:</strong> <span id="product-unit">-</span></small>
                </div>
              </div>
              <div class="row mt-2">
                <div class="col-md-6">
                  <small><strong>{{ __('Recipe Cost') }}:</strong> <span id="product-recipe-cost">-</span></small>
                </div>
                <div class="col-md-6">
                  <small><strong>{{ __('Estimated Total') }}:</strong> <span id="estimated-total-cost">-</span></small>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
          <button type="button" class="btn btn-primary" id="create-quick-order">{{ __('Create Order') }}</button>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
