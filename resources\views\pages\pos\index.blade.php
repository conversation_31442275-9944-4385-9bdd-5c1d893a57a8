@extends('layouts/posLayout')

@section('title', 'Point of Sale')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite(['resources/js/pos-system.js'])
@endsection

@section('content')
<div class="row h-100">
  <!-- Products Section -->
  <div class="col-lg-8">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h5 class="mb-0">Products</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-primary btn-sm" id="refresh-products">
          <i class="ri-refresh-line me-1"></i>
          Refresh
        </button>
        <button class="btn btn-outline-secondary btn-sm" id="view-held">
          <i class="ri-pause-circle-line me-1"></i>
          Held (<span id="held-count">0</span>)
        </button>
      </div>
    </div>
    
    <!-- Product Grid -->
    <div class="product-grid" id="product-grid">
      <!-- Products will be loaded here -->
    </div>
    
    <!-- Loading Spinner -->
    <div class="text-center py-5" id="products-loading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2 text-muted">Loading products...</p>
    </div>
    
    <!-- No Products Message -->
    <div class="text-center py-5 d-none" id="no-products">
      <i class="ri-inbox-line display-4 text-muted"></i>
      <p class="mt-2 text-muted">No products found</p>
    </div>
    
    <!-- Pagination -->
    <div class="d-flex justify-content-center mt-3">
      <nav aria-label="Products pagination">
        <ul class="pagination" id="products-pagination">
          <!-- Pagination will be generated here -->
        </ul>
      </nav>
    </div>
  </div>
  
  <!-- Cart Section -->
  <div class="col-lg-4">
    <div class="cart-sidebar">
      <!-- Cart Header -->
      <div class="p-3 border-bottom">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="ri-shopping-cart-line me-2"></i>
            Cart
          </h5>
          <button class="btn btn-outline-danger btn-sm" id="clear-cart">
            <i class="ri-delete-bin-line me-1"></i>
            Clear
          </button>
        </div>
      </div>
      
      <!-- Cart Items -->
      <div class="cart-items" id="cart-items">
        <div class="text-center py-5 text-muted" id="empty-cart">
          <i class="ri-shopping-cart-line display-4"></i>
          <p class="mt-2">Cart is empty</p>
          <small>Add products to start a sale</small>
        </div>
      </div>
      
      <!-- Cart Footer -->
      <div class="cart-footer">
        <!-- Customer Info -->
        <div class="mb-3">
          <div class="row g-2">
            <div class="col-7">
              <input type="text" class="form-control form-control-sm" id="customer-name" placeholder="Customer name (optional)">
            </div>
            <div class="col-5">
              <input type="text" class="form-control form-control-sm" id="customer-phone" placeholder="Phone">
            </div>
          </div>
        </div>
        
        <!-- Totals -->
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <span>Subtotal:</span>
            <span class="fw-bold" id="cart-subtotal">$0.00</span>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Discount:</span>
            <div class="d-flex align-items-center">
              <input type="number" class="form-control form-control-sm me-2" id="discount-amount" placeholder="0.00" style="width: 80px;" step="0.01" min="0">
              <span class="fw-bold" id="cart-discount">$0.00</span>
            </div>
          </div>
          <div class="d-flex justify-content-between mb-1">
            <span>Tax:</span>
            <div class="d-flex align-items-center">
              <input type="number" class="form-control form-control-sm me-2" id="tax-amount" placeholder="0.00" style="width: 80px;" step="0.01" min="0">
              <span class="fw-bold" id="cart-tax">$0.00</span>
            </div>
          </div>
          <hr>
          <div class="d-flex justify-content-between">
            <span class="h6">Total:</span>
            <span class="h5 text-primary fw-bold" id="cart-total">$0.00</span>
          </div>
        </div>
        
        <!-- Payment Method -->
        <div class="mb-3">
          <label class="form-label small">Payment Method</label>
          <select class="form-select form-select-sm" id="payment-method">
            <option value="cash">Cash</option>
            <option value="card">Card</option>
            <option value="transfer">Bank Transfer</option>
          </select>
        </div>
        
        <!-- Amount Paid -->
        <div class="mb-3">
          <label class="form-label small">Amount Paid</label>
          <input type="number" class="form-control" id="amount-paid" placeholder="0.00" step="0.01" min="0">
          <div class="d-flex justify-content-between mt-1">
            <small class="text-muted">Change:</small>
            <small class="fw-bold" id="change-amount">$0.00</small>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="d-grid gap-2">
          <button class="btn btn-primary btn-pos" id="process-sale" disabled>
            <i class="ri-money-dollar-circle-line me-2"></i>
            Process Sale
          </button>
          <div class="row g-2">
            <div class="col-6">
              <button class="btn btn-outline-warning btn-pos w-100" id="hold-transaction">
                <i class="ri-pause-circle-line me-1"></i>
                Hold
              </button>
            </div>
            <div class="col-6">
              <button class="btn btn-outline-info btn-pos w-100" id="view-sales">
                <i class="ri-file-list-line me-1"></i>
                Sales
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Receipt Modal -->
<div class="modal fade" id="receiptModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="ri-receipt-line me-2"></i>
          Sale Receipt
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="receipt-content">
          <!-- Receipt content will be generated here -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="print-receipt">
          <i class="ri-printer-line me-1"></i>
          Print Receipt
        </button>
        <button type="button" class="btn btn-success" id="new-sale">
          <i class="ri-add-line me-1"></i>
          New Sale
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Held Transactions Modal -->
<div class="modal fade" id="heldTransactionsModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="ri-pause-circle-line me-2"></i>
          Held Transactions
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="held-transactions-list">
          <!-- Held transactions will be listed here -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Sales History Modal -->
<div class="modal fade" id="salesHistoryModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-xl modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="ri-file-list-line me-2"></i>
          Sales History
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row mb-3">
          <div class="col-md-4">
            <input type="date" class="form-control" id="sales-date" value="{{ date('Y-m-d') }}">
          </div>
          <div class="col-md-8">
            <div id="sales-summary" class="d-flex gap-3">
              <!-- Sales summary will be displayed here -->
            </div>
          </div>
        </div>
        <div id="sales-history-list">
          <!-- Sales history will be listed here -->
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
