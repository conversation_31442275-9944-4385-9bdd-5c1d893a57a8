<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CurrencySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'currency_name',
        'currency_code',
        'currency_symbol',
        'currency_position',
        'decimal_places',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'decimal_places' => 'integer'
    ];

    /**
     * Get the active currency setting
     */
    public static function getActiveCurrency()
    {
        return static::where('is_active', true)->first() ?? static::getDefaultCurrency();
    }

    /**
     * Get default currency if none exists
     */
    public static function getDefaultCurrency()
    {
        return (object) [
            'currency_name' => 'US Dollar',
            'currency_code' => 'USD',
            'currency_symbol' => '$',
            'currency_position' => 'prefix',
            'decimal_places' => 2,
            'is_active' => true
        ];
    }

    /**
     * Format amount with currency
     */
    public function formatAmount($amount)
    {
        $formattedAmount = number_format($amount, $this->decimal_places);

        if ($this->currency_position === 'prefix') {
            return $this->currency_symbol . $formattedAmount;
        } else {
            return $formattedAmount . $this->currency_symbol;
        }
    }

    /**
     * Get list of common currencies
     */
    public static function getCommonCurrencies()
    {
        return [
            'USD' => ['name' => 'US Dollar', 'symbol' => '$'],
            'EUR' => ['name' => 'Euro', 'symbol' => '€'],
            'GBP' => ['name' => 'British Pound', 'symbol' => '£'],
            'JPY' => ['name' => 'Japanese Yen', 'symbol' => '¥'],
            'CAD' => ['name' => 'Canadian Dollar', 'symbol' => 'C$'],
            'AUD' => ['name' => 'Australian Dollar', 'symbol' => 'A$'],
            'CHF' => ['name' => 'Swiss Franc', 'symbol' => 'CHF'],
            'CNY' => ['name' => 'Chinese Yuan', 'symbol' => '¥'],
            'INR' => ['name' => 'Indian Rupee', 'symbol' => '₹'],
            'BRL' => ['name' => 'Brazilian Real', 'symbol' => 'R$'],
            'RUB' => ['name' => 'Russian Ruble', 'symbol' => '₽'],
            'KRW' => ['name' => 'South Korean Won', 'symbol' => '₩'],
            'SGD' => ['name' => 'Singapore Dollar', 'symbol' => 'S$'],
            'HKD' => ['name' => 'Hong Kong Dollar', 'symbol' => 'HK$'],
            'NOK' => ['name' => 'Norwegian Krone', 'symbol' => 'kr'],
            'SEK' => ['name' => 'Swedish Krona', 'symbol' => 'kr'],
            'DKK' => ['name' => 'Danish Krone', 'symbol' => 'kr'],
            'PLN' => ['name' => 'Polish Zloty', 'symbol' => 'zł'],
            'CZK' => ['name' => 'Czech Koruna', 'symbol' => 'Kč'],
            'HUF' => ['name' => 'Hungarian Forint', 'symbol' => 'Ft'],
            'ILS' => ['name' => 'Israeli Shekel', 'symbol' => '₪'],
            'MXN' => ['name' => 'Mexican Peso', 'symbol' => '$'],
            'ZAR' => ['name' => 'South African Rand', 'symbol' => 'R'],
            'TRY' => ['name' => 'Turkish Lira', 'symbol' => '₺'],
            'AED' => ['name' => 'UAE Dirham', 'symbol' => 'د.إ'],
            'SAR' => ['name' => 'Saudi Riyal', 'symbol' => '﷼'],
            'EGP' => ['name' => 'Egyptian Pound', 'symbol' => '£'],
            'MAD' => ['name' => 'Moroccan Dirham', 'symbol' => 'د.م.'],
            'TND' => ['name' => 'Tunisian Dinar', 'symbol' => 'د.ت'],
            'DZD' => ['name' => 'Algerian Dinar', 'symbol' => 'د.ج']
        ];
    }
}
