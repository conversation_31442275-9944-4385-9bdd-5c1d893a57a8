<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->id();
            $table->string('sale_number')->unique();
            $table->string('customer_name')->nullable();
            $table->string('customer_phone', 20)->nullable();
            $table->decimal('subtotal', 12, 2);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->decimal('amount_paid', 12, 2);
            $table->decimal('change_amount', 10, 2)->default(0);
            $table->enum('payment_method', ['cash', 'card', 'transfer']);
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('cashier_id');
            $table->timestamp('sale_date');
            $table->timestamps();

            // Foreign key constraints with custom short names
            $table->foreign('cashier_id', 'sales_cashier_fk')->references('id')->on('users')->onDelete('restrict');

            // Indexes for performance with custom short names
            $table->index('sale_number', 'sales_number_idx');
            $table->index('sale_date', 'sales_date_idx');
            $table->index('cashier_id', 'sales_cashier_idx');
            $table->index('payment_method', 'sales_payment_method_idx');
            $table->index(['sale_date', 'cashier_id'], 'sales_date_cashier_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales');
    }
};
