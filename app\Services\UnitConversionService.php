<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\UnitConversion;
use Illuminate\Support\Facades\Cache;

class UnitConversionService
{
    /**
     * Convert a quantity from one unit to another.
     */
    public function convert(float $quantity, Unit $fromUnit, Unit $toUnit): float
    {
        if ($fromUnit->id === $toUnit->id) {
            return $quantity;
        }

        if (!$fromUnit->canConvertTo($toUnit)) {
            throw new \InvalidArgumentException(
                "Cannot convert from {$fromUnit->symbol} to {$toUnit->symbol}. Units are not compatible."
            );
        }

        return $fromUnit->convertTo($toUnit, $quantity);
    }

    /**
     * Convert a quantity from one unit to another by unit IDs.
     */
    public function convertByIds(float $quantity, int $fromUnitId, int $toUnitId): float
    {
        $fromUnit = $this->getCachedUnit($fromUnitId);
        $toUnit = $this->getCachedUnit($toUnitId);

        return $this->convert($quantity, $fromUnit, $toUnit);
    }

    /**
     * Check if two units are compatible for conversion.
     */
    public function areCompatible(Unit $unit1, Unit $unit2): bool
    {
        return $unit1->canConvertTo($unit2);
    }

    /**
     * Check if two units are compatible by IDs.
     */
    public function areCompatibleByIds(int $unit1Id, int $unit2Id): bool
    {
        if ($unit1Id === $unit2Id) {
            return true;
        }

        $unit1 = $this->getCachedUnit($unit1Id);
        $unit2 = $this->getCachedUnit($unit2Id);

        return $this->areCompatible($unit1, $unit2);
    }

    /**
     * Get the conversion factor between two units.
     */
    public function getConversionFactor(Unit $fromUnit, Unit $toUnit): float
    {
        if ($fromUnit->id === $toUnit->id) {
            return 1.0;
        }

        return $fromUnit->getConversionFactor($toUnit);
    }

    /**
     * Get all units that are compatible with the given unit.
     */
    public function getCompatibleUnits(Unit $unit): \Illuminate\Support\Collection
    {
        return $unit->getConvertibleUnits();
    }

    /**
     * Convert a quantity to the base unit of its category.
     */
    public function convertToBaseUnit(float $quantity, Unit $unit): float
    {
        if ($unit->isBaseUnit()) {
            return $quantity;
        }

        $baseUnit = $unit->getBaseUnit();
        if (!$baseUnit) {
            throw new \InvalidArgumentException("Unit {$unit->symbol} has no base unit defined.");
        }

        return $this->convert($quantity, $unit, $baseUnit);
    }

    /**
     * Convert a quantity from base unit to target unit.
     */
    public function convertFromBaseUnit(float $quantity, Unit $targetUnit): float
    {
        $baseUnit = $targetUnit->getBaseUnit();
        if (!$baseUnit) {
            throw new \InvalidArgumentException("Unit {$targetUnit->symbol} has no base unit defined.");
        }

        return $this->convert($quantity, $baseUnit, $targetUnit);
    }

    /**
     * Get a cached unit by ID.
     */
    private function getCachedUnit(int $unitId): Unit
    {
        return Cache::remember("unit_{$unitId}", 3600, function () use ($unitId) {
            return Unit::with('category')->findOrFail($unitId);
        });
    }

    /**
     * Clear unit cache.
     */
    public function clearCache(): void
    {
        $units = Unit::all();
        foreach ($units as $unit) {
            Cache::forget("unit_{$unit->id}");
        }
    }

    /**
     * Batch convert multiple quantities.
     */
    public function batchConvert(array $conversions): array
    {
        $results = [];
        
        foreach ($conversions as $conversion) {
            $results[] = [
                'original_quantity' => $conversion['quantity'],
                'from_unit_id' => $conversion['from_unit_id'],
                'to_unit_id' => $conversion['to_unit_id'],
                'converted_quantity' => $this->convertByIds(
                    $conversion['quantity'],
                    $conversion['from_unit_id'],
                    $conversion['to_unit_id']
                )
            ];
        }

        return $results;
    }

    /**
     * Get conversion suggestions for a unit.
     */
    public function getConversionSuggestions(Unit $unit): array
    {
        $compatibleUnits = $this->getCompatibleUnits($unit);
        $suggestions = [];

        foreach ($compatibleUnits as $compatibleUnit) {
            $factor = $this->getConversionFactor($unit, $compatibleUnit);
            $suggestions[] = [
                'unit' => $compatibleUnit,
                'factor' => $factor,
                'description' => "1 {$unit->symbol} = {$factor} {$compatibleUnit->symbol}"
            ];
        }

        return $suggestions;
    }
}
