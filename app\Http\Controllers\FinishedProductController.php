<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\FinishedProduct;
use App\Models\Unit;
use App\Models\Recipe;
use App\Models\RawMaterial;
use App\Services\ImageUploadService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class FinishedProductController extends Controller
{
    protected $imageUploadService;

    public function __construct(ImageUploadService $imageUploadService)
    {
        $this->imageUploadService = $imageUploadService;
    }

    /**
     * Display the finished products management page.
     */
    public function index()
    {
        $finishedProducts = FinishedProduct::with('unit')->get();
        $totalProducts = $finishedProducts->count();
        $activeProducts = FinishedProduct::active()->count();
        $lowStockProducts = FinishedProduct::lowStock()->count();
        $totalValue = $finishedProducts->sum('stock_value');

        return view('pages.finished-products.index', [
            'totalProducts' => $totalProducts,
            'activeProducts' => $activeProducts,
            'lowStockProducts' => $lowStockProducts,
            'totalValue' => $totalValue,
        ]);
    }

    /**
     * Get finished products data for DataTable.
     */
    public function getData(Request $request): JsonResponse
    {
        $columns = [
            1 => 'id',
            2 => 'name',
            3 => 'current_stock',
            4 => 'minimum_stock',
            5 => 'selling_price',
            6 => 'production_time_minutes',
            7 => 'is_active',
        ];

        $totalData = FinishedProduct::count();
        $totalFiltered = $totalData;

        $limit = $request->input('length');
        $start = $request->input('start');
        $order = $columns[$request->input('order.0.column')] ?? 'id';
        $dir = $request->input('order.0.dir');

        $query = FinishedProduct::with(['unit', 'recipes.rawMaterial']);

        if (!empty($request->input('search.value'))) {
            $search = $request->input('search.value');

            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%")
                  ->orWhereHas('unit', function($unitQuery) use ($search) {
                      $unitQuery->where('name', 'LIKE', "%{$search}%")
                               ->orWhere('symbol', 'LIKE', "%{$search}%");
                  });
            });

            $totalFiltered = $query->count();
        }

        $products = $query->offset($start)
            ->limit($limit)
            ->orderBy($order, $dir)
            ->get();

        $data = [];

        if (!empty($products)) {
            $ids = $start;

            foreach ($products as $product) {
                $nestedData['id'] = $product->id;
                $nestedData['fake_id'] = ++$ids;
                $nestedData['name'] = $product->name;
                $nestedData['description'] = $product->description;
                $nestedData['current_stock'] = $product->current_stock;
                $nestedData['minimum_stock'] = $product->minimum_stock;
                $nestedData['selling_price'] = $product->selling_price;
                $nestedData['production_time_minutes'] = $product->production_time_minutes;
                $nestedData['is_active'] = $product->is_active;
                $nestedData['unit'] = $product->unit;
                $nestedData['stock_status'] = $product->stock_status;
                $nestedData['stock_status_color'] = $product->stock_status_color;
                $nestedData['stock_value'] = $product->stock_value;
                $nestedData['production_cost'] = $product->production_cost;
                $nestedData['profit_margin'] = $product->profit_margin;
                $nestedData['max_producible_quantity'] = $product->max_producible_quantity;
                $nestedData['recipe_count'] = $product->recipes->count();
                $nestedData['image_path'] = $product->image_path;
                $nestedData['image_url'] = $product->image_url;
                $nestedData['image_alt'] = $product->image_alt_text;

                $data[] = $nestedData;
            }
        }

        return response()->json([
            'draw' => intval($request->input('draw')),
            'recordsTotal' => intval($totalData),
            'recordsFiltered' => intval($totalFiltered),
            'code' => 200,
            'data' => $data,
        ]);
    }

    /**
     * Store a newly created finished product or update existing one.
     */
    public function store(Request $request): JsonResponse
    {
        // Convert checkbox value to boolean
        $requestData = $request->all();
        if (isset($requestData['is_active'])) {
            $requestData['is_active'] = in_array($requestData['is_active'], ['on', '1', 'true', true, 1], true);
        } else {
            $requestData['is_active'] = false;
        }

        $validator = Validator::make($requestData, [
            'name' => 'required|string|max:100|unique:finished_products,name,' . $request->id,
            'description' => 'nullable|string',
            'unit_id' => 'required|exists:units,id',
            'current_stock' => 'required|numeric|min:0|regex:/^\d+(\.\d{1,3})?$/',
            'minimum_stock' => 'required|numeric|min:0|regex:/^\d+(\.\d{1,3})?$/',
            'selling_price' => 'nullable|numeric|min:0|regex:/^\d+(\.\d{1,2})?$/',
            'production_time_minutes' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'image_alt' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $productId = $request->id;

        // Handle image upload
        $imagePath = null;
        $imageAlt = $requestData['image_alt'] ?? null;

        if ($request->hasFile('image')) {
            try {
                $uploadResult = $this->imageUploadService->uploadImage(
                    $request->file('image'),
                    'finished-products',
                    [
                        'max_width' => 800,
                        'max_height' => 800,
                        'create_thumbnail' => true,
                        'thumbnail_width' => 200,
                        'thumbnail_height' => 200,
                    ]
                );
                $imagePath = $uploadResult['path'];
            } catch (\Exception $e) {
                return response()->json([
                    'message' => 'Image upload failed: ' . $e->getMessage()
                ], 422);
            }
        }

        if ($productId) {
            // Update existing finished product
            $product = FinishedProduct::findOrFail($productId);

            // Delete old image if new one is uploaded
            if ($imagePath && $product->image_path) {
                $this->imageUploadService->deleteImage($product->image_path);
            }

            $updateData = [
                'name' => $requestData['name'],
                'description' => $requestData['description'],
                'unit_id' => $requestData['unit_id'],
                'current_stock' => $requestData['current_stock'],
                'minimum_stock' => $requestData['minimum_stock'],
                'selling_price' => $requestData['selling_price'],
                'production_time_minutes' => $requestData['production_time_minutes'],
                'is_active' => $requestData['is_active'],
            ];

            // Only update image fields if new image is uploaded
            if ($imagePath) {
                $updateData['image_path'] = $imagePath;
                $updateData['image_alt'] = $imageAlt;
            } elseif ($imageAlt !== null) {
                // Update alt text even if no new image
                $updateData['image_alt'] = $imageAlt;
            }

            $product->update($updateData);

            return response()->json(['message' => 'Finished product updated successfully']);
        } else {
            // Create new finished product
            FinishedProduct::create([
                'name' => $requestData['name'],
                'description' => $requestData['description'],
                'unit_id' => $requestData['unit_id'],
                'current_stock' => $requestData['current_stock'],
                'minimum_stock' => $requestData['minimum_stock'],
                'selling_price' => $requestData['selling_price'],
                'production_time_minutes' => $requestData['production_time_minutes'],
                'image_path' => $imagePath,
                'image_alt' => $imageAlt,
                'is_active' => $requestData['is_active'],
            ]);

            return response()->json(['message' => 'Finished product created successfully']);
        }
    }

    /**
     * Show the form for editing the specified finished product.
     */
    public function edit($id): JsonResponse
    {
        $product = FinishedProduct::with('unit')->findOrFail($id);
        return response()->json($product);
    }

    /**
     * Remove the specified finished product from storage.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $product = FinishedProduct::findOrFail($id);

            // Check if finished product has manufacturing orders or movements
            if ($product->manufacturingOrders()->count() > 0) {
                return response()->json([
                    'message' => 'Cannot delete finished product. It has manufacturing orders.'
                ], 422);
            }

            if ($product->movements()->count() > 0) {
                return response()->json([
                    'message' => 'Cannot delete finished product. It has stock movement history.'
                ], 422);
            }

            // Delete associated image
            if ($product->image_path) {
                $this->imageUploadService->deleteImage($product->image_path);
            }

            // Delete associated recipes first
            $product->recipes()->delete();
            $product->delete();

            return response()->json(['message' => 'Finished product deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error deleting finished product'], 500);
        }
    }

    /**
     * Get active finished products for dropdowns.
     */
    public function getActiveProducts(): JsonResponse
    {
        $products = FinishedProduct::with('unit')
            ->active()
            ->orderBy('name')
            ->get(['id', 'name', 'unit_id', 'current_stock']);

        return response()->json($products);
    }

    /**
     * Get low stock finished products.
     */
    public function getLowStockProducts(): JsonResponse
    {
        $products = FinishedProduct::with('unit')
            ->lowStock()
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json($products);
    }

    /**
     * Get recipe for a finished product.
     */
    public function getRecipe($id): JsonResponse
    {
        $product = FinishedProduct::with([
            'recipes.rawMaterial.unit.category',
            'recipes.unit.category'
        ])->findOrFail($id);

        return response()->json([
            'product' => $product,
            'recipes' => $product->recipes
        ]);
    }

    /**
     * Update recipe for a finished product.
     */
    public function updateRecipe(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'recipes' => 'required|array',
            'recipes.*.raw_material_id' => 'required|exists:raw_materials,id',
            'recipes.*.quantity_required' => 'required|numeric|min:0.001',
            'recipes.*.unit_id' => 'required|exists:units,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $product = FinishedProduct::findOrFail($id);

            // Delete existing recipes
            $product->recipes()->delete();

            // Create new recipes
            foreach ($request->recipes as $index => $recipeData) {
                Recipe::create([
                    'finished_product_id' => $product->id,
                    'raw_material_id' => $recipeData['raw_material_id'],
                    'quantity_required' => $recipeData['quantity_required'],
                    'unit_id' => $recipeData['unit_id'],
                    'sort_order' => $index + 1,
                ]);
            }

            DB::commit();
            return response()->json(['message' => 'Recipe updated successfully']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error updating recipe'], 500);
        }
    }

    /**
     * Get movements for a finished product.
     */
    public function getMovements($id): JsonResponse
    {
        $product = FinishedProduct::findOrFail($id);

        $movements = $product->movements()
            ->with(['user', 'manufacturingOrder'])
            ->orderBy('movement_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'product' => $product,
            'movements' => $movements
        ]);
    }
}
