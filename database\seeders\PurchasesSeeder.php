<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Purchase;
use App\Models\PurchaseItem;
use App\Models\RawMaterial;
use App\Models\User;

class PurchasesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for created_by
        $user = User::first();

        // Get some raw materials (French seafood ingredients)
        $flour = RawMaterial::where('name', 'Farine')->first();
        $eggs = RawMaterial::where('name', 'Œufs')->first();
        $crevettes = RawMaterial::where('name', 'Crevettes crues décortiquées')->first();
        $saumon = RawMaterial::where('name', 'Filets de saumon')->first();
        $fromage = RawMaterial::where('name', 'Fromage râpé')->first();
        $herbes = RawMaterial::where('name', '<PERSON><PERSON> fraîches')->first();

        // Purchase 1 - Completed
        $purchase1 = Purchase::create([
            'supplier_name' => 'Poissonnerie Atlantique',
            'supplier_contact' => '+33-1-45-67-89-01',
            'supplier_email' => '<EMAIL>',
            'supplier_address' => '123 Quai des Pêcheurs, 75001 Paris, France',
            'purchase_date' => now()->subDays(15),
            'expected_delivery_date' => now()->subDays(10),
            'actual_delivery_date' => now()->subDays(8),
            'subtotal' => 485.50,
            'delivery_fee' => 35.00,
            'handling_fee' => 15.00,
            'other_fees' => 8.50,
            'total_amount' => 544.00,
            'status' => 'completed',
            'notes' => 'Livraison hebdomadaire de fruits de mer frais',
            'created_by' => $user->id,
            'received_by' => $user->id,
        ]);

        // Purchase 1 Items
        PurchaseItem::create([
            'purchase_id' => $purchase1->id,
            'raw_material_id' => $crevettes->id,
            'quantity' => 5.000,
            'unit_price' => 29.50, // Different price than current
            'received_quantity' => 5.000,
            'notes' => 'Crevettes fraîches de qualité premium',
        ]);

        PurchaseItem::create([
            'purchase_id' => $purchase1->id,
            'raw_material_id' => $saumon->id,
            'quantity' => 3.000,
            'unit_price' => 27.00, // Different price than current
            'received_quantity' => 3.000,
            'notes' => 'Filets de saumon norvégien',
        ]);

        PurchaseItem::create([
            'purchase_id' => $purchase1->id,
            'raw_material_id' => $flour->id,
            'quantity' => 25.000,
            'unit_price' => 2.40, // Different price than current
            'received_quantity' => 25.000,
            'notes' => 'Farine de blé française T55',
        ]);

        // Purchase 2 - Received (ready to complete)
        $purchase2 = Purchase::create([
            'supplier_name' => 'Fromagerie Artisanale',
            'supplier_contact' => '+33-1-42-36-78-90',
            'supplier_email' => '<EMAIL>',
            'supplier_address' => '456 Rue du Fromage, 69001 Lyon, France',
            'purchase_date' => now()->subDays(8),
            'expected_delivery_date' => now()->subDays(3),
            'actual_delivery_date' => now()->subDays(2),
            'subtotal' => 285.00,
            'delivery_fee' => 20.00,
            'handling_fee' => 12.00,
            'other_fees' => 0.00,
            'total_amount' => 317.00,
            'status' => 'received',
            'notes' => 'Commande de fromages premium pour plats gratinés',
            'created_by' => $user->id,
            'received_by' => $user->id,
        ]);

        // Purchase 2 Items
        PurchaseItem::create([
            'purchase_id' => $purchase2->id,
            'raw_material_id' => $fromage->id,
            'quantity' => 6.000,
            'unit_price' => 13.50, // Different price than current
            'received_quantity' => 6.000,
            'notes' => 'Mélange de fromages râpés de qualité',
        ]);

        PurchaseItem::create([
            'purchase_id' => $purchase2->id,
            'raw_material_id' => $herbes->id,
            'quantity' => 800.000,
            'unit_price' => 0.18, // Different price than current
            'received_quantity' => 800.000,
            'notes' => 'Herbes fraîches du marché',
        ]);

        // Purchase 3 - Shipped (ready to receive)
        $purchase3 = Purchase::create([
            'supplier_name' => 'Ferme Bio du Terroir',
            'supplier_contact' => '+33-2-98-76-54-32',
            'supplier_email' => '<EMAIL>',
            'supplier_address' => '789 Chemin de la Ferme, 29000 Quimper, France',
            'purchase_date' => now()->subDays(5),
            'expected_delivery_date' => now()->addDays(1),
            'actual_delivery_date' => null,
            'subtotal' => 156.00,
            'delivery_fee' => 25.00,
            'handling_fee' => 8.00,
            'other_fees' => 5.00,
            'total_amount' => 194.00,
            'status' => 'shipped',
            'notes' => 'Livraison hebdomadaire produits bio - à réfrigérer',
            'created_by' => $user->id,
            'received_by' => null,
        ]);

        // Purchase 3 Items
        PurchaseItem::create([
            'purchase_id' => $purchase3->id,
            'raw_material_id' => $eggs->id,
            'quantity' => 144.000,
            'unit_price' => 0.38, // Different price than current
            'received_quantity' => 0.000,
            'notes' => 'Œufs frais bio de poules élevées au sol',
        ]);

        PurchaseItem::create([
            'purchase_id' => $purchase3->id,
            'raw_material_id' => $herbes->id,
            'quantity' => 500.000,
            'unit_price' => 0.16, // Different price than current
            'received_quantity' => 0.000,
            'notes' => 'Herbes fraîches du jardin bio',
        ]);

        // Purchase 4 - Ordered
        $purchase4 = Purchase::create([
            'supplier_name' => 'Minoterie Française',
            'supplier_contact' => '+33-3-80-45-67-89',
            'supplier_email' => '<EMAIL>',
            'supplier_address' => '321 Avenue des Moulins, 21000 Dijon, France',
            'purchase_date' => now()->subDays(3),
            'expected_delivery_date' => now()->addDays(5),
            'actual_delivery_date' => null,
            'subtotal' => 189.50,
            'delivery_fee' => 18.00,
            'handling_fee' => 8.50,
            'other_fees' => 4.00,
            'total_amount' => 220.00,
            'status' => 'ordered',
            'notes' => 'Commande spéciale de farine premium',
            'created_by' => $user->id,
            'received_by' => null,
        ]);

        // Purchase 4 Items
        PurchaseItem::create([
            'purchase_id' => $purchase4->id,
            'raw_material_id' => $flour->id,
            'quantity' => 50.000,
            'unit_price' => 2.45, // Different price than current
            'received_quantity' => 0.000,
            'notes' => 'Farine T55 de qualité supérieure',
        ]);
    }
}
