<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ManufacturingOrder;
use App\Models\ManufacturingConsumption;
use App\Models\FinishedProduct;
use App\Models\RawMaterial;
use App\Models\User;

class ManufacturingOrdersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for created_by
        $user = User::first();

        // Get some finished products (French seafood dishes)
        $crevettesAil = FinishedProduct::where('name', 'Crevettes à l\'ail')->first();
        $saumonGrille = FinishedProduct::where('name', 'Filet de saumon grillé')->first();
        $crevettesGratinees = FinishedProduct::where('name', 'Crevettes gratinées')->first();
        $poissonMeuniere = FinishedProduct::where('name', 'Poisson blanc meunière')->first();

        // Manufacturing Order 1 - Completed
        $order1 = ManufacturingOrder::create([
            'finished_product_id' => $crevettesAil->id,
            'planned_quantity' => 12.000,
            'produced_quantity' => 12.000,
            'planned_date' => now()->subDays(5),
            'status' => 'completed',
            'responsible_person' => 'Chef Pierre Dubois',
            'estimated_time_minutes' => 45,
            'actual_time_minutes' => 42,
            'notes' => 'Commande spéciale pour événement du weekend',
            'created_by' => $user->id,
        ]);

        // Create consumption records for completed order
        $crevettes = RawMaterial::where('name', 'Crevettes crues décortiquées')->first();
        $beurreAil = RawMaterial::where('name', 'Beurre à l\'ail')->first();
        $herbes = RawMaterial::where('name', 'Herbes fraîches')->first();
        $farine = RawMaterial::where('name', 'Farine')->first();
        $oeufs = RawMaterial::where('name', 'Œufs')->first();

        ManufacturingConsumption::create([
            'manufacturing_order_id' => $order1->id,
            'raw_material_id' => $crevettes->id,
            'planned_quantity' => 1.500, // 12 portions * 125g each
            'actual_quantity' => 1.520, // Slight over-consumption
            'unit_cost' => 28.50,
            'total_cost' => 43.32,
            'consumed_at' => now()->subDays(5),
            'notes' => 'Utilisé légèrement plus de crevettes que prévu',
            'recorded_by' => $user->id,
        ]);

        ManufacturingConsumption::create([
            'manufacturing_order_id' => $order1->id,
            'raw_material_id' => $beurreAil->id,
            'planned_quantity' => 0.300, // 12 portions * 25g each
            'actual_quantity' => 0.300, // Exact consumption
            'unit_cost' => 12.00,
            'total_cost' => 3.60,
            'consumed_at' => now()->subDays(5),
            'notes' => 'Mesure parfaite',
            'recorded_by' => $user->id,
        ]);

        ManufacturingConsumption::create([
            'manufacturing_order_id' => $order1->id,
            'raw_material_id' => $herbes->id,
            'planned_quantity' => 60.000, // 12 portions * 5g each
            'actual_quantity' => 55.000, // Slight under-consumption
            'unit_cost' => 0.15,
            'total_cost' => 8.25,
            'consumed_at' => now()->subDays(5),
            'notes' => 'Économisé un peu sur les herbes',
            'recorded_by' => $user->id,
        ]);

        // Manufacturing Order 2 - In Progress
        $order2 = ManufacturingOrder::create([
            'finished_product_id' => $saumonGrille->id,
            'planned_quantity' => 8.000,
            'produced_quantity' => 0.000,
            'planned_date' => now(),
            'status' => 'in_progress',
            'responsible_person' => 'Chef Marie Leroy',
            'estimated_time_minutes' => 60,
            'actual_time_minutes' => null,
            'notes' => 'Commande importante pour fête d\'anniversaire',
            'created_by' => $user->id,
        ]);

        // Manufacturing Order 3 - Planned
        $order3 = ManufacturingOrder::create([
            'finished_product_id' => $crevettesGratinees->id,
            'planned_quantity' => 6.000,
            'produced_quantity' => 0.000,
            'planned_date' => now()->addDays(2),
            'status' => 'planned',
            'responsible_person' => 'Chef Antoine Moreau',
            'estimated_time_minutes' => 75,
            'actual_time_minutes' => null,
            'notes' => 'Production hebdomadaire de crevettes gratinées',
            'created_by' => $user->id,
        ]);

        // Manufacturing Order 4 - Planned (for tomorrow)
        $order4 = ManufacturingOrder::create([
            'finished_product_id' => $poissonMeuniere->id,
            'planned_quantity' => 10.000,
            'produced_quantity' => 0.000,
            'planned_date' => now()->addDay(),
            'status' => 'planned',
            'responsible_person' => 'Chef Sophie Blanc',
            'estimated_time_minutes' => 50,
            'actual_time_minutes' => null,
            'notes' => 'Poisson meunière pour le café',
            'created_by' => $user->id,
        ]);
    }
}
