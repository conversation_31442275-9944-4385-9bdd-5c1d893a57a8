@extends('layouts/layoutMaster')

@section('title', __('Units Management') . ' - Manufacturing App')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite(['resources/js/units-management.js'])
@endsection

@section('content')

<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Total Units') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{$totalUnits}}</h4>
              <p class="text-success mb-1">(100%)</p>
            </div>
            <small class="mb-0">{{ __('All measurement units') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-ruler-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Active Units') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$activeUnits}}</h4>
              <p class="text-success mb-1">({{$totalUnits > 0 ? round(($activeUnits/$totalUnits)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">{{ __('Currently in use') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Inactive Units') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$inactiveUnits}}</h4>
              <p class="text-danger mb-1">({{$totalUnits > 0 ? round(($inactiveUnits/$totalUnits)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">{{ __('Not currently used') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-close-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">{{ __('Unit Types') }}</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">6</h4>
              <p class="text-info mb-1">({{ __('Categories') }})</p>
            </div>
            <small class="mb-0">{{ __('Weight, Volume, Length, etc.') }}</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-list-check ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Units List Table -->
<div class="card">
  <div class="card-header pb-0">
    <h5 class="card-title mb-0">{{ __('Units Management') }}</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-units table">
      <thead>
        <tr>
          <th></th>
          <th>Id</th>
          <th>{{ __('Unit Name') }}</th>
          <th>{{ __('Symbol') }}</th>
          <th>{{ __('Category') }}</th>
          <th>{{ __('Conversion Factor') }}</th>
          <th>{{ __('Base Unit') }}</th>
          <th>{{ __('Description') }}</th>
          <th>{{ __('Status') }}</th>
          <th>{{ __('Actions') }}</th>
        </tr>
      </thead>
    </table>
  </div>

  <!-- Offcanvas to add new unit -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddUnit" aria-labelledby="offcanvasAddUnitLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddUnitLabel" class="offcanvas-title">{{ __('Add Unit') }}</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 h-100">
      <form class="add-new-unit pt-0" id="addNewUnitForm">
        <input type="hidden" name="id" id="unit_id">

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-unit-name" placeholder="Kilogram" name="name" aria-label="{{ __('Unit Name') }}" />
          <label for="add-unit-name">{{ __('Unit Name') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" id="add-unit-symbol" class="form-control" placeholder="kg" aria-label="{{ __('Symbol') }}" name="symbol" />
          <label for="add-unit-symbol">{{ __('Symbol') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <select class="form-select" id="add-unit-category" name="category_id" aria-label="{{ __('Category') }}">
            <option value="">{{ __('Select Category') }}</option>
          </select>
          <label for="add-unit-category">{{ __('Category') }}</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="number" step="0.000001" min="0.000001" id="add-unit-conversion-factor" class="form-control" placeholder="1" aria-label="{{ __('Conversion Factor') }}" name="conversion_factor" value="1" />
          <label for="add-unit-conversion-factor">{{ __('Conversion Factor') }}</label>
          <div class="form-text">{{ __('Factor to convert to base unit (e.g., 1000 for grams to kg)') }}</div>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <textarea class="form-control" id="add-unit-description" placeholder="{{ __('Description') }}..." name="description" rows="3"></textarea>
          <label for="add-unit-description">{{ __('Description') }}</label>
        </div>

        <div class="mb-5">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="add-unit-base" name="is_base_unit">
            <label class="form-check-label" for="add-unit-base">{{ __('Base Unit') }}</label>
            <div class="form-text">{{ __('Check if this is the base unit for its category') }}</div>
          </div>
        </div>

        <div class="mb-5">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="add-unit-active" name="is_active" checked>
            <label class="form-check-label" for="add-unit-active">{{ __('Active') }}</label>
          </div>
        </div>

        <button type="submit" class="btn btn-primary me-sm-3 me-1 data-submit">{{ __('Submit') }}</button>
        <button type="reset" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">{{ __('Cancel') }}</button>
      </form>
    </div>
  </div>
</div>
@endsection
