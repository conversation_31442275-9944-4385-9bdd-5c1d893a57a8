@extends('layouts/layoutMaster')

@section('title', 'Currency Settings')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js'
])
@endsection

@section('page-script')
@vite(['resources/assets/js/currency-settings.js'])
@endsection

@section('content')

<!-- Display Success/Error Messages -->
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
  {{ session('success') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
  {{ session('error') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if($errors->any())
<div class="alert alert-danger alert-dismissible fade show" role="alert">
  <ul class="mb-0">
    @foreach($errors->all() as $error)
      <li>{{ $error }}</li>
    @endforeach
  </ul>
  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="row g-6">
  <!-- Currency Settings List -->
  <div class="col-xl-8 col-lg-7">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Currency Settings</h5>
        <div>
          <button type="button" class="btn btn-secondary me-2" onclick="console.log('Test button clicked'); alert('JS is working!');">Test JS</button>
          <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCurrencyModal">
            <i class="ri-add-line me-1"></i>Add Currency
          </button>
        </div>
      </div>
      <div class="card-body">
        @if($currencySettings->count() > 0)
        <div class="table-responsive">
          <table class="table table-hover" id="currencyTable">
            <thead>
              <tr>
                <th>Currency</th>
                <th>Code</th>
                <th>Symbol</th>
                <th>Position</th>
                <th>Decimal Places</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              @foreach($currencySettings as $currency)
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    @if($currency->is_active)
                      <i class="ri-star-fill text-warning me-2"></i>
                    @endif
                    <span class="fw-medium">{{ $currency->currency_name }}</span>
                  </div>
                </td>
                <td><span class="badge bg-label-primary">{{ $currency->currency_code }}</span></td>
                <td><span class="fw-bold">{{ $currency->currency_symbol }}</span></td>
                <td>
                  <span class="badge bg-label-{{ $currency->currency_position === 'prefix' ? 'info' : 'secondary' }}">
                    {{ ucfirst($currency->currency_position) }}
                  </span>
                </td>
                <td>{{ $currency->decimal_places }}</td>
                <td>
                  @if($currency->is_active)
                    <span class="badge bg-success">Active</span>
                  @else
                    <span class="badge bg-secondary">Inactive</span>
                  @endif
                </td>
                <td>
                  <div class="dropdown">
                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                      <i class="ri-more-2-line"></i>
                    </button>
                    <div class="dropdown-menu">
                      @if(!$currency->is_active)
                        <form action="{{ route('currency-settings.set-active', $currency) }}" method="POST" class="d-inline">
                          @csrf
                          @method('PATCH')
                          <button type="submit" class="dropdown-item">
                            <i class="ri-star-line me-1"></i>Set as Active
                          </button>
                        </form>
                      @endif
                      <button type="button" class="dropdown-item edit-currency"
                              data-id="{{ $currency->id }}"
                              data-name="{{ $currency->currency_name }}"
                              data-code="{{ $currency->currency_code }}"
                              data-symbol="{{ $currency->currency_symbol }}"
                              data-position="{{ $currency->currency_position }}"
                              data-decimal="{{ $currency->decimal_places }}"
                              data-active="{{ $currency->is_active ? 1 : 0 }}">
                        <i class="ri-edit-box-line me-1"></i>Edit
                      </button>
                      @if(!$currency->is_active)
                        <form action="{{ route('currency-settings.destroy', $currency) }}" method="POST" class="d-inline">
                          @csrf
                          @method('DELETE')
                          <button type="submit" class="dropdown-item text-danger"
                                  onclick="return confirm('Are you sure you want to delete this currency?')">
                            <i class="ri-delete-bin-7-line me-1"></i>Delete
                          </button>
                        </form>
                      @endif
                    </div>
                  </div>
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        @else
        <div class="text-center py-4">
          <img src="{{ asset('assets/img/illustrations/page-misc-error-light.png') }}" alt="No currencies" class="img-fluid mb-3" style="max-width: 200px;">
          <h5>No Currency Settings Found</h5>
          <p class="text-muted">Add your first currency setting to get started.</p>
          <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCurrencyModal">
            <i class="ri-add-line me-1"></i>Add Currency
          </button>
        </div>
        @endif
      </div>
    </div>
  </div>

  <!-- Currency Preview -->
  <div class="col-xl-4 col-lg-5">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">Currency Preview</h5>
      </div>
      <div class="card-body">
        @php
          $activeCurrency = $currencySettings->where('is_active', true)->first();
          if (!$activeCurrency) {
            $activeCurrency = (object) [
              'currency_name' => 'US Dollar',
              'currency_code' => 'USD',
              'currency_symbol' => '$',
              'currency_position' => 'prefix',
              'decimal_places' => 2
            ];
          }
        @endphp

        <div class="mb-4">
          <h6 class="text-muted mb-2">Active Currency</h6>
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-sm me-3">
              <span class="avatar-initial rounded bg-label-primary">{{ substr($activeCurrency->currency_code, 0, 2) }}</span>
            </div>
            <div>
              <h6 class="mb-0">{{ $activeCurrency->currency_name }}</h6>
              <small class="text-muted">{{ $activeCurrency->currency_code }}</small>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h6 class="text-muted mb-2">Format Examples</h6>
          <div class="border rounded p-3">
            @php
              $sampleAmounts = [1234.56, 999.99, 50.00, 0.99];
            @endphp
            @foreach($sampleAmounts as $amount)
              <div class="d-flex justify-content-between mb-2">
                <span class="text-muted">{{ number_format($amount, $activeCurrency->decimal_places) }}</span>
                <span class="fw-medium">
                  @if($activeCurrency->currency_position === 'prefix')
                    {{ $activeCurrency->currency_symbol }}{{ number_format($amount, $activeCurrency->decimal_places) }}
                  @else
                    {{ number_format($amount, $activeCurrency->decimal_places) }}{{ $activeCurrency->currency_symbol }}
                  @endif
                </span>
              </div>
            @endforeach
          </div>
        </div>

        <div class="alert alert-info">
          <i class="ri-information-line me-2"></i>
          <small>This currency format will be used throughout the application for displaying prices and amounts.</small>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Currency Modal -->
<div class="modal fade" id="addCurrencyModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add Currency Setting</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('currency-settings.store') }}" method="POST" id="addCurrencyForm">
        @csrf
        <div class="modal-body">
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label" for="add_currency_list">Select Currency</label>
              <select class="form-select" id="add_currency_list" name="currency_list">
                <option value="">Select a currency...</option>
                @foreach($commonCurrencies as $code => $details)
                  <option value="{{ $code }}" data-name="{{ $details['name'] }}" data-symbol="{{ $details['symbol'] }}">
                    {{ $code }} - {{ $details['name'] }}
                  </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="add_currency_name">Currency Name</label>
              <input type="text" class="form-control" id="add_currency_name" name="currency_name" required>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="add_currency_code">Currency Code</label>
              <input type="text" class="form-control" id="add_currency_code" name="currency_code" maxlength="3" required>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="add_currency_symbol">Currency Symbol</label>
              <input type="text" class="form-control" id="add_currency_symbol" name="currency_symbol" required>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="add_currency_position">Currency Position</label>
              <select class="form-select" id="add_currency_position" name="currency_position" required>
                <option value="prefix">Prefix (e.g., $100)</option>
                <option value="suffix">Suffix (e.g., 100$)</option>
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="add_decimal_places">Decimal Places</label>
              <select class="form-select" id="add_decimal_places" name="decimal_places" required>
                <option value="0">0 (e.g., 100)</option>
                <option value="1">1 (e.g., 100.0)</option>
                <option value="2" selected>2 (e.g., 100.00)</option>
                <option value="3">3 (e.g., 100.000)</option>
                <option value="4">4 (e.g., 100.0000)</option>
              </select>
            </div>
            <div class="col-12">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" value="1">
                <label class="form-check-label" for="add_is_active">
                  Set as active currency
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Add Currency</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Edit Currency Modal -->
<div class="modal fade" id="editCurrencyModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Currency Setting</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="editCurrencyForm" method="POST" onsubmit="console.log('Form onsubmit triggered'); return true;">
        @csrf
        @method('PUT')
        <div class="modal-body">
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label" for="edit_currency_name">Currency Name</label>
              <input type="text" class="form-control" id="edit_currency_name" name="currency_name" required>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="edit_currency_code">Currency Code</label>
              <input type="text" class="form-control" id="edit_currency_code" name="currency_code" maxlength="3" required>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="edit_currency_symbol">Currency Symbol</label>
              <input type="text" class="form-control" id="edit_currency_symbol" name="currency_symbol" required>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="edit_currency_position">Currency Position</label>
              <select class="form-select" id="edit_currency_position" name="currency_position" required>
                <option value="prefix">Prefix (e.g., $100)</option>
                <option value="suffix">Suffix (e.g., 100$)</option>
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="edit_decimal_places">Decimal Places</label>
              <select class="form-select" id="edit_decimal_places" name="decimal_places" required>
                <option value="0">0 (e.g., 100)</option>
                <option value="1">1 (e.g., 100.0)</option>
                <option value="2">2 (e.g., 100.00)</option>
                <option value="3">3 (e.g., 100.000)</option>
                <option value="4">4 (e.g., 100.0000)</option>
              </select>
            </div>
            <div class="col-md-6">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" value="1">
                <label class="form-check-label" for="edit_is_active">
                  Set as active currency
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary" onclick="console.log('Update button clicked');">Update Currency</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
  console.log('Inline script loaded');

  // Test edit button click
  $(document).on('click', '.edit-currency', function() {
    console.log('Edit button clicked - inline script');
    const currencyId = $(this).data('id');
    console.log('Currency ID:', currencyId);

    // Test if modal opens
    $('#editCurrencyModal').modal('show');
  });

  // Test form submission
  $('#editCurrencyForm').on('submit', function(e) {
    console.log('Form submitted - inline script');
    console.log('Form action:', $(this).attr('action'));
    console.log('Form method:', $(this).attr('method'));

    // Don't prevent default for now to see what happens
  });
});
</script>

@endsection