<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\RawMaterial;
use App\Models\Unit;

class RawMaterialsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get units for reference
        $kg = Unit::where('symbol', 'kg')->first();
        $g = Unit::where('symbol', 'g')->first();
        $L = Unit::where('symbol', 'L')->first();
        $ml = Unit::where('symbol', 'ml')->first();
        $pcs = Unit::where('symbol', 'pcs')->first();
        $pack = Unit::where('symbol', 'pack')->first();

        $rawMaterials = [
            // Fruits de mer (Seafood)
            [
                'name' => 'Crevettes crues décortiquées',
                'description' => 'Crevettes fraîches crues décortiquées',
                'unit_id' => $kg->id,
                'current_stock' => 5.000,
                'minimum_stock' => 1.000,
                'unit_price' => 28.50,
                'is_active' => true,
            ],
            [
                'name' => 'Crevettes roses/grises',
                'description' => 'Crevettes roses et grises fraîches',
                'unit_id' => $kg->id,
                'current_stock' => 3.500,
                'minimum_stock' => 0.500,
                'unit_price' => 32.00,
                'is_active' => true,
            ],
            [
                'name' => 'Langoustines',
                'description' => 'Langoustines fraîches de qualité premium',
                'unit_id' => $kg->id,
                'current_stock' => 2.000,
                'minimum_stock' => 0.500,
                'unit_price' => 45.00,
                'is_active' => true,
            ],
            [
                'name' => 'Saint-Jacques',
                'description' => 'Noix de Saint-Jacques fraîches',
                'unit_id' => $kg->id,
                'current_stock' => 1.500,
                'minimum_stock' => 0.300,
                'unit_price' => 55.00,
                'is_active' => true,
            ],
            [
                'name' => 'Filets de poisson blanc',
                'description' => 'Filets de sole, cabillaud, merlan',
                'unit_id' => $kg->id,
                'current_stock' => 4.000,
                'minimum_stock' => 1.000,
                'unit_price' => 22.50,
                'is_active' => true,
            ],
            [
                'name' => 'Filets de saumon',
                'description' => 'Filets de saumon frais',
                'unit_id' => $kg->id,
                'current_stock' => 3.000,
                'minimum_stock' => 0.800,
                'unit_price' => 26.00,
                'is_active' => true,
            ],
            [
                'name' => 'Calamars',
                'description' => 'Calamars en anneaux ou tubes',
                'unit_id' => $kg->id,
                'current_stock' => 2.500,
                'minimum_stock' => 0.500,
                'unit_price' => 18.50,
                'is_active' => true,
            ],
            [
                'name' => 'Chair de crabe',
                'description' => 'Chair de crabe fraîche décortiquée',
                'unit_id' => $kg->id,
                'current_stock' => 1.000,
                'minimum_stock' => 0.200,
                'unit_price' => 65.00,
                'is_active' => true,
            ],

            // Panure et farines (Breadcrumbs and flours)
            [
                'name' => 'Chapelure',
                'description' => 'Chapelure fine pour panure',
                'unit_id' => $kg->id,
                'current_stock' => 8.000,
                'minimum_stock' => 2.000,
                'unit_price' => 3.50,
                'is_active' => true,
            ],
            [
                'name' => 'Panko',
                'description' => 'Chapelure japonaise Panko',
                'unit_id' => $kg->id,
                'current_stock' => 5.000,
                'minimum_stock' => 1.000,
                'unit_price' => 6.80,
                'is_active' => true,
            ],
            [
                'name' => 'Farine',
                'description' => 'Farine de blé tout usage',
                'unit_id' => $kg->id,
                'current_stock' => 25.000,
                'minimum_stock' => 5.000,
                'unit_price' => 2.20,
                'is_active' => true,
            ],

            // Produits laitiers (Dairy products)
            [
                'name' => 'Œufs',
                'description' => 'Œufs frais de poules élevées au sol',
                'unit_id' => $pcs->id,
                'current_stock' => 144.000,
                'minimum_stock' => 24.000,
                'unit_price' => 0.35,
                'is_active' => true,
            ],
            [
                'name' => 'Lait/crème',
                'description' => 'Lait entier et crème fraîche',
                'unit_id' => $L->id,
                'current_stock' => 15.000,
                'minimum_stock' => 3.000,
                'unit_price' => 1.80,
                'is_active' => true,
            ],
            [
                'name' => 'Fromage râpé',
                'description' => 'Mélange de fromages râpés',
                'unit_id' => $kg->id,
                'current_stock' => 6.000,
                'minimum_stock' => 1.500,
                'unit_price' => 12.50,
                'is_active' => true,
            ],
            [
                'name' => 'Jambon',
                'description' => 'Jambon blanc de qualité supérieure',
                'unit_id' => $kg->id,
                'current_stock' => 4.000,
                'minimum_stock' => 1.000,
                'unit_price' => 18.00,
                'is_active' => true,
            ],
            [
                'name' => 'Gruyère',
                'description' => 'Fromage Gruyère AOP',
                'unit_id' => $kg->id,
                'current_stock' => 3.000,
                'minimum_stock' => 0.800,
                'unit_price' => 22.00,
                'is_active' => true,
            ],
            [
                'name' => 'Mozzarella',
                'description' => 'Mozzarella fraîche di bufala',
                'unit_id' => $kg->id,
                'current_stock' => 2.500,
                'minimum_stock' => 0.500,
                'unit_price' => 15.50,
                'is_active' => true,
            ],
            [
                'name' => 'Chèvre',
                'description' => 'Fromage de chèvre frais',
                'unit_id' => $kg->id,
                'current_stock' => 2.000,
                'minimum_stock' => 0.500,
                'unit_price' => 16.80,
                'is_active' => true,
            ],
            [
                'name' => 'Beurre à l\'ail',
                'description' => 'Beurre aromatisé à l\'ail et aux herbes',
                'unit_id' => $kg->id,
                'current_stock' => 3.000,
                'minimum_stock' => 0.800,
                'unit_price' => 12.00,
                'is_active' => true,
            ],

            // Légumes et herbes (Vegetables and herbs)
            [
                'name' => 'Épinards',
                'description' => 'Épinards frais en feuilles',
                'unit_id' => $kg->id,
                'current_stock' => 5.000,
                'minimum_stock' => 1.000,
                'unit_price' => 4.50,
                'is_active' => true,
            ],
            [
                'name' => 'Herbes fraîches',
                'description' => 'Mélange d\'herbes fraîches (persil, ciboulette, aneth)',
                'unit_id' => $g->id,
                'current_stock' => 800.000,
                'minimum_stock' => 200.000,
                'unit_price' => 0.15,
                'is_active' => true,
            ],
        ];

        foreach ($rawMaterials as $material) {
            RawMaterial::create($material);
        }
    }
}
