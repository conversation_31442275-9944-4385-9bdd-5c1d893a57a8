/*
 * Finished Products Management
 */

'use strict';

// Datatable (jquery)
$(function () {
  // Variable declaration for table
  var dt_finished_product_table = $('.datatables-finished-products'),
    select2 = $('.select2'),
    offCanvasForm = $('#offcanvasAddFinishedProduct'),
    recipeModal = $('#recipeModal');

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Image upload handling
  const imageInput = $('#add-product-image');
  const imagePreview = $('#add-product-image-preview');
  const imagePreviewContainer = $('.image-preview-container');
  const removeImageBtn = $('#remove-product-image');

  // Handle image selection
  imageInput.on('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        Swal.fire({
          icon: 'error',
          title: __('Error!'),
          text: __('Please select a valid image file (JPEG, PNG, GIF, WebP)'),
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        });
        imageInput.val('');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        Swal.fire({
          icon: 'error',
          title: __('Error!'),
          text: __('Image size must be less than 5MB'),
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        });
        imageInput.val('');
        return;
      }

      // Show preview
      const reader = new FileReader();
      reader.onload = function(e) {
        imagePreview.attr('src', e.target.result);
        imagePreviewContainer.show();
      };
      reader.readAsDataURL(file);
    }
  });

  // Handle image removal
  removeImageBtn.on('click', function() {
    imageInput.val('');
    imagePreview.attr('src', '');
    imagePreviewContainer.hide();
  });

  // Load units for dropdown
  function loadUnits() {
    $.get(baseUrl + 'units/active', function(data) {
      var unitSelect = $('#add-product-unit');
      unitSelect.empty().append('<option value="">Select Unit</option>');

      data.forEach(function(unit) {
        unitSelect.append(`<option value="${unit.id}">${unit.name} (${unit.symbol})</option>`);
      });
    });
  }

  // Load raw materials for recipe
  function loadRawMaterials() {
    return $.get(baseUrl + 'raw-materials/active');
  }

  // Load units on page load
  loadUnits();

  // Initialize flatpickr when modal is shown
  $('#quickOrderModal').on('shown.bs.modal', function () {
    if (!$('#quick-order-date').hasClass('flatpickr-input')) {
      flatpickr('#quick-order-date', {
        dateFormat: 'Y-m-d',
        minDate: 'today'
      });
    }
  });

  // Finished Products datatable
  if (dt_finished_product_table.length) {
    var dt_finished_product = dt_finished_product_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: baseUrl + 'finished-products/data'
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'id' },
        { data: 'name' },
        { data: 'current_stock' },
        { data: 'selling_price' },
        { data: 'production_time_minutes' },
        { data: 'recipe_count' },
        { data: 'is_active' },
        { data: 'action' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          searchable: false,
          orderable: false,
          targets: 1,
          render: function (data, type, full, meta) {
            return `<span>${full.fake_id}</span>`;
          }
        },
        {
          // Product name with image and description
          targets: 2,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $name = full['name'];
            var $description = full['description'] || '';
            var $imageUrl = full['image_url'] || '/assets/img/default/product-placeholder.svg';
            var $imageAlt = full['image_alt'] || $name;

            return '<div class="d-flex align-items-center">' +
              '<div class="avatar avatar-sm me-3">' +
              '<img src="' + $imageUrl + '" alt="' + $imageAlt + '" class="table-image-thumbnail" ' +
              'onerror="this.src=\'/assets/img/default/product-placeholder.svg\'">' +
              '</div>' +
              '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $name + '</span>' +
              ($description ? '<small class="text-muted">' + $description + '</small>' : '') +
              '</div>' +
              '</div>';
          }
        },
        {
          // Current stock with unit and status
          targets: 3,
          render: function (data, type, full, meta) {
            var $stock = parseFloat(full['current_stock']).toFixed(3);
            var $minStock = parseFloat(full['minimum_stock']).toFixed(3);
            var $unit = full['unit'] ? full['unit']['symbol'] : '';
            var $status = full['stock_status'];
            var $statusColor = full['stock_status_color'];

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $stock + ' ' + $unit + '</span>' +
              '<small class="text-muted">Min: ' + $minStock + ' ' + $unit + '</small>' +
              '<span class="badge bg-label-' + $statusColor + ' badge-sm">' + $status + '</span>' +
              '</div>';
          }
        },
        {
          // Selling price and profit margin
          targets: 4,
          render: function (data, type, full, meta) {
            var $price = full['selling_price'] ? (window.formatCurrency ? window.formatCurrency(full['selling_price']) : '$' + parseFloat(full['selling_price']).toFixed(2)) : 'N/A';
            var $cost = full['production_cost'] ? (window.formatCurrency ? window.formatCurrency(full['production_cost']) : '$' + parseFloat(full['production_cost']).toFixed(2)) : '0.00';
            var $margin = full['profit_margin'] ? (window.formatCurrency ? window.formatCurrency(full['profit_margin']) : '$' + parseFloat(full['profit_margin']).toFixed(2)) : '0.00';

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $price + '</span>' +
              '<small class="text-muted">Cost: ' + $cost + '</small>' +
              '<small class="text-success">Profit: ' + $margin + '</small>' +
              '</div>';
          }
        },
        {
          // Production time and capacity
          targets: 5,
          render: function (data, type, full, meta) {
            var $time = full['production_time_minutes'] ? full['production_time_minutes'] + ' min' : 'N/A';
            var $maxQty = full['max_producible_quantity'] || 0;

            return '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $time + '</span>' +
              '<small class="text-info">Max: ' + $maxQty + ' units</small>' +
              '</div>';
          }
        },
        {
          // Recipe count
          targets: 6,
          className: 'text-center',
          render: function (data, type, full, meta) {
            var $count = full['recipe_count'] || 0;
            var $badgeColor = $count > 0 ? 'success' : 'secondary';
            return '<span class="badge bg-label-' + $badgeColor + '">' + $count + ' ingredients</span>';
          }
        },
        {
          // Status
          targets: 7,
          className: 'text-center',
          render: function (data, type, full, meta) {
            var $status = full['is_active'];
            return `${
              $status
                ? '<span class="badge bg-label-success">Active</span>'
                : '<span class="badge bg-label-secondary">Inactive</span>'
            }`;
          }
        },
        {
          // Actions
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return (
              '<div class="d-flex align-items-center gap-50">' +
              `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddFinishedProduct"><i class="ri-edit-box-line ri-20px"></i></button>` +
              `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}"><i class="ri-delete-bin-7-line ri-20px"></i></button>` +
              '<button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ri-more-2-line ri-20px"></i></button>' +
              '<div class="dropdown-menu dropdown-menu-end m-0">' +
              `<a href="javascript:;" class="dropdown-item manage-recipe" data-id="${full['id']}" data-name="${full['name']}">Manage Recipe</a>` +
              `<a href="javascript:;" class="dropdown-item view-movements" data-id="${full['id']}">View Movements</a>` +
              `<a href="javascript:;" class="dropdown-item create-order" data-id="${full['id']}">Create Order</a>` +
              '</div>' +
              '</div>'
            );
          }
        }
      ],
      order: [[2, 'asc']],
      dom:
        '<"card-header d-flex rounded-0 flex-wrap pb-md-0 pt-0"' +
        '<"me-5 ms-n2"f>' +
        '<"d-flex justify-content-start justify-content-md-end align-items-baseline"<"dt-action-buttons d-flex align-items-start align-items-md-center justify-content-sm-center gap-4"lB>>' +
        '>t' +
        '<"row mx-1"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 20, 50, 70, 100],
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: 'Search Products',
        info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
        paginate: {
          next: '<i class="ri-arrow-right-s-line"></i>',
          previous: '<i class="ri-arrow-left-s-line"></i>'
        }
      },
      // Buttons with Dropdown
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-outline-secondary dropdown-toggle me-4 waves-effect waves-light',
          text: '<i class="ri-upload-2-line ri-16px me-2"></i><span class="d-none d-sm-inline-block">Export </span>',
          buttons: [
            {
              extend: 'print',
              title: 'Finished Products',
              text: '<i class="ri-printer-line me-1" ></i>Print',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'csv',
              title: 'Finished Products',
              text: '<i class="ri-file-text-line me-1" ></i>Csv',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'excel',
              title: 'Finished Products',
              text: '<i class="ri-file-excel-line me-1"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'pdf',
              title: 'Finished Products',
              text: '<i class="ri-file-pdf-line me-1"></i>Pdf',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            }
          ]
        },
        {
          text: '<i class="ri-add-line ri-16px me-0 me-sm-2 align-baseline"></i><span class="d-none d-sm-inline-block">Add New Product</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddFinishedProduct'
          }
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Details of ' + data['name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== ''
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }

  // Delete Record
  $(document).on('click', '.delete-record', function () {
    var product_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // sweetalert for confirmation of delete
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        confirmButton: 'btn btn-primary me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        // delete the data
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}finished-products/${product_id}`,
          success: function (response) {
            dt_finished_product.draw();
            Swal.fire({
              icon: 'success',
              title: 'Deleted!',
              text: response.message || 'The finished product has been deleted!',
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function (xhr) {
            var response = xhr.responseJSON;
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: response.message || 'Error deleting finished product',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Edit record
  $(document).on('click', '.edit-record', function () {
    var product_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // changing the title of offcanvas
    $('#offcanvasAddFinishedProductLabel').html('Edit Finished Product');

    // get data
    $.get(`${baseUrl}finished-products/${product_id}/edit`, function (data) {
      $('#finished_product_id').val(data.id);
      $('#add-product-name').val(data.name);
      $('#add-product-description').val(data.description);
      $('#add-product-unit').val(data.unit_id);
      $('#add-product-current-stock').val(data.current_stock);
      $('#add-product-minimum-stock').val(data.minimum_stock);
      $('#add-product-selling-price').val(data.selling_price);
      $('#add-product-production-time').val(data.production_time_minutes);
      $('#add-product-image-alt').val(data.image_alt);
      $('#add-product-active').prop('checked', data.is_active);

      // Handle existing image
      if (data.image_path) {
        imagePreview.attr('src', data.image_url);
        imagePreviewContainer.show();
      } else {
        imagePreview.attr('src', '');
        imagePreviewContainer.hide();
      }

      // Clear file input
      imageInput.val('');
    });
  });

  // Reset form when adding new product
  $('.add-new').on('click', function () {
    $('#finished_product_id').val('');
    $('#offcanvasAddFinishedProductLabel').html('Add Finished Product');
    $('#addNewFinishedProductForm')[0].reset();
    $('#add-product-active').prop('checked', true);

    // Reset image preview
    imageInput.val('');
    imagePreview.attr('src', '');
    imagePreviewContainer.hide();

    loadUnits(); // Reload units
  });

  // Form validation and submission
  const addNewFinishedProductForm = document.getElementById('addNewFinishedProductForm');

  const fv = FormValidation.formValidation(addNewFinishedProductForm, {
    fields: {
      name: {
        validators: {
          notEmpty: {
            message: 'Please enter product name'
          }
        }
      },
      unit_id: {
        validators: {
          notEmpty: {
            message: 'Please select a unit'
          }
        }
      },
      current_stock: {
        validators: {
          notEmpty: {
            message: 'Please enter current stock'
          },
          numeric: {
            message: 'Current stock must be a number'
          }
        }
      },
      minimum_stock: {
        validators: {
          notEmpty: {
            message: 'Please enter minimum stock'
          },
          numeric: {
            message: 'Minimum stock must be a number'
          }
        }
      }
    },
    plugins: {
      trigger: new FormValidation.plugins.Trigger(),
      bootstrap5: new FormValidation.plugins.Bootstrap5({
        eleValidClass: '',
        rowSelector: function (field, ele) {
          return '.mb-5';
        }
      }),
      submitButton: new FormValidation.plugins.SubmitButton(),
      autoFocus: new FormValidation.plugins.AutoFocus()
    }
  }).on('core.form.valid', function () {
    // Submit form via AJAX
    var formData = new FormData(addNewFinishedProductForm);

    $.ajax({
      type: 'POST',
      url: baseUrl + 'finished-products',
      data: formData,
      processData: false,
      contentType: false,
      success: function (response) {
        dt_finished_product.draw();
        offCanvasForm.offcanvas('hide');

        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: response.message,
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      },
      error: function (xhr) {
        var response = xhr.responseJSON;
        if (response.errors) {
          // Handle validation errors
          Object.keys(response.errors).forEach(function(key) {
            fv.updateFieldStatus(key, 'Invalid', 'notEmpty');
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: response.message || 'Something went wrong',
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      }
    });
  });

  // View Movements
  $(document).on('click', '.view-movements', function () {
    var productId = $(this).data('id');

    $.get(`${baseUrl}finished-products/${productId}/movements`, function (data) {
      $('#movements-product-name').text(data.product.name);
      $('#movements-current-stock').text(data.product.current_stock + ' ' + (data.product.unit ? data.product.unit.symbol : ''));

      var tableBody = $('#movements-table-body');
      tableBody.empty();

      if (data.movements && data.movements.length > 0) {
        $('#no-movements').hide();
        $('#movementsTable').show();

        data.movements.forEach(function(movement) {
          var typeColor = getMovementTypeColor(movement.movement_type);
          var quantityDisplay = movement.quantity > 0 ? '+' + movement.quantity : movement.quantity;
          var valueDisplay = movement.total_value ? (window.formatCurrency ? window.formatCurrency(movement.total_value) : '$' + parseFloat(movement.total_value).toFixed(2)) : '-';

          var row = `
            <tr>
              <td>${formatDate(movement.movement_date)}</td>
              <td><span class="badge bg-label-${typeColor}">${movement.movement_type_label || movement.movement_type}</span></td>
              <td class="${movement.quantity > 0 ? 'text-success' : 'text-danger'}">${quantityDisplay}</td>
              <td>${movement.stock_before}</td>
              <td>${movement.stock_after}</td>
              <td>${valueDisplay}</td>
              <td>${movement.document_reference || '-'}</td>
              <td>${movement.reason || '-'}</td>
              <td>${movement.user ? movement.user.name : '-'}</td>
            </tr>
          `;
          tableBody.append(row);
        });
      } else {
        $('#movementsTable').hide();
        $('#no-movements').show();
      }

      $('#movementsModal').modal('show');
    });
  });

  // Create Order
  $(document).on('click', '.create-order', function () {
    var productId = $(this).data('id');
    var productName = $(this).closest('tr').find('td:nth-child(3)').text().trim();

    // Reset form first
    $('#quickOrderForm')[0].reset();
    $('#quick-order-product-id').val(productId);
    $('#quick-order-product-name').val(productName);

    // Set default planned date to tomorrow
    var tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    $('#quick-order-date').val(tomorrow.toISOString().split('T')[0]);

    // Load product information
    loadProductInfo(productId);

    $('#quickOrderModal').modal('show');
  });

  // Load product information for quick order
  function loadProductInfo(productId) {
    $.get(`${baseUrl}finished-products/${productId}/recipe`, function(data) {
      if (data.product) {
        var product = data.product;
        var unit = product.unit ? product.unit.symbol : '';
        var recipeCost = 0;

        // Calculate recipe cost
        if (data.recipes && data.recipes.length > 0) {
          data.recipes.forEach(function(recipe) {
            if (recipe.raw_material && recipe.raw_material.unit_price) {
              recipeCost += recipe.quantity_required * recipe.raw_material.unit_price;
            }
          });
        }

        // Update product info display
        $('#product-current-stock').text(product.current_stock + ' ' + unit);
        $('#product-unit').text(unit);
        $('#product-recipe-cost').text(window.formatCurrency ? window.formatCurrency(recipeCost) : '$' + recipeCost.toFixed(2));

        // Show product info
        $('#product-info').show();

        // Update estimated total when quantity changes
        updateEstimatedTotal(recipeCost);
      }
    });
  }

  // Update estimated total cost
  function updateEstimatedTotal(recipeCost) {
    var quantity = parseFloat($('#quick-order-quantity').val()) || 0;
    var totalCost = quantity * recipeCost;
    $('#estimated-total-cost').text(window.formatCurrency ? window.formatCurrency(totalCost) : '$' + totalCost.toFixed(2));
  }

  // Update estimated total when quantity changes
  $(document).on('input', '#quick-order-quantity', function() {
    var recipeCost = 0;
    var recipeCostText = $('#product-recipe-cost').text();
    if (recipeCostText && recipeCostText !== '-') {
      // Extract numeric value from formatted currency
      recipeCost = parseFloat(recipeCostText.replace(/[^0-9.-]+/g, '')) || 0;
    }
    updateEstimatedTotal(recipeCost);
  });

  // Form validation for quick order
  const quickOrderForm = document.getElementById('quickOrderForm');
  const quickOrderValidation = FormValidation.formValidation(quickOrderForm, {
    fields: {
      planned_quantity: {
        validators: {
          notEmpty: {
            message: 'Please enter planned quantity'
          },
          numeric: {
            message: 'Planned quantity must be a number'
          },
          greaterThan: {
            message: 'Planned quantity must be greater than 0',
            min: 0.001
          }
        }
      },
      planned_date: {
        validators: {
          notEmpty: {
            message: 'Please select planned date'
          },
          date: {
            format: 'YYYY-MM-DD',
            message: 'Please enter a valid date'
          }
        }
      }
    },
    plugins: {
      trigger: new FormValidation.plugins.Trigger(),
      bootstrap5: new FormValidation.plugins.Bootstrap5({
        eleValidClass: '',
        rowSelector: function (field, ele) {
          return '.mb-5';
        }
      }),
      submitButton: new FormValidation.plugins.SubmitButton(),
      autoFocus: new FormValidation.plugins.AutoFocus()
    }
  });

  // Create Quick Order
  $('#create-quick-order').on('click', function() {
    // Validate form
    quickOrderValidation.validate().then(function(status) {
      if (status === 'Valid') {
        var formData = {
          finished_product_id: $('#quick-order-product-id').val(),
          planned_quantity: $('#quick-order-quantity').val(),
          planned_date: $('#quick-order-date').val(),
          responsible_person: $('#quick-order-responsible').val(),
          estimated_time_minutes: $('#quick-order-time').val(),
          notes: $('#quick-order-notes').val()
        };

        $.ajax({
          type: 'POST',
          url: baseUrl + 'manufacturing-orders',
          data: formData,
          success: function(response) {
            $('#quickOrderModal').modal('hide');

            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: response.message,
              showCancelButton: true,
              confirmButtonText: 'View Orders',
              cancelButtonText: 'Stay Here',
              customClass: {
                confirmButton: 'btn btn-primary me-3',
                cancelButton: 'btn btn-outline-secondary'
              }
            }).then(function(result) {
              if (result.isConfirmed) {
                window.location.href = baseUrl + 'manufacturing-orders';
              }
            });
          },
          error: function(xhr) {
            var response = xhr.responseJSON;
            if (response.errors) {
              // Handle validation errors
              Object.keys(response.errors).forEach(function(key) {
                quickOrderValidation.updateFieldStatus(key, 'Invalid', 'notEmpty');
              });
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: response.message || 'Error creating manufacturing order',
                customClass: {
                  confirmButton: 'btn btn-danger'
                }
              });
            }
          }
        });
      }
    });
  });

  // Helper function to get movement type color
  function getMovementTypeColor(type) {
    switch(type) {
      case 'inbound':
      case 'production':
        return 'success';
      case 'outbound':
        return 'danger';
      case 'adjustment':
        return 'warning';
      default:
        return 'secondary';
    }
  }

  // Helper function to format date
  function formatDate(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
  }
});