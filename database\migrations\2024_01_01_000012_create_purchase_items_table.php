<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('purchase_id');
            $table->unsignedBigInteger('raw_material_id');
            $table->decimal('quantity', 10, 3);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('line_total', 12, 2);
            $table->decimal('received_quantity', 10, 3)->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign key constraints with custom short names
            $table->foreign('purchase_id', 'purchase_items_purchase_fk')->references('id')->on('purchases')->onDelete('cascade');
            $table->foreign('raw_material_id', 'purchase_items_material_fk')->references('id')->on('raw_materials')->onDelete('restrict');

            // Indexes for performance with custom short names
            $table->index('purchase_id', 'purchase_items_purchase_idx');
            $table->index('raw_material_id', 'purchase_items_material_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_items');
    }
};
