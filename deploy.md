# Manufacturing Management System - Ubuntu Server Deployment Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Server Setup](#server-setup)
3. [Install Dependencies](#install-dependencies)
4. [Database Setup](#database-setup)
5. [Application Deployment](#application-deployment)
6. [Web Server Configuration](#web-server-configuration)
7. [SSL Certificate Setup](#ssl-certificate-setup)
8. [Process Management](#process-management)
9. [Security Configuration](#security-configuration)
10. [Monitoring & Maintenance](#monitoring--maintenance)
11. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- Ubuntu 20.04 LTS or 22.04 LTS
- Minimum 2GB RAM (4GB recommended)
- 20GB+ disk space
- Root or sudo access

### Domain & DNS
- Domain name pointed to your server IP
- SSL certificate (Let's Encrypt recommended)

## Server Setup

### 1. Update System
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git unzip software-properties-common
```

### 2. Create Application User
```bash
sudo adduser --system --group --home /var/www/manufacturing manufacturing
sudo usermod -aG www-data manufacturing
```

### 3. Configure Firewall
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw status
```

## Install Dependencies

### 1. Install PHP 8.2
```bash
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update
sudo apt install -y php8.2 php8.2-fpm php8.2-cli php8.2-common php8.2-mysql \
    php8.2-zip php8.2-gd php8.2-mbstring php8.2-curl php8.2-xml php8.2-bcmath \
    php8.2-intl php8.2-redis php8.2-imagick php8.2-dev
```

### 2. Install Composer
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer
```

### 3. Install Node.js & Yarn
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install Yarn
curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
sudo apt update
sudo apt install -y yarn
```

### 4. Install MySQL 8.0
```bash
sudo apt install -y mysql-server mysql-client
sudo mysql_secure_installation
```

### 5. Install Redis
```bash
sudo apt install -y redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

### 6. Install Nginx
```bash
sudo apt install -y nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

## Database Setup

### 1. Create Database and User
```bash
sudo mysql -u root -p
```

```sql
CREATE DATABASE manufacturing_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'manufacturing_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON manufacturing_db.* TO 'manufacturing_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. Configure MySQL
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

Add/modify these settings:
```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
tmp_table_size = 64M
max_heap_table_size = 64M
```

```bash
sudo systemctl restart mysql
```

## Application Deployment

### 1. Clone Repository
```bash
sudo -u manufacturing git clone https://github.com/your-repo/manufacturing-app.git /var/www/manufacturing
cd /var/www/manufacturing
```

### 2. Set Permissions
```bash
sudo chown -R manufacturing:www-data /var/www/manufacturing
sudo chmod -R 755 /var/www/manufacturing
sudo chmod -R 775 /var/www/manufacturing/storage
sudo chmod -R 775 /var/www/manufacturing/bootstrap/cache
```

### 3. Install PHP Dependencies
```bash
sudo -u manufacturing composer install --optimize-autoloader --no-dev
```

### 4. Install Dependencies & Build Assets with Yarn
```bash
# Install Node.js dependencies with Yarn
sudo -u manufacturing yarn install

# Build assets for production
sudo -u manufacturing yarn build

# Verify build was successful
ls -la /var/www/manufacturing/public/build/
sudo -u manufacturing cat /var/www/manufacturing/public/build/manifest.json

# Set proper permissions for build directory
sudo chown -R manufacturing:www-data /var/www/manufacturing/public/build
sudo chmod -R 755 /var/www/manufacturing/public/build
```

### 5. Environment Configuration
```bash
sudo -u manufacturing cp .env.example .env
sudo -u manufacturing nano .env
```

Configure `.env` file:
```env
APP_NAME="Manufacturing Management System"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=manufacturing_db
DB_USERNAME=manufacturing_user
DB_PASSWORD=your_secure_password

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Currency Settings
DEFAULT_CURRENCY=EUR
CURRENCY_POSITION=before
CURRENCY_DECIMALS=2

# App Locale
APP_LOCALE=fr
APP_FALLBACK_LOCALE=fr
APP_FAKER_LOCALE=fr_FR
```

### 6. Generate Application Key
```bash
sudo -u manufacturing php artisan key:generate
```

### 7. Run Migrations and Seeders
```bash
sudo -u manufacturing php artisan migrate --force
sudo -u manufacturing php artisan db:seed --force
```

### 8. Cache Configuration
```bash
sudo -u manufacturing php artisan config:cache
sudo -u manufacturing php artisan route:cache
sudo -u manufacturing php artisan view:cache
```

## Web Server Configuration

### 1. Create Nginx Virtual Host
```bash
sudo nano /etc/nginx/sites-available/manufacturing
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/manufacturing/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Deny access to sensitive files
    location ~ /\.(env|git) {
        deny all;
        return 404;
    }

    # File upload limit
    client_max_body_size 100M;
}
```

### 2. Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/manufacturing /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. Configure PHP-FPM
```bash
sudo nano /etc/php/8.2/fpm/pool.d/manufacturing.conf
```

```ini
[manufacturing]
user = manufacturing
group = www-data
listen = /var/run/php/php8.2-fpm-manufacturing.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 300
```

Update Nginx config to use the new socket:
```bash
sudo nano /etc/nginx/sites-available/manufacturing
```

Change the fastcgi_pass line to:
```nginx
fastcgi_pass unix:/var/run/php/php8.2-fpm-manufacturing.sock;
```

```bash
sudo systemctl restart php8.2-fpm
sudo systemctl reload nginx
```

## SSL Certificate Setup

### 1. Install Certbot
```bash
sudo apt install -y certbot python3-certbot-nginx
```

### 2. Obtain SSL Certificate
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 3. Auto-renewal Setup
```bash
sudo crontab -e
```

Add this line:
```bash
0 12 * * * /usr/bin/certbot renew --quiet
```

## Process Management

### 1. Create Queue Worker Service
```bash
sudo nano /etc/systemd/system/manufacturing-worker.service
```

```ini
[Unit]
Description=Manufacturing Queue Worker
After=network.target

[Service]
Type=simple
User=manufacturing
Group=www-data
Restart=always
ExecStart=/usr/bin/php /var/www/manufacturing/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 2. Create Scheduler Service
```bash
sudo nano /etc/systemd/system/manufacturing-scheduler.service
```

```ini
[Unit]
Description=Manufacturing Task Scheduler
After=network.target

[Service]
Type=oneshot
User=manufacturing
Group=www-data
ExecStart=/usr/bin/php /var/www/manufacturing/artisan schedule:run
```

```bash
sudo nano /etc/systemd/system/manufacturing-scheduler.timer
```

```ini
[Unit]
Description=Run Manufacturing Scheduler Every Minute
Requires=manufacturing-scheduler.service

[Timer]
OnCalendar=*:*
Persistent=true

[Install]
WantedBy=timers.target
```

### 3. Enable Services
```bash
sudo systemctl daemon-reload
sudo systemctl enable manufacturing-worker
sudo systemctl enable manufacturing-scheduler.timer
sudo systemctl start manufacturing-worker
sudo systemctl start manufacturing-scheduler.timer
```

## Security Configuration

### 1. Configure Fail2Ban
```bash
sudo apt install -y fail2ban
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
```

```bash
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 2. Secure File Permissions
```bash
sudo chmod 644 /var/www/manufacturing/.env
sudo chmod -R 644 /var/www/manufacturing/config/
sudo chmod -R 644 /var/www/manufacturing/routes/
sudo find /var/www/manufacturing -type f -name "*.php" -exec chmod 644 {} \;
sudo find /var/www/manufacturing -type d -exec chmod 755 {} \;
```

### 3. Configure Log Rotation
```bash
sudo nano /etc/logrotate.d/manufacturing
```

```bash
/var/www/manufacturing/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 manufacturing www-data
    postrotate
        sudo systemctl reload php8.2-fpm
    endscript
}
```

## Monitoring & Maintenance

### 1. Create Backup Script
```bash
sudo nano /usr/local/bin/manufacturing-backup.sh
```

```bash
#!/bin/bash

# Configuration
APP_DIR="/var/www/manufacturing"
BACKUP_DIR="/var/backups/manufacturing"
DB_NAME="manufacturing_db"
DB_USER="manufacturing_user"
DB_PASS="your_secure_password"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/database_$DATE.sql.gz

# Application files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C $APP_DIR \
    --exclude='node_modules' \
    --exclude='vendor' \
    --exclude='storage/logs' \
    --exclude='storage/framework/cache' \
    --exclude='storage/framework/sessions' \
    --exclude='storage/framework/views' \
    .

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
sudo chmod +x /usr/local/bin/manufacturing-backup.sh
```

### 2. Schedule Backups
```bash
sudo crontab -e
```

Add:
```bash
0 2 * * * /usr/local/bin/manufacturing-backup.sh >> /var/log/manufacturing-backup.log 2>&1
```

### 3. Health Check Script
```bash
sudo nano /usr/local/bin/manufacturing-health.sh
```

```bash
#!/bin/bash

# Check services
services=("nginx" "php8.2-fpm" "mysql" "redis-server" "manufacturing-worker")

for service in "${services[@]}"; do
    if ! systemctl is-active --quiet $service; then
        echo "$(date): $service is not running" >> /var/log/manufacturing-health.log
        systemctl restart $service
    fi
done

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%" >> /var/log/manufacturing-health.log
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "$(date): Memory usage is ${MEM_USAGE}%" >> /var/log/manufacturing-health.log
fi
```

```bash
sudo chmod +x /usr/local/bin/manufacturing-health.sh
```

Schedule health checks:
```bash
sudo crontab -e
```

Add:
```bash
*/5 * * * * /usr/local/bin/manufacturing-health.sh
```

## Troubleshooting

### Common Issues

#### 1. Permission Errors
```bash
sudo chown -R manufacturing:www-data /var/www/manufacturing
sudo chmod -R 775 /var/www/manufacturing/storage
sudo chmod -R 775 /var/www/manufacturing/bootstrap/cache
```

#### 2. Queue Jobs Not Processing
```bash
sudo systemctl status manufacturing-worker
sudo systemctl restart manufacturing-worker
sudo -u manufacturing php artisan queue:restart
```

#### 3. Cache Issues
```bash
sudo -u manufacturing php artisan cache:clear
sudo -u manufacturing php artisan config:clear
sudo -u manufacturing php artisan route:clear
sudo -u manufacturing php artisan view:clear
```

#### 4. Database Connection Issues
```bash
# Test database connection
sudo -u manufacturing php artisan tinker
>>> DB::connection()->getPdo();
```

#### 5. Vite Manifest Not Found Error
```bash
# Check if build directory exists
ls -la /var/www/manufacturing/public/build/

# If missing, build the assets
cd /var/www/manufacturing
sudo -u manufacturing npm install
sudo -u manufacturing npm run build

# Alternative: Build locally and upload
# On local machine: npm run build
# Then upload: scp -r public/build/ user@server:/var/www/manufacturing/public/

# Verify manifest exists
sudo -u manufacturing cat /var/www/manufacturing/public/build/manifest.json

# Fix permissions if needed
sudo chown -R manufacturing:www-data /var/www/manufacturing/public/build
sudo chmod -R 755 /var/www/manufacturing/public/build
```

#### 5. Vite Manifest Not Found Error (Yarn)
```bash
# Check if build directory exists
ls -la /var/www/manufacturing/public/build/

# If missing, build the assets with Yarn
cd /var/www/manufacturing
sudo -u manufacturing yarn install
sudo -u manufacturing yarn build

# Alternative: Build locally and upload
# On local machine: yarn build
# Then upload: scp -r public/build/ user@server:/var/www/manufacturing/public/

# Verify manifest exists
sudo -u manufacturing cat /var/www/manufacturing/public/build/manifest.json

# Fix permissions if needed
sudo chown -R manufacturing:www-data /var/www/manufacturing/public/build
sudo chmod -R 755 /var/www/manufacturing/public/build
```

#### 6. Yarn/Node.js Issues
```bash
# Check versions
node --version
yarn --version

# If Yarn not installed
curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
sudo apt update
sudo apt install -y yarn

# Clear Yarn cache if build fails
sudo -u manufacturing yarn cache clean
sudo -u manufacturing rm -rf node_modules yarn.lock
sudo -u manufacturing yarn install
sudo -u manufacturing yarn build

# Check for build errors
sudo -u manufacturing yarn build --verbose
```

#### 7. Check Application Logs
```bash
tail -f /var/www/manufacturing/storage/logs/laravel.log
tail -f /var/log/nginx/error.log
tail -f /var/log/php8.2-fpm.log
```

### Performance Optimization

#### 1. Enable OPcache
```bash
sudo nano /etc/php/8.2/fpm/conf.d/10-opcache.ini
```

```ini
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

#### 2. Configure Redis for Sessions
```bash
sudo nano /etc/redis/redis.conf
```

Uncomment and set:
```
maxmemory 256mb
maxmemory-policy allkeys-lru
```

```bash
sudo systemctl restart redis-server
```

### Deployment Updates

#### 1. Update Application
```bash
cd /var/www/manufacturing
sudo -u manufacturing git pull origin main
sudo -u manufacturing composer install --optimize-autoloader --no-dev
sudo -u manufacturing yarn install
sudo -u manufacturing yarn build
sudo -u manufacturing php artisan migrate --force
sudo -u manufacturing php artisan config:cache
sudo -u manufacturing php artisan route:cache
sudo -u manufacturing php artisan view:cache
sudo systemctl restart manufacturing-worker
sudo systemctl reload php8.2-fpm
```

#### 2. Zero-Downtime Deployment Script
```bash
sudo nano /usr/local/bin/manufacturing-deploy.sh
```

```bash
#!/bin/bash

APP_DIR="/var/www/manufacturing"
BACKUP_DIR="/var/backups/manufacturing"
DATE=$(date +%Y%m%d_%H%M%S)

echo "Starting deployment..."

# Create backup
/usr/local/bin/manufacturing-backup.sh

# Put application in maintenance mode
sudo -u manufacturing php $APP_DIR/artisan down

# Update code
cd $APP_DIR
sudo -u manufacturing git pull origin main

# Install dependencies
sudo -u manufacturing composer install --optimize-autoloader --no-dev
sudo -u manufacturing yarn install
sudo -u manufacturing yarn build

# Run migrations
sudo -u manufacturing php artisan migrate --force

# Clear and cache
sudo -u manufacturing php artisan config:cache
sudo -u manufacturing php artisan route:cache
sudo -u manufacturing php artisan view:cache

# Restart services
sudo systemctl restart manufacturing-worker
sudo systemctl reload php8.2-fpm

# Bring application back online
sudo -u manufacturing php artisan up

echo "Deployment completed successfully!"
```

```bash
sudo chmod +x /usr/local/bin/manufacturing-deploy.sh
```

## Final Checklist

- [ ] Server updated and secured
- [ ] All dependencies installed
- [ ] Database created and configured
- [ ] Application deployed and configured
- [ ] Web server configured with SSL
- [ ] Queue workers running
- [ ] Scheduler configured
- [ ] Backups scheduled
- [ ] Monitoring in place
- [ ] Firewall configured
- [ ] Fail2Ban configured
- [ ] Log rotation configured

## Support

For issues and support:
- Check application logs: `/var/www/manufacturing/storage/logs/`
- Check system logs: `/var/log/`
- Monitor services: `sudo systemctl status service-name`
- Check resource usage: `htop`, `df -h`, `free -h`

---

**Note**: Replace `your-domain.com`, `your_secure_password`, and other placeholders with your actual values before deployment.
