<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Unit;
use App\Models\UnitCategory;
use App\Models\RawMaterial;
use App\Models\FinishedProduct;
use App\Models\Recipe;
use App\Services\UnitConversionService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UnitConversionTest extends TestCase
{
    use RefreshDatabase;

    protected $conversionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->conversionService = new UnitConversionService();
    }

    public function test_basic_unit_conversion()
    {
        // Create test category
        $category = UnitCategory::create([
            'name' => 'Test Weight',
            'description' => 'Test weight category',
            'is_active' => true,
        ]);

        // Create test units
        $kg = Unit::create([
            'name' => 'Test Kilogram',
            'symbol' => 'test_kg',
            'description' => 'Test base unit of mass',
            'category_id' => $category->id,
            'conversion_factor' => 1,
            'is_base_unit' => true,
            'is_active' => true,
        ]);

        $g = Unit::create([
            'name' => 'Test Gram',
            'symbol' => 'test_g',
            'description' => 'Test unit of mass',
            'category_id' => $category->id,
            'conversion_factor' => 1000,
            'is_base_unit' => false,
            'is_active' => true,
        ]);

        // Test kg to g conversion
        $result = $kg->convertTo($g, 1);
        $this->assertEquals(1000, $result);

        // Test g to kg conversion
        $result = $g->convertTo($kg, 1000);
        $this->assertEquals(1, $result);
    }

    public function test_conversion_service()
    {
        // Create test category and units
        $category = UnitCategory::create([
            'name' => 'Service Test Weight',
            'description' => 'Service test weight category',
            'is_active' => true,
        ]);

        $kg = Unit::create([
            'name' => 'Service Test Kilogram',
            'symbol' => 'svc_kg',
            'description' => 'Service test base unit of mass',
            'category_id' => $category->id,
            'conversion_factor' => 1,
            'is_base_unit' => true,
            'is_active' => true,
        ]);

        $g = Unit::create([
            'name' => 'Service Test Gram',
            'symbol' => 'svc_g',
            'description' => 'Service test unit of mass',
            'category_id' => $category->id,
            'conversion_factor' => 1000,
            'is_base_unit' => false,
            'is_active' => true,
        ]);

        // Test service conversion
        $result = $this->conversionService->convert(2.5, $kg, $g);
        $this->assertEquals(2500, $result);

        // Test compatibility check
        $this->assertTrue($this->conversionService->areCompatible($kg, $g));
    }

    public function test_recipe_unit_conversion()
    {
        // Create test category and units
        $category = UnitCategory::create([
            'name' => 'Recipe Test Weight',
            'description' => 'Recipe test weight category',
            'is_active' => true,
        ]);

        $kg = Unit::create([
            'name' => 'Recipe Test Kilogram',
            'symbol' => 'rcp_kg',
            'description' => 'Recipe test base unit of mass',
            'category_id' => $category->id,
            'conversion_factor' => 1,
            'is_base_unit' => true,
            'is_active' => true,
        ]);

        $g = Unit::create([
            'name' => 'Recipe Test Gram',
            'symbol' => 'rcp_g',
            'description' => 'Recipe test unit of mass',
            'category_id' => $category->id,
            'conversion_factor' => 1000,
            'is_base_unit' => false,
            'is_active' => true,
        ]);

        // Create raw material with kg unit
        $rawMaterial = RawMaterial::create([
            'name' => 'Test Flour',
            'description' => 'Test wheat flour',
            'unit_id' => $kg->id,
            'current_stock' => 10,
            'minimum_stock' => 2,
            'unit_price' => 5.00,
            'is_active' => true,
        ]);

        // Create finished product
        $finishedProduct = FinishedProduct::create([
            'name' => 'Test Bread',
            'description' => 'Test fresh bread',
            'unit_id' => $kg->id,
            'current_stock' => 0,
            'minimum_stock' => 5,
            'selling_price' => 15.00,
            'is_active' => true,
        ]);

        // Create recipe with gram unit (different from raw material unit)
        $recipe = Recipe::create([
            'finished_product_id' => $finishedProduct->id,
            'raw_material_id' => $rawMaterial->id,
            'quantity_required' => 500, // 500 grams
            'unit_id' => $g->id, // Recipe uses grams
            'sort_order' => 1,
        ]);

        // Test unit compatibility
        $this->assertTrue($recipe->isUnitCompatible());

        // Test converted quantity (500g should be 0.5kg)
        $convertedQuantity = $recipe->getConvertedQuantity();
        $this->assertEquals(0.5, $convertedQuantity);

        // Test availability (10kg stock should be enough for 0.5kg requirement)
        $this->assertTrue($recipe->isAvailable());

        // Test cost calculation (0.5kg * $5.00 = $2.50)
        $this->assertEquals(2.5, $recipe->cost);
    }

    public function test_incompatible_units()
    {
        // Create weight category and unit
        $weightCategory = UnitCategory::create([
            'name' => 'Incompatible Test Weight',
            'description' => 'Incompatible test weight category',
            'is_active' => true,
        ]);

        $kg = Unit::create([
            'name' => 'Incompatible Test Kilogram',
            'symbol' => 'inc_kg',
            'description' => 'Incompatible test unit of mass',
            'category_id' => $weightCategory->id,
            'conversion_factor' => 1,
            'is_base_unit' => true,
            'is_active' => true,
        ]);

        // Create volume category and unit
        $volumeCategory = UnitCategory::create([
            'name' => 'Incompatible Test Volume',
            'description' => 'Incompatible test volume category',
            'is_active' => true,
        ]);

        $liter = Unit::create([
            'name' => 'Incompatible Test Liter',
            'symbol' => 'inc_L',
            'description' => 'Incompatible test unit of volume',
            'category_id' => $volumeCategory->id,
            'conversion_factor' => 1,
            'is_base_unit' => true,
            'is_active' => true,
        ]);

        // Test incompatible units
        $this->assertFalse($this->conversionService->areCompatible($kg, $liter));

        // Test exception on conversion
        $this->expectException(\InvalidArgumentException::class);
        $kg->convertTo($liter, 1);
    }
}
