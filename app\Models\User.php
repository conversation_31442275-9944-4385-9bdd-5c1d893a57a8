<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
  use HasFactory, Notifiable, HasRoles;

  /**
   * The attributes that are mass assignable.
   *
   * @var array<int, string>
   */
  protected $fillable = [
    'name',
    'email',
    'password',
  ];

  /**
   * The attributes that should be hidden for serialization.
   *
   * @var array<int, string>
   */
  protected $hidden = [
    'password',
    'remember_token',
  ];

  /**
   * Get the attributes that should be cast.
   *
   * @return array<string, string>
   */
  protected function casts(): array
  {
    return [
      'email_verified_at' => 'datetime',
      'password' => 'hashed',
    ];
  }

  /**
   * Get the manufacturing orders created by this user.
   */
  public function createdManufacturingOrders(): HasMany
  {
    return $this->hasMany(ManufacturingOrder::class, 'created_by');
  }

  /**
   * Get the raw material movements made by this user.
   */
  public function rawMaterialMovements(): HasMany
  {
    return $this->hasMany(RawMaterialMovement::class);
  }

  /**
   * Get the finished product movements made by this user.
   */
  public function finishedProductMovements(): HasMany
  {
    return $this->hasMany(FinishedProductMovement::class);
  }

  /**
   * Get the manufacturing consumptions recorded by this user.
   */
  public function recordedConsumptions(): HasMany
  {
    return $this->hasMany(ManufacturingConsumption::class, 'recorded_by');
  }

  /**
   * Check if the user is a Super Admin.
   * Super Admins bypass all permission checks.
   */
  public function isSuperAdmin(): bool
  {
    return $this->hasRole('Super Admin');
  }
}