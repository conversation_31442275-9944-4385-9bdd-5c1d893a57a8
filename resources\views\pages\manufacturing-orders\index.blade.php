@extends('layouts/layoutMaster')

@section('title', 'Manufacturing Orders - Manufacturing App')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite([
  'resources/js/manufacturing-orders-management.js',
  'resources/js/consumption-management.js'
])
@endsection

@section('content')

<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Total Orders</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{$totalOrders}}</h4>
              <p class="text-success mb-1">(100%)</p>
            </div>
            <small class="mb-0">All manufacturing orders</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-file-list-3-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">In Progress</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$inProgressOrders}}</h4>
              <p class="text-warning mb-1">({{$totalOrders > 0 ? round(($inProgressOrders/$totalOrders)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">Currently producing</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-settings-3-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Completed</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$completedOrders}}</h4>
              <p class="text-success mb-1">({{$totalOrders > 0 ? round(($completedOrders/$totalOrders)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">Successfully finished</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-double-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Overdue</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-1">{{$overdueOrders}}</h4>
              <p class="text-danger mb-1">({{$totalOrders > 0 ? round(($overdueOrders/$totalOrders)*100) : 0}}%)</p>
            </div>
            <small class="mb-0">Past due date</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-danger rounded-3">
              <div class="ri-alarm-warning-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Manufacturing Orders List Table -->
<div class="card">
  <div class="card-header pb-0 d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Manufacturing Orders Management</h5>
    <a href="{{ route('manufacturing-orders.kanban') }}" class="btn btn-outline-primary">
      <i class="ri-kanban-view me-1"></i>{{ __('Kanban View') }}
    </a>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-manufacturing-orders table">
      <thead>
        <tr>
          <th></th>
          <th>Order #</th>
          <th>Product</th>
          <th>Quantity</th>
          <th>Progress</th>
          <th>Planned Date</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>

  <!-- Offcanvas to add new manufacturing order -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddOrder" aria-labelledby="offcanvasAddOrderLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddOrderLabel" class="offcanvas-title">Add Manufacturing Order</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 h-100">
      <form class="add-new-order pt-0" id="addNewOrderForm">
        <input type="hidden" name="id" id="order_id">

        <!-- Order Type Selection -->
        <div class="form-floating form-floating-outline mb-5">
          <select id="add-order-type" class="form-select" name="order_type">
            <option value="single">Single Product Order</option>
            <option value="multi">Multi-Product Order</option>
          </select>
          <label for="add-order-type">Order Type</label>
        </div>

        <!-- Single Product Fields -->
        <div id="single-product-fields">
          <div class="form-floating form-floating-outline mb-5">
            <select id="add-order-product" class="form-select" name="finished_product_id">
              <option value="">Select Product</option>
            </select>
            <label for="add-order-product">Finished Product</label>
          </div>

          <div class="form-floating form-floating-outline mb-5">
            <input type="number" step="0.001" class="form-control" id="add-order-quantity" placeholder="0.000" name="planned_quantity" />
            <label for="add-order-quantity">Planned Quantity</label>
          </div>
        </div>

        <!-- Multi-Product Fields -->
        <div id="multi-product-fields" style="display: none;">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="fw-medium mb-0">Products to Manufacture</h6>
            <button type="button" class="btn btn-sm btn-primary" id="add-product-item">
              <i class="ri-add-line me-1"></i>Add Product
            </button>
          </div>

          <div id="product-items-container">
            <!-- Product items will be added here -->
          </div>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-order-date" placeholder="YYYY-MM-DD" name="planned_date" />
          <label for="add-order-date">Planned Date</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <input type="text" class="form-control" id="add-order-responsible" placeholder="John Doe" name="responsible_person" />
          <label for="add-order-responsible">Responsible Person</label>
        </div>

        <!-- Time Field - Single Product -->
        <div class="form-floating form-floating-outline mb-5" id="single-time-field">
          <input type="number" class="form-control" id="add-order-time" placeholder="120" name="estimated_time_minutes" />
          <label for="add-order-time">Estimated Time (minutes)</label>
        </div>

        <!-- Time Field - Multi Product -->
        <div class="form-floating form-floating-outline mb-5" id="multi-time-field" style="display: none;">
          <input type="number" class="form-control" id="add-order-total-time" placeholder="240" name="total_estimated_time_minutes" />
          <label for="add-order-total-time">Total Estimated Time (minutes)</label>
        </div>

        <div class="form-floating form-floating-outline mb-5">
          <textarea class="form-control" id="add-order-notes" placeholder="Order notes..." name="notes" rows="3"></textarea>
          <label for="add-order-notes">Notes</label>
        </div>

        <div class="mb-5" id="product-info" style="display: none;">
          <div class="alert alert-info">
            <h6 class="alert-heading mb-2">Product Information</h6>
            <div class="row">
              <div class="col-6">
                <small class="text-muted">Production Cost:</small>
                <div class="fw-medium" id="product-cost">{{ Helper::formatCurrency(0) }}</div>
              </div>
              <div class="col-6">
                <small class="text-muted">Max Producible:</small>
                <div class="fw-medium" id="product-max-qty">0 units</div>
              </div>
            </div>
          </div>
        </div>

        <button type="submit" class="btn btn-primary me-sm-3 me-1 data-submit">Submit</button>
        <button type="reset" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">Cancel</button>
      </form>
    </div>
  </div>

  <!-- Order Details Modal -->
  <div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="orderDetailsModalLabel">Order Details</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="order-details-content">
            <!-- Order details will be loaded here -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
          <div id="order-actions">
            <!-- Action buttons will be added here based on order status -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Consumption Management Modal -->
  <div class="modal fade" id="consumptionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="consumptionModalLabel">Material Consumption</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="consumptionForm">
            <input type="hidden" id="consumption-order-id">

            <div class="mb-4">
              <h6 class="fw-medium">Order: <span id="consumption-order-number" class="text-primary"></span></h6>
            </div>

            <div id="consumption-items">
              <!-- Consumption items will be dynamically added here -->
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="save-consumption">Save Consumption</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Complete Order Modal -->
  <div class="modal fade" id="completeOrderModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Complete Manufacturing Order</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="completeOrderForm">
            <input type="hidden" id="complete-order-id">

            <div class="form-floating form-floating-outline mb-4">
              <input type="number" step="0.001" class="form-control" id="complete-produced-quantity" name="produced_quantity" required>
              <label for="complete-produced-quantity">Produced Quantity</label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <input type="number" class="form-control" id="complete-actual-time" name="actual_time_minutes">
              <label for="complete-actual-time">Actual Time (minutes)</label>
            </div>

            <div class="form-floating form-floating-outline mb-4">
              <textarea class="form-control" id="complete-notes" name="notes" rows="3"></textarea>
              <label for="complete-notes">Completion Notes</label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="confirm-complete-order">Complete Order</button>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
