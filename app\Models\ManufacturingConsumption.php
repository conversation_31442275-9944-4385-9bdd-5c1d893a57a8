<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ManufacturingConsumption extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'manufacturing_order_id',
        'raw_material_id',
        'planned_quantity',
        'actual_quantity',
        'unit_cost',
        'total_cost',
        'notes',
        'consumed_at',
        'recorded_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'planned_quantity' => 'decimal:3',
        'actual_quantity' => 'decimal:3',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'consumed_at' => 'datetime',
    ];

    /**
     * Get the manufacturing order that this consumption belongs to.
     */
    public function manufacturingOrder(): BelongsTo
    {
        return $this->belongsTo(ManufacturingOrder::class);
    }

    /**
     * Get the raw material that was consumed.
     */
    public function rawMaterial(): BelongsTo
    {
        return $this->belongsTo(RawMaterial::class);
    }

    /**
     * Get the user who recorded this consumption.
     */
    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    /**
     * Get the variance between planned and actual quantity.
     */
    public function getQuantityVarianceAttribute(): float
    {
        return $this->actual_quantity - $this->planned_quantity;
    }

    /**
     * Get the variance percentage.
     */
    public function getVariancePercentageAttribute(): float
    {
        if ($this->planned_quantity <= 0) {
            return 0;
        }
        return (($this->actual_quantity - $this->planned_quantity) / $this->planned_quantity) * 100;
    }

    /**
     * Check if the consumption is over the planned amount.
     */
    public function isOverConsumption(): bool
    {
        return $this->actual_quantity > $this->planned_quantity;
    }

    /**
     * Check if the consumption is under the planned amount.
     */
    public function isUnderConsumption(): bool
    {
        return $this->actual_quantity < $this->planned_quantity;
    }

    /**
     * Get the consumption status for UI display.
     */
    public function getConsumptionStatusAttribute(): string
    {
        if ($this->actual_quantity == $this->planned_quantity) {
            return 'exact';
        } elseif ($this->actual_quantity > $this->planned_quantity) {
            return 'over';
        }
        return 'under';
    }

    /**
     * Get the consumption status color for UI.
     */
    public function getConsumptionStatusColorAttribute(): string
    {
        return match ($this->consumption_status) {
            'exact' => 'green',
            'over' => 'red',
            'under' => 'orange',
            default => 'gray',
        };
    }

    /**
     * Boot the model to automatically calculate total_cost.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($consumption) {
            if ($consumption->actual_quantity && $consumption->unit_cost) {
                $consumption->total_cost = $consumption->actual_quantity * $consumption->unit_cost;
            }
        });
    }
}
