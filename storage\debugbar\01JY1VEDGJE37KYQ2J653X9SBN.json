{"__meta": {"id": "01JY1VEDGJE37KYQ2J653X9SBN", "datetime": "2025-06-18 15:45:11", "utime": **********.69892, "method": "GET", "uri": "/manufacturing-orders", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.233523, "end": **********.698952, "duration": 0.46542906761169434, "duration_str": "465ms", "measures": [{"label": "Booting", "start": **********.233523, "relative_start": 0, "end": **********.364882, "relative_end": **********.364882, "duration": 0.*****************, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.364914, "relative_start": 0.*****************, "end": **********.698954, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.375597, "relative_start": 0.****************, "end": **********.383059, "relative_end": **********.383059, "duration": 0.007462024688720703, "duration_str": "7.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.488857, "relative_start": 0.*****************, "end": **********.697979, "relative_end": **********.697979, "duration": 0.****************, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: pages.manufacturing-orders.index", "start": **********.548537, "relative_start": 0.***************, "end": **********.548537, "relative_end": **********.548537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.layoutMaster", "start": **********.56427, "relative_start": 0.***************, "end": **********.56427, "relative_end": **********.56427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.contentNavbarLayout", "start": **********.565038, "relative_start": 0.3315150737762451, "end": **********.565038, "relative_end": **********.565038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenuData", "start": **********.599633, "relative_start": 0.36611008644104004, "end": **********.599633, "relative_end": **********.599633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenu", "start": **********.6361, "relative_start": 0.40257716178894043, "end": **********.6361, "relative_end": **********.6361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: _partials.macros", "start": **********.668953, "relative_start": 0.43543004989624023, "end": **********.668953, "relative_end": **********.668953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.submenu", "start": **********.670945, "relative_start": 0.4374220371246338, "end": **********.670945, "relative_end": **********.670945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.navbar.navbar", "start": **********.671931, "relative_start": 0.43840813636779785, "end": **********.671931, "relative_end": **********.671931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.footer.footer", "start": **********.677259, "relative_start": 0.44373607635498047, "end": **********.677259, "relative_end": **********.677259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.commonMaster", "start": **********.679088, "relative_start": 0.44556522369384766, "end": **********.679088, "relative_end": **********.679088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.styles", "start": **********.680814, "relative_start": 0.44729113578796387, "end": **********.680814, "relative_end": **********.680814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scriptsIncludes", "start": **********.684654, "relative_start": 0.45113110542297363, "end": **********.684654, "relative_end": **********.684654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scripts", "start": **********.688659, "relative_start": 0.4551360607147217, "end": **********.688659, "relative_end": **********.688659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 4590928, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "pages.manufacturing-orders.index", "param_count": null, "params": [], "start": **********.548452, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/pages/manufacturing-orders/index.blade.phppages.manufacturing-orders.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Fpages%2Fmanufacturing-orders%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "layouts.layoutMaster", "param_count": null, "params": [], "start": **********.564191, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/layoutMaster.blade.phplayouts.layoutMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FlayoutMaster.blade.php&line=1", "ajax": false, "filename": "layoutMaster.blade.php", "line": "?"}}, {"name": "layouts.contentNavbarLayout", "param_count": null, "params": [], "start": **********.564958, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/contentNavbarLayout.blade.phplayouts.contentNavbarLayout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcontentNavbarLayout.blade.php&line=1", "ajax": false, "filename": "contentNavbarLayout.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenuData", "param_count": null, "params": [], "start": **********.599545, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenuData.blade.phplayouts.sections.menu.verticalMenuData", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenuData.blade.php&line=1", "ajax": false, "filename": "verticalMenuData.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenu", "param_count": null, "params": [], "start": **********.636009, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenu.blade.phplayouts.sections.menu.verticalMenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenu.blade.php&line=1", "ajax": false, "filename": "verticalMenu.blade.php", "line": "?"}}, {"name": "_partials.macros", "param_count": null, "params": [], "start": **********.668825, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/_partials/macros.blade.php_partials.macros", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2F_partials%2Fmacros.blade.php&line=1", "ajax": false, "filename": "macros.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.submenu", "param_count": null, "params": [], "start": **********.670829, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/submenu.blade.phplayouts.sections.menu.submenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2Fsubmenu.blade.php&line=1", "ajax": false, "filename": "submenu.blade.php", "line": "?"}}, {"name": "layouts.sections.navbar.navbar", "param_count": null, "params": [], "start": **********.67185, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/navbar/navbar.blade.phplayouts.sections.navbar.navbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fnavbar%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}}, {"name": "layouts.sections.footer.footer", "param_count": null, "params": [], "start": **********.677137, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/footer/footer.blade.phplayouts.sections.footer.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Ffooter%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "layouts.commonMaster", "param_count": null, "params": [], "start": **********.678923, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/commonMaster.blade.phplayouts.commonMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcommonMaster.blade.php&line=1", "ajax": false, "filename": "commonMaster.blade.php", "line": "?"}}, {"name": "layouts.sections.styles", "param_count": null, "params": [], "start": **********.680699, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/styles.blade.phplayouts.sections.styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "layouts.sections.scriptsIncludes", "param_count": null, "params": [], "start": **********.684574, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scriptsIncludes.blade.phplayouts.sections.scriptsIncludes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2FscriptsIncludes.blade.php&line=1", "ajax": false, "filename": "scriptsIncludes.blade.php", "line": "?"}}, {"name": "layouts.sections.scripts", "param_count": null, "params": [], "start": **********.688566, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scripts.blade.phplayouts.sections.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03237, "accumulated_duration_str": "32.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `manu_sessions` where `id` = '3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr' limit 1", "type": "query", "params": [], "bindings": ["3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.407325, "duration": 0.0245, "duration_str": "24.5ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "manufacturing", "explain": null, "start_percent": 0, "width_percent": 75.687}, {"sql": "select * from `manu_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.442844, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "manufacturing", "explain": null, "start_percent": 75.687, "width_percent": 4.943}, {"sql": "select * from `manu_manufacturing_orders` where `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4515371, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:24", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=24", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 80.63, "width_percent": 2.039}, {"sql": "select * from `manu_finished_products` where `manu_finished_products`.`id` in (2, 4, 5, 6) and `manu_finished_products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.461, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=24", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 82.669, "width_percent": 2.904}, {"sql": "select * from `manu_users` where `manu_users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4666898, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=24", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "24"}, "connection": "manufacturing", "explain": null, "start_percent": 85.573, "width_percent": 1.328}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.470887, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:26", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=26", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "26"}, "connection": "manufacturing", "explain": null, "start_percent": 86.901, "width_percent": 1.236}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'in_progress' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4799068, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=27", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "27"}, "connection": "manufacturing", "explain": null, "start_percent": 88.137, "width_percent": 1.884}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4843688, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ManufacturingOrderController.php:28", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ManufacturingOrderController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\ManufacturingOrderController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=28", "ajax": false, "filename": "ManufacturingOrderController.php", "line": "28"}, "connection": "manufacturing", "explain": null, "start_percent": 90.022, "width_percent": 1.297}, {"sql": "select * from `manu_cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.49555, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 91.319, "width_percent": 2.348}, {"sql": "select `manu_permissions`.*, `manu_model_has_permissions`.`model_id` as `pivot_model_id`, `manu_model_has_permissions`.`permission_id` as `pivot_permission_id`, `manu_model_has_permissions`.`model_type` as `pivot_model_type` from `manu_permissions` inner join `manu_model_has_permissions` on `manu_permissions`.`id` = `manu_model_has_permissions`.`permission_id` where `manu_model_has_permissions`.`model_id` in (1) and `manu_model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.512484, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "manufacturing", "explain": null, "start_percent": 93.667, "width_percent": 1.576}, {"sql": "select `manu_roles`.*, `manu_model_has_roles`.`model_id` as `pivot_model_id`, `manu_model_has_roles`.`role_id` as `pivot_role_id`, `manu_model_has_roles`.`model_type` as `pivot_model_type` from `manu_roles` inner join `manu_model_has_roles` on `manu_roles`.`id` = `manu_model_has_roles`.`role_id` where `manu_model_has_roles`.`model_id` in (1) and `manu_model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.518023, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "manufacturing", "explain": null, "start_percent": 95.243, "width_percent": 1.359}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "pages.manufacturing-orders.index", "file": "C:\\wamp64\\www\\recipe\\resources\\views/pages/manufacturing-orders/index.blade.php", "line": 233}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.557958, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 96.602, "width_percent": 3.398}]}, "models": {"data": {"App\\Models\\ManufacturingOrder": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrder.php&line=1", "ajax": false, "filename": "ManufacturingOrder.php", "line": "?"}}, "App\\Models\\FinishedProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FFinishedProduct.php&line=1", "ajax": false, "filename": "FinishedProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 38, "messages": [{"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-372240863 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372240863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.523151, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-759547645 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759547645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529638, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-944632740 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944632740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.531706, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1477921548 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477921548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53367, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-111214628 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111214628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535276, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-814829694 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814829694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537056, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-575443257 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575443257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538722, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-78263099 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78263099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541338, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-465053385 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465053385\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.548129, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1209079436 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209079436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.568162, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2021028191 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021028191\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.570133, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1485964410 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485964410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.572431, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1151859434 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151859434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.578345, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177083961 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177083961\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.581555, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1605391647 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605391647\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.584398, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-977826676 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977826676\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.586035, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1419273754 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419273754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587904, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2007760428 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007760428\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599247, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1736993681 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736993681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602055, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1796504501 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796504501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.60434, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-4647546 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4647546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6079, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-869364435 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869364435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.613118, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059266528 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059266528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.616552, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1983333676 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983333676\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.619864, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1724116139 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724116139\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621648, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478738030 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478738030\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.623925, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1128614216 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128614216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63564, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1928561295 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928561295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63752, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1375489015 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375489015\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.638746, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-663128406 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663128406\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.641114, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-753790453 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753790453\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.645369, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1491053488 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491053488\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.64702, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1080299364 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080299364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.648575, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1166664977 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166664977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.649507, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1735174605 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735174605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.65045, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1077004556 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077004556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.654874, "xdebug_link": null}, {"message": "[\n  ability => manage-settings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-428690360 data-indent-pad=\"  \"><span class=sf-dump-note>manage-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428690360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.665964, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2028415190 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028415190\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.667677, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/manufacturing-orders", "action_name": "manufacturing-orders.index", "controller_action": "App\\Http\\Controllers\\ManufacturingOrderController@index", "uri": "GET manufacturing-orders", "controller": "App\\Http\\Controllers\\ManufacturingOrderController@index<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/manufacturing-orders", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FManufacturingOrderController.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ManufacturingOrderController.php:22-38</a>", "middleware": "web, auth", "duration": "495ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1611976775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1611976775\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1463190634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1463190634\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/manufacturing-orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1583 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImhUQkxzNkp0cngzZE8zR1pKRUVNSnc9PSIsInZhbHVlIjoiN2tucGJyU2xwUUhVR0VOTmhodU52RlpSM0c4bm11Vy9haFN6VExSWVFScHp6Ui8xSGhpaUExQ3AyQXNmemJLcXRLUnZ3WFpXQTNlRTV6U0E4dHVMZG1jQWVYYk5VUGgxNmgxMmhQT0JKSERTeXp1UEJDaEQ3ck9QMTloUllvb3kiLCJtYWMiOiIzYWM5MjUyZGRjYTE5NTZmZjhkYWM4ZTkwZTY5NGQwNDAzOGY4OTFjYWIyMDI5ZGI0MmI1OTk2NTg2NmQxN2I1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjJwa1FLVE04WjJnZHhHem9qcFoyR1E9PSIsInZhbHVlIjoiQ0dFSTUvWU9ab1BmaGZ1MzVyaGdHQkd4UjR3M0QxcFM3RjhHNGFtK3kxNHpCVk9zTzRxQm1UL1hBcmtLNTFnUkZiQzg1QzJMZEdmOWlaUldwelNYU0dTcVhoUElqajQrOER1WVV3bFQwbktmdnRmcGdIZ3I4U3VRVjB2MFd0QngiLCJtYWMiOiJkMzMxNmY5N2QwMWQwY2Y3OGQ2YWM3M2JmYjQxMmY2NjNlZjEzODYzN2M0ZTMwOWIwMDVhNGRhOTQ1NWZmNmIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1469908817 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3cYJWfONRExitP6cRqkHj46SyLATd8nkyimFdAkr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469908817\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-172790696 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 15:45:11 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172790696\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1967644363 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gns0d17oI0vbPk0VM8NxAKEEbQ6Tm7f3EcLKIX0Z</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/translations/fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967644363\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/manufacturing-orders", "action_name": "manufacturing-orders.index", "controller_action": "App\\Http\\Controllers\\ManufacturingOrderController@index"}, "badge": null}}