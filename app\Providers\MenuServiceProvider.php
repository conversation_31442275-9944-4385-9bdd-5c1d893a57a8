<?php

namespace App\Providers;

use Illuminate\Support\Facades\View;
use Illuminate\Routing\Route;
use Illuminate\Support\ServiceProvider;

class MenuServiceProvider extends ServiceProvider
{
  /**
   * Register services.
   */
  public function register(): void
  {
    //
  }

  /**
   * Bootstrap services.
   */
  public function boot(): void
  {
    // Use view composer to generate menu data dynamically
    View::composer('layouts.sections.menu.verticalMenu', function ($view) {
      // Include the Blade menu data file to get filtered menu
      $verticalMenuData = null;

      // Render the menu data blade file to get the filtered menu
      $menuContent = view('layouts.sections.menu.verticalMenuData')->render();

      // Get the menu data from the rendered view
      $verticalMenuData = $view->getData()['verticalMenuData'] ?? $this->getFallbackMenuData();

      // For horizontal menu, use the same data for now (can be customized later)
      $horizontalMenuData = $verticalMenuData;

      // Share menu data with the view
      $view->with('menuData', [$verticalMenuData, $horizontalMenuData]);
    });

    // Also share with all views for backward compatibility
    View::composer('*', function ($view) {
      if (!$view->offsetExists('menuData')) {
        // Get menu data for authenticated users
        if (auth()->check()) {
          $verticalMenuData = $this->getMenuData();
          $horizontalMenuData = $verticalMenuData; // Same data for both
          $view->with('menuData', [$verticalMenuData, $horizontalMenuData]);
        } else {
          // Fallback for non-authenticated users
          $view->with('menuData', [$this->getFallbackMenuData(), $this->getFallbackMenuData()]);
        }
      }
    });
  }

  /**
   * Get menu data with permission filtering
   */
  private function getMenuData()
  {
    // Include the menu data and return the processed menu
    $menuItems = $this->getMenuItems();

    // Filter menu items based on permissions
    $filteredMenu = $this->filterMenuByPermissions($menuItems);

    // Convert to object format for compatibility
    return (object) ['menu' => array_map(function($item) {
      return (object) array_map(function($value) {
        if (is_array($value)) {
          return array_map(function($subItem) {
            return (object) $subItem;
          }, $value);
        }
        return $value;
      }, $item);
    }, $filteredMenu)];
  }

  /**
   * Get fallback menu data for non-authenticated users
   */
  private function getFallbackMenuData()
  {
    return (object) ['menu' => [
      (object) [
        'name' => 'Dashboard',
        'icon' => 'menu-icon tf-icons ri-home-smile-line',
        'slug' => 'dashboard',
        'url' => '/'
      ]
    ]];
  }

  /**
   * Define menu structure
   */
  private function getMenuItems()
  {
    return [
      // Dashboard - Always visible for authenticated users
      [
        'name' => __('Dashboard'),
        'icon' => 'menu-icon tf-icons ri-home-smile-line',
        'slug' => 'dashboard',
        'url' => '/',
        'permission' => null
      ],

      // Manufacturing Section
      [
        'name' => __('Manufacturing'),
        'icon' => 'menu-icon tf-icons ri-settings-3-line',
        'slug' => 'manufacturing',
        'permission' => null,
        'submenu' => [
          [
            'url' => 'units',
            'name' => __('Units'),
            'slug' => 'units.index',
            'permission' => 'view-units'
          ],
          [
            'url' => 'raw-materials',
            'name' => __('Raw Materials'),
            'slug' => 'raw-materials.index',
            'permission' => 'view-raw-materials'
          ],
          [
            'url' => 'finished-products',
            'name' => __('Finished Products'),
            'slug' => 'finished-products.index',
            'permission' => 'view-finished-products'
          ],
          [
            'url' => 'manufacturing-orders',
            'name' => __('Manufacturing Orders'),
            'slug' => 'manufacturing-orders.index',
            'permission' => 'view-manufacturing-orders'
          ],
          [
            'url' => 'purchases',
            'name' => __('Purchases'),
            'slug' => 'purchases.index',
            'permission' => 'view-purchases'
          ]
        ]
      ],

      // POS Section
      [
        'name' => __('Point of Sale'),
        'icon' => 'menu-icon tf-icons ri-shopping-cart-line',
        'slug' => 'pos',
        'url' => 'pos',
        'permission' => 'access-pos'
      ],

      // Menu Header for Admin Section
      [
        'menuHeader' => __('Administration'),
        'permission' => 'view-users|view-roles'
      ],

      // User Management
      [
        'name' => __('Users'),
        'icon' => 'menu-icon tf-icons ri-user-line',
        'slug' => 'app-user',
        'permission' => 'view-users',
        'submenu' => [
          [
            'url' => 'laravel/user-management',
            'name' => __('User Management'),
            'slug' => 'laravel-example-user-management',
            'permission' => 'view-users'
          ]
        ]
      ],

      // Roles & Permissions
      [
        'name' => __('Roles & Permissions'),
        'icon' => 'menu-icon tf-icons ri-lock-2-line',
        'slug' => 'app-access',
        'permission' => 'view-roles',
        'submenu' => [
          [
            'url' => 'roles',
            'name' => __('Roles Management'),
            'slug' => 'roles.index',
            'permission' => 'view-roles'
          ]
        ]
      ]
    ];
  }

  /**
   * Filter menu items based on user permissions
   */
  private function filterMenuByPermissions($menuItems)
  {
    $filteredMenu = [];

    foreach ($menuItems as $item) {
      // Check if it's a menu header
      if (isset($item['menuHeader'])) {
        if ($this->hasMenuPermission($item['permission'] ?? null)) {
          $filteredMenu[] = $item;
        }
        continue;
      }

      // Check if item has submenu
      if (isset($item['submenu'])) {
        // Filter submenu items
        $filteredSubmenu = [];
        foreach ($item['submenu'] as $subItem) {
          if ($this->hasMenuPermission($subItem['permission'] ?? null)) {
            $filteredSubmenu[] = $subItem;
          }
        }

        // Only include parent if it has visible submenu items
        if (!empty($filteredSubmenu)) {
          $item['submenu'] = $filteredSubmenu;
          $filteredMenu[] = $item;
        }
      } else {
        // Single menu item
        if ($this->hasMenuPermission($item['permission'] ?? null)) {
          $filteredMenu[] = $item;
        }
      }
    }

    return $filteredMenu;
  }

  /**
   * Check if user has required permission
   */
  private function hasMenuPermission($permission)
  {
    if (!$permission || !auth()->check()) {
      return $permission === null; // No permission required or not authenticated
    }

    if (strpos($permission, '|') !== false) {
      // Multiple permissions (OR condition)
      $permissions = explode('|', $permission);
      foreach ($permissions as $perm) {
        if (auth()->user()->can(trim($perm))) {
          return true;
        }
      }
      return false;
    }

    return auth()->user()->can($permission);
  }
}
