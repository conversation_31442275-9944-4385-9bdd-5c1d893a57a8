<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sale_id');
            $table->unsignedBigInteger('finished_product_id');
            $table->decimal('quantity', 10, 3);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('line_total', 12, 2);
            $table->timestamps();

            // Foreign key constraints with custom short names
            $table->foreign('sale_id', 'sale_items_sale_fk')->references('id')->on('sales')->onDelete('cascade');
            $table->foreign('finished_product_id', 'sale_items_product_fk')->references('id')->on('finished_products')->onDelete('restrict');

            // Indexes for performance with custom short names
            $table->index('sale_id', 'sale_items_sale_idx');
            $table->index('finished_product_id', 'sale_items_product_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_items');
    }
};
