<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RawMaterialMovement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'raw_material_id',
        'movement_type',
        'quantity',
        'unit_price',
        'total_value',
        'document_reference',
        'reason',
        'notes',
        'stock_before',
        'stock_after',
        'user_id',
        'movement_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'total_value' => 'decimal:2',
        'stock_before' => 'decimal:3',
        'stock_after' => 'decimal:3',
        'movement_date' => 'datetime',
    ];

    /**
     * The movement types available.
     */
    const MOVEMENT_TYPES = [
        'inbound' => 'Inbound',
        'outbound' => 'Outbound',
        'adjustment' => 'Adjustment',
    ];

    /**
     * Get the raw material that this movement belongs to.
     */
    public function rawMaterial(): BelongsTo
    {
        return $this->belongsTo(RawMaterial::class);
    }

    /**
     * Get the user who made this movement.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include inbound movements.
     */
    public function scopeInbound($query)
    {
        return $query->where('movement_type', 'inbound');
    }

    /**
     * Scope a query to only include outbound movements.
     */
    public function scopeOutbound($query)
    {
        return $query->where('movement_type', 'outbound');
    }

    /**
     * Scope a query to only include adjustment movements.
     */
    public function scopeAdjustment($query)
    {
        return $query->where('movement_type', 'adjustment');
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    /**
     * Get the movement type label.
     */
    public function getMovementTypeLabelAttribute(): string
    {
        return self::MOVEMENT_TYPES[$this->movement_type] ?? $this->movement_type;
    }

    /**
     * Get the movement type color for UI.
     */
    public function getMovementTypeColorAttribute(): string
    {
        return match ($this->movement_type) {
            'inbound' => 'green',
            'outbound' => 'red',
            'adjustment' => 'blue',
            default => 'gray',
        };
    }

    /**
     * Get the absolute quantity (always positive for display).
     */
    public function getAbsoluteQuantityAttribute(): float
    {
        return abs($this->quantity);
    }

    /**
     * Check if this is a positive movement (increases stock).
     */
    public function isPositiveMovement(): bool
    {
        return $this->quantity > 0;
    }

    /**
     * Check if this is a negative movement (decreases stock).
     */
    public function isNegativeMovement(): bool
    {
        return $this->quantity < 0;
    }

    /**
     * Boot the model to automatically calculate total_value.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($movement) {
            if ($movement->unit_price && $movement->quantity) {
                $movement->total_value = abs($movement->quantity) * $movement->unit_price;
            }
        });
    }
}
