<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\ManufacturingOrder;
use App\Models\ManufacturingOrderItem;
use App\Models\FinishedProduct;
use App\Models\ManufacturingConsumption;
use App\Models\RawMaterialMovement;
use App\Models\FinishedProductMovement;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ManufacturingOrderController extends Controller
{
    /**
     * Display the manufacturing orders management page.
     */
    public function index()
    {
        $orders = ManufacturingOrder::with(['finishedProduct', 'createdBy'])->get();
        $totalOrders = $orders->count();
        $plannedOrders = ManufacturingOrder::planned()->count();
        $inProgressOrders = ManufacturingOrder::inProgress()->count();
        $completedOrders = ManufacturingOrder::completed()->count();
        $overdueOrders = $orders->filter(fn($order) => $order->isOverdue())->count();

        return view('pages.manufacturing-orders.index', [
            'totalOrders' => $totalOrders,
            'plannedOrders' => $plannedOrders,
            'inProgressOrders' => $inProgressOrders,
            'completedOrders' => $completedOrders,
            'overdueOrders' => $overdueOrders,
        ]);
    }

    /**
     * Display the manufacturing orders kanban view.
     */
    public function kanban()
    {
        $orders = ManufacturingOrder::with(['finishedProduct', 'createdBy'])->get();
        $totalOrders = $orders->count();
        $plannedOrders = ManufacturingOrder::planned()->count();
        $inProgressOrders = ManufacturingOrder::inProgress()->count();
        $completedOrders = ManufacturingOrder::completed()->count();
        $overdueOrders = $orders->filter(fn($order) => $order->isOverdue())->count();

        return view('pages.manufacturing-orders.kanban', [
            'totalOrders' => $totalOrders,
            'plannedOrders' => $plannedOrders,
            'inProgressOrders' => $inProgressOrders,
            'completedOrders' => $completedOrders,
            'overdueOrders' => $overdueOrders,
        ]);
    }

    /**
     * Get manufacturing orders data for DataTable.
     */
    public function getData(Request $request): JsonResponse
    {
        $columns = [
            1 => 'id',
            2 => 'order_number',
            3 => 'finished_product_id',
            4 => 'planned_quantity',
            5 => 'planned_date',
            6 => 'status',
            7 => 'created_at',
        ];

        $totalData = ManufacturingOrder::count();
        $totalFiltered = $totalData;

        $limit = $request->input('length');
        $start = $request->input('start');
        $order = $columns[$request->input('order.0.column')] ?? 'id';
        $dir = $request->input('order.0.dir');

        $query = ManufacturingOrder::with(['finishedProduct.unit', 'finishedProduct.recipes.rawMaterial', 'createdBy', 'consumptions', 'items.finishedProduct.recipes.rawMaterial']);

        if (!empty($request->input('search.value'))) {
            $search = $request->input('search.value');

            $query->where(function($q) use ($search) {
                $q->where('order_number', 'LIKE', "%{$search}%")
                  ->orWhere('responsible_person', 'LIKE', "%{$search}%")
                  ->orWhere('notes', 'LIKE', "%{$search}%")
                  ->orWhereHas('finishedProduct', function($productQuery) use ($search) {
                      $productQuery->where('name', 'LIKE', "%{$search}%");
                  })
                  ->orWhereHas('createdBy', function($userQuery) use ($search) {
                      $userQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });

            $totalFiltered = $query->count();
        }

        $orders = $query->offset($start)
            ->limit($limit)
            ->orderBy($order, $dir)
            ->get();

        $data = [];

        if (!empty($orders)) {
            $ids = $start;

            foreach ($orders as $manufacturingOrder) {
                $nestedData['id'] = $manufacturingOrder->id;
                $nestedData['fake_id'] = ++$ids;
                $nestedData['order_number'] = $manufacturingOrder->order_number;
                $nestedData['finished_product'] = $manufacturingOrder->finishedProduct;
                $nestedData['planned_quantity'] = $manufacturingOrder->planned_quantity;
                $nestedData['produced_quantity'] = $manufacturingOrder->produced_quantity;
                $nestedData['planned_date'] = $manufacturingOrder->planned_date;
                $nestedData['status'] = $manufacturingOrder->status;
                $nestedData['status_label'] = $manufacturingOrder->status_label;
                $nestedData['status_color'] = $manufacturingOrder->status_color;
                $nestedData['completion_percentage'] = $manufacturingOrder->completion_percentage;
                $nestedData['remaining_quantity'] = $manufacturingOrder->remaining_quantity;
                $nestedData['responsible_person'] = $manufacturingOrder->responsible_person;
                $nestedData['estimated_cost'] = $manufacturingOrder->estimated_cost;
                $nestedData['actual_cost'] = $manufacturingOrder->actual_cost;
                $nestedData['estimated_time_formatted'] = $manufacturingOrder->estimated_time_formatted;
                $nestedData['actual_time_formatted'] = $manufacturingOrder->actual_time_formatted;
                $nestedData['created_by'] = $manufacturingOrder->createdBy;
                $nestedData['is_overdue'] = $manufacturingOrder->isOverdue();
                $nestedData['can_start'] = $manufacturingOrder->canStart();
                $nestedData['cannot_start_reason'] = $manufacturingOrder->cannot_start_reason;
                $nestedData['can_complete'] = $manufacturingOrder->canComplete();
                $nestedData['consumptions_count'] = $manufacturingOrder->consumptions->count();

                $data[] = $nestedData;
            }
        }

        return response()->json([
            'draw' => intval($request->input('draw')),
            'recordsTotal' => intval($totalData),
            'recordsFiltered' => intval($totalFiltered),
            'code' => 200,
            'data' => $data,
        ]);
    }

    /**
     * Store a newly created manufacturing order or update existing one.
     */
    public function store(Request $request): JsonResponse
    {
        // Determine if this is a single or multi-product order
        $orderType = $request->input('order_type', 'single');

        if ($orderType === 'multi') {
            $validator = Validator::make($request->all(), [
                'order_type' => 'required|in:single,multi',
                'planned_date' => 'required|date|after_or_equal:today',
                'responsible_person' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'total_estimated_time_minutes' => 'nullable|integer|min:0',
                'items' => 'required|array|min:1',
                'items.*.finished_product_id' => 'required|exists:finished_products,id',
                'items.*.planned_quantity' => 'required|numeric|min:0.001|regex:/^\d+(\.\d{1,3})?$/',
                'items.*.estimated_time_minutes' => 'nullable|integer|min:0',
                'items.*.notes' => 'nullable|string',
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'finished_product_id' => 'required|exists:finished_products,id',
                'planned_quantity' => 'required|numeric|min:0.001|regex:/^\d+(\.\d{1,3})?$/',
                'planned_date' => 'required|date|after_or_equal:today',
                'responsible_person' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'estimated_time_minutes' => 'nullable|integer|min:0',
            ]);
        }

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $orderId = $request->id;

        try {
            DB::beginTransaction();

            if ($orderId) {
                // Update existing order (only if status is planned)
                $order = ManufacturingOrder::findOrFail($orderId);

                if ($order->status !== 'planned') {
                    return response()->json([
                        'message' => 'Cannot edit order that is not in planned status.'
                    ], 422);
                }

                $order->update([
                    'finished_product_id' => $request->finished_product_id,
                    'planned_quantity' => $request->planned_quantity,
                    'planned_date' => $request->planned_date,
                    'responsible_person' => $request->responsible_person,
                    'notes' => $request->notes,
                    'estimated_time_minutes' => $request->estimated_time_minutes,
                ]);

                // Recalculate estimated cost
                $this->calculateEstimatedCost($order);

                DB::commit();
                return response()->json(['message' => 'Manufacturing order updated successfully']);
            } else {
                // Create new order
                if ($orderType === 'multi') {
                    $order = ManufacturingOrder::create([
                        'order_type' => 'multi',
                        'planned_date' => $request->planned_date,
                        'responsible_person' => $request->responsible_person,
                        'notes' => $request->notes,
                        'total_estimated_time_minutes' => $request->total_estimated_time_minutes,
                        'created_by' => Auth::id(),
                    ]);

                    // Create order items
                    $totalEstimatedCost = 0;
                    foreach ($request->items as $itemData) {
                        $product = FinishedProduct::find($itemData['finished_product_id']);
                        $estimatedCost = $product->production_cost * $itemData['planned_quantity'];
                        $totalEstimatedCost += $estimatedCost;

                        ManufacturingOrderItem::create([
                            'manufacturing_order_id' => $order->id,
                            'finished_product_id' => $itemData['finished_product_id'],
                            'planned_quantity' => $itemData['planned_quantity'],
                            'estimated_cost' => $estimatedCost,
                            'estimated_time_minutes' => $itemData['estimated_time_minutes'] ?? null,
                            'notes' => $itemData['notes'] ?? null,
                        ]);
                    }

                    // Update total estimated cost
                    $order->update(['total_estimated_cost' => $totalEstimatedCost]);
                } else {
                    // Single product order
                    $order = ManufacturingOrder::create([
                        'order_type' => 'single',
                        'finished_product_id' => $request->finished_product_id,
                        'planned_quantity' => $request->planned_quantity,
                        'planned_date' => $request->planned_date,
                        'responsible_person' => $request->responsible_person,
                        'notes' => $request->notes,
                        'estimated_time_minutes' => $request->estimated_time_minutes,
                        'created_by' => Auth::id(),
                    ]);

                    // Calculate estimated cost
                    $this->calculateEstimatedCost($order);
                }

                DB::commit();
                return response()->json(['message' => 'Manufacturing order created successfully']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error saving manufacturing order'], 500);
        }
    }

    /**
     * Calculate estimated cost for a manufacturing order.
     */
    private function calculateEstimatedCost(ManufacturingOrder $order)
    {
        $product = $order->finishedProduct;
        $estimatedCost = $product->production_cost * $order->planned_quantity;

        $order->update(['estimated_cost' => $estimatedCost]);
    }

    /**
     * Show the form for editing the specified manufacturing order.
     */
    public function edit($id): JsonResponse
    {
        $order = ManufacturingOrder::with(['finishedProduct', 'createdBy'])->findOrFail($id);
        return response()->json($order);
    }

    /**
     * Remove the specified manufacturing order from storage.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $order = ManufacturingOrder::findOrFail($id);

            // Can only delete planned orders
            if ($order->status !== 'planned') {
                return response()->json([
                    'message' => 'Cannot delete order that is not in planned status.'
                ], 422);
            }

            $order->delete();
            return response()->json(['message' => 'Manufacturing order deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error deleting manufacturing order'], 500);
        }
    }

    /**
     * Start a manufacturing order.
     */
    public function start($id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $order = ManufacturingOrder::findOrFail($id);

            if (!$order->canStart()) {
                return response()->json([
                    'message' => 'Cannot start this order. Check if materials are available and order is in planned status.'
                ], 422);
            }

            $order->update([
                'status' => 'in_progress',
                'started_at' => now(),
            ]);

            // Create planned consumptions based on recipe
            $this->createPlannedConsumptions($order);

            DB::commit();
            return response()->json(['message' => 'Manufacturing order started successfully']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error starting manufacturing order'], 500);
        }
    }

    /**
     * Create planned consumptions for a manufacturing order.
     */
    private function createPlannedConsumptions(ManufacturingOrder $order)
    {
        $consumptions = [];

        if ($order->isMultiProduct()) {
            // Multi-product order: aggregate consumptions from all items
            foreach ($order->items as $item) {
                $product = $item->finishedProduct;
                foreach ($product->recipes as $recipe) {
                    $plannedQuantity = $recipe->quantity_required * $item->planned_quantity;
                    $materialId = $recipe->raw_material_id;

                    if (isset($consumptions[$materialId])) {
                        $consumptions[$materialId]['planned_quantity'] += $plannedQuantity;
                    } else {
                        $consumptions[$materialId] = [
                            'raw_material_id' => $materialId,
                            'planned_quantity' => $plannedQuantity,
                            'unit_cost' => $recipe->rawMaterial->unit_price ?? 0,
                        ];
                    }
                }
            }
        } else {
            // Single product order
            $product = $order->finishedProduct;
            foreach ($product->recipes as $recipe) {
                $plannedQuantity = $recipe->quantity_required * $order->planned_quantity;
                $materialId = $recipe->raw_material_id;

                $consumptions[$materialId] = [
                    'raw_material_id' => $materialId,
                    'planned_quantity' => $plannedQuantity,
                    'unit_cost' => $recipe->rawMaterial->unit_price ?? 0,
                ];
            }
        }

        // Create consumption records
        foreach ($consumptions as $consumption) {
            ManufacturingConsumption::create([
                'manufacturing_order_id' => $order->id,
                'raw_material_id' => $consumption['raw_material_id'],
                'planned_quantity' => $consumption['planned_quantity'],
                'actual_quantity' => 0,
                'unit_cost' => $consumption['unit_cost'],
                'total_cost' => 0,
                'recorded_by' => Auth::id(),
            ]);
        }
    }

    /**
     * Complete a manufacturing order.
     */
    public function complete(Request $request, $id): JsonResponse
    {
        $order = ManufacturingOrder::with('items')->findOrFail($id);

        if ($order->isMultiProduct()) {
            $validator = Validator::make($request->all(), [
                'total_actual_time_minutes' => 'nullable|integer|min:0',
                'notes' => 'nullable|string',
                'items' => 'required|array',
                'items.*.id' => 'required|exists:manufacturing_order_items,id',
                'items.*.produced_quantity' => 'required|numeric|min:0|regex:/^\d+(\.\d{1,3})?$/',
                'items.*.actual_time_minutes' => 'nullable|integer|min:0',
                'items.*.notes' => 'nullable|string',
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'produced_quantity' => 'required|numeric|min:0.001|regex:/^\d+(\.\d{1,3})?$/',
                'actual_time_minutes' => 'nullable|integer|min:0',
                'notes' => 'nullable|string',
            ]);
        }

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            if (!$order->canComplete()) {
                return response()->json([
                    'message' => 'Cannot complete this order. Order must be in progress.'
                ], 422);
            }

            if ($order->isMultiProduct()) {
                // Multi-product completion
                $totalProducedQuantity = 0;
                $totalActualCost = 0;

                // Update each item
                foreach ($request->items as $itemData) {
                    $item = ManufacturingOrderItem::findOrFail($itemData['id']);
                    $producedQuantity = $itemData['produced_quantity'];
                    $totalProducedQuantity += $producedQuantity;

                    // Check material availability for this item
                    $this->validateMaterialAvailabilityForItem($order, $item, $producedQuantity);

                    // Auto-consume materials for this item
                    $itemActualCost = $this->autoConsumeForItem($order, $item, $producedQuantity);
                    $totalActualCost += $itemActualCost;

                    // Update item
                    $item->update([
                        'produced_quantity' => $producedQuantity,
                        'actual_cost' => $itemActualCost,
                        'actual_time_minutes' => $itemData['actual_time_minutes'] ?? null,
                        'notes' => $itemData['notes'] ?? null,
                    ]);

                    // Create finished product movement for this item
                    $this->createFinishedProductMovementForItem($order, $item, $producedQuantity, $itemActualCost);
                }

                // Update order totals
                $order->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                    'total_actual_time_minutes' => $request->total_actual_time_minutes,
                    'total_actual_cost' => $totalActualCost,
                    'notes' => $request->notes,
                ]);
            } else {
                // Single product completion
                $producedQuantity = $request->produced_quantity;

                // Check if sufficient materials are available for auto-consumption
                $this->validateMaterialAvailability($order, $producedQuantity);

                // Auto-consume materials if no manual consumption has been recorded
                $this->autoConsumeIfNeeded($order, $producedQuantity);

                // Update order
                $order->update([
                    'status' => 'completed',
                    'produced_quantity' => $producedQuantity,
                    'completed_at' => now(),
                    'actual_time_minutes' => $request->actual_time_minutes,
                    'notes' => $request->notes,
                ]);

                // Calculate actual cost from consumptions
                $actualCost = $order->consumptions->sum('total_cost');
                $order->update(['actual_cost' => $actualCost]);

                // Create finished product movement
                $this->createFinishedProductMovement($order, $producedQuantity);
            }

            DB::commit();
            return response()->json(['message' => 'Manufacturing order completed successfully']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    /**
     * Create finished product movement when order is completed.
     */
    private function createFinishedProductMovement(ManufacturingOrder $order, $producedQuantity)
    {
        $product = $order->finishedProduct;
        $stockBefore = $product->current_stock;
        $stockAfter = $stockBefore + $producedQuantity;
        $unitCost = $order->actual_cost > 0 ? $order->actual_cost / $producedQuantity : 0;

        FinishedProductMovement::create([
            'finished_product_id' => $product->id,
            'movement_type' => 'production',
            'quantity' => $producedQuantity,
            'unit_cost' => $unitCost,
            'total_value' => $order->actual_cost,
            'reason' => 'Production completion',
            'notes' => "Manufacturing Order: {$order->order_number}",
            'stock_before' => $stockBefore,
            'stock_after' => $stockAfter,
            'manufacturing_order_id' => $order->id,
            'user_id' => Auth::id(),
        ]);

        // Update product stock
        $product->update(['current_stock' => $stockAfter]);
    }

    /**
     * Cancel a manufacturing order.
     */
    public function cancel($id): JsonResponse
    {
        try {
            $order = ManufacturingOrder::findOrFail($id);

            if ($order->status === 'completed') {
                return response()->json([
                    'message' => 'Cannot cancel a completed order.'
                ], 422);
            }

            $order->update(['status' => 'cancelled']);
            return response()->json(['message' => 'Manufacturing order cancelled successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error cancelling manufacturing order'], 500);
        }
    }

    /**
     * Get order details with consumptions.
     */
    public function show($id): JsonResponse
    {
        $order = ManufacturingOrder::with([
            'finishedProduct.unit',
            'finishedProduct.recipes.rawMaterial',
            'items.finishedProduct.unit',
            'consumptions.rawMaterial.unit',
            'createdBy',
            'finishedProductMovements'
        ])->findOrFail($id);

        // Add computed properties
        $orderData = $order->toArray();
        $orderData['can_start'] = $order->canStart();
        $orderData['cannot_start_reason'] = $order->cannot_start_reason;
        $orderData['can_complete'] = $order->canComplete();
        $orderData['cannot_complete_reason'] = $order->cannot_complete_reason;

        return response()->json($orderData);
    }

    /**
     * Update material consumption for an order.
     */
    public function updateConsumption(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'consumptions' => 'required|array',
            'consumptions.*.id' => 'required|exists:manufacturing_consumptions,id',
            'consumptions.*.actual_quantity' => 'required|numeric|min:0',
            'consumptions.*.notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $order = ManufacturingOrder::findOrFail($id);

            if ($order->status !== 'in_progress') {
                return response()->json([
                    'message' => 'Can only update consumption for orders in progress.'
                ], 422);
            }

            foreach ($request->consumptions as $consumptionData) {
                $consumption = ManufacturingConsumption::findOrFail($consumptionData['id']);

                // Calculate total cost
                $totalCost = $consumptionData['actual_quantity'] * $consumption->unit_cost;

                $consumption->update([
                    'actual_quantity' => $consumptionData['actual_quantity'],
                    'total_cost' => $totalCost,
                    'notes' => $consumptionData['notes'] ?? null,
                    'consumed_at' => now(),
                ]);

                // Create raw material movement
                $this->createRawMaterialMovement($consumption);
            }

            DB::commit();
            return response()->json(['message' => 'Material consumption updated successfully']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error updating consumption'], 500);
        }
    }

    /**
     * Validate material availability for completion.
     */
    private function validateMaterialAvailability(ManufacturingOrder $order, $producedQuantity)
    {
        // Check if any consumption has been manually recorded
        $hasManualConsumption = $order->consumptions()
            ->where('actual_quantity', '>', 0)
            ->exists();

        // If manual consumption exists, no need to validate (materials already consumed)
        if ($hasManualConsumption) {
            return;
        }

        // Calculate required materials based on produced quantity
        $scaleFactor = $producedQuantity / $order->planned_quantity;
        $insufficientMaterials = [];

        foreach ($order->consumptions as $consumption) {
            $requiredQuantity = $consumption->planned_quantity * $scaleFactor;
            $material = $consumption->rawMaterial;

            if ($material->current_stock < $requiredQuantity) {
                $insufficientMaterials[] = [
                    'material' => $material->name,
                    'required' => $requiredQuantity,
                    'available' => $material->current_stock,
                    'unit' => $material->unit->symbol ?? ''
                ];
            }
        }

        if (!empty($insufficientMaterials)) {
            $errorMessage = "Insufficient materials to complete this order:\n";
            foreach ($insufficientMaterials as $material) {
                $errorMessage .= "- {$material['material']}: Required {$material['required']} {$material['unit']}, Available {$material['available']} {$material['unit']}\n";
            }

            throw new \Exception($errorMessage);
        }
    }

    /**
     * Auto-consume materials if no manual consumption has been recorded.
     */
    private function autoConsumeIfNeeded(ManufacturingOrder $order, $producedQuantity)
    {
        // Check if any consumption has been manually recorded
        $hasManualConsumption = $order->consumptions()
            ->where('actual_quantity', '>', 0)
            ->exists();

        // If manual consumption exists, don't auto-consume
        if ($hasManualConsumption) {
            return;
        }

        // Auto-consume based on recipe proportional to produced quantity
        $scaleFactor = $producedQuantity / $order->planned_quantity;

        foreach ($order->consumptions as $consumption) {
            // Calculate actual quantity based on produced quantity
            $actualQuantity = $consumption->planned_quantity * $scaleFactor;

            // Check if sufficient stock is available
            $material = $consumption->rawMaterial;
            if ($material->current_stock < $actualQuantity) {
                throw new \Exception("Insufficient stock for {$material->name}. Available: {$material->current_stock}, Required: {$actualQuantity}");
            }

            // Update consumption with actual quantities
            $consumption->update([
                'actual_quantity' => $actualQuantity,
                'total_cost' => $actualQuantity * $consumption->unit_cost,
                'consumed_at' => now(),
                'notes' => 'Auto-consumed on order completion',
                'recorded_by' => Auth::id(),
            ]);

            // Create raw material movement
            $this->createRawMaterialMovement($consumption);
        }
    }

    /**
     * Create raw material movement for consumption.
     */
    private function createRawMaterialMovement(ManufacturingConsumption $consumption)
    {
        $material = $consumption->rawMaterial;
        $stockBefore = $material->current_stock;
        $stockAfter = $stockBefore - $consumption->actual_quantity;

        RawMaterialMovement::create([
            'raw_material_id' => $material->id,
            'movement_type' => 'outbound',
            'quantity' => -$consumption->actual_quantity,
            'unit_price' => $consumption->unit_cost,
            'total_value' => $consumption->total_cost,
            'document_reference' => $consumption->manufacturingOrder->order_number,
            'reason' => 'Manufacturing consumption',
            'notes' => $consumption->notes,
            'stock_before' => $stockBefore,
            'stock_after' => $stockAfter,
            'user_id' => Auth::id(),
        ]);

        // Update material stock
        $material->update(['current_stock' => $stockAfter]);
    }

    /**
     * Validate material availability for a specific item in multi-product order.
     */
    private function validateMaterialAvailabilityForItem(ManufacturingOrder $order, ManufacturingOrderItem $item, $producedQuantity)
    {
        // Check if any consumption has been manually recorded
        $hasManualConsumption = $order->consumptions()
            ->where('actual_quantity', '>', 0)
            ->exists();

        // If manual consumption exists, no need to validate
        if ($hasManualConsumption) {
            return;
        }

        $product = $item->finishedProduct;
        $scaleFactor = $producedQuantity / $item->planned_quantity;
        $insufficientMaterials = [];

        foreach ($product->recipes as $recipe) {
            $requiredQuantity = $recipe->quantity_required * $item->planned_quantity * $scaleFactor;
            $material = $recipe->rawMaterial;

            if ($material->current_stock < $requiredQuantity) {
                $insufficientMaterials[] = [
                    'material' => $material->name,
                    'required' => $requiredQuantity,
                    'available' => $material->current_stock,
                    'unit' => $material->unit->symbol ?? '',
                    'product' => $product->name
                ];
            }
        }

        if (!empty($insufficientMaterials)) {
            $errorMessage = "Insufficient materials for {$product->name}:\n";
            foreach ($insufficientMaterials as $material) {
                $errorMessage .= "- {$material['material']}: Required {$material['required']} {$material['unit']}, Available {$material['available']} {$material['unit']}\n";
            }

            throw new \Exception($errorMessage);
        }
    }

    /**
     * Auto-consume materials for a specific item and return actual cost.
     */
    private function autoConsumeForItem(ManufacturingOrder $order, ManufacturingOrderItem $item, $producedQuantity): float
    {
        // Check if any consumption has been manually recorded
        $hasManualConsumption = $order->consumptions()
            ->where('actual_quantity', '>', 0)
            ->exists();

        // If manual consumption exists, calculate cost from existing consumptions
        if ($hasManualConsumption) {
            // Calculate proportional cost based on this item's production
            $totalPlannedQuantity = $order->items->sum('planned_quantity');
            $itemProportion = $item->planned_quantity / $totalPlannedQuantity;
            return $order->consumptions->sum('total_cost') * $itemProportion;
        }

        $product = $item->finishedProduct;
        $scaleFactor = $producedQuantity / $item->planned_quantity;
        $totalCost = 0;

        foreach ($product->recipes as $recipe) {
            $actualQuantity = $recipe->quantity_required * $item->planned_quantity * $scaleFactor;
            $unitCost = $recipe->rawMaterial->unit_price ?? 0;
            $cost = $actualQuantity * $unitCost;
            $totalCost += $cost;

            // Find or create consumption record
            $consumption = $order->consumptions()
                ->where('raw_material_id', $recipe->raw_material_id)
                ->first();

            if ($consumption) {
                // Update existing consumption
                $consumption->update([
                    'actual_quantity' => $consumption->actual_quantity + $actualQuantity,
                    'total_cost' => $consumption->total_cost + $cost,
                    'consumed_at' => now(),
                    'notes' => ($consumption->notes ? $consumption->notes . '; ' : '') . "Auto-consumed for {$product->name}",
                    'recorded_by' => Auth::id(),
                ]);
            } else {
                // This shouldn't happen if planned consumptions were created properly
                $consumption = ManufacturingConsumption::create([
                    'manufacturing_order_id' => $order->id,
                    'raw_material_id' => $recipe->raw_material_id,
                    'planned_quantity' => $actualQuantity,
                    'actual_quantity' => $actualQuantity,
                    'unit_cost' => $unitCost,
                    'total_cost' => $cost,
                    'consumed_at' => now(),
                    'notes' => "Auto-consumed for {$product->name}",
                    'recorded_by' => Auth::id(),
                ]);
            }

            // Create raw material movement
            $this->createRawMaterialMovement($consumption);
        }

        return $totalCost;
    }

    /**
     * Create finished product movement for a specific item.
     */
    private function createFinishedProductMovementForItem(ManufacturingOrder $order, ManufacturingOrderItem $item, $producedQuantity, $actualCost)
    {
        $product = $item->finishedProduct;
        $stockBefore = $product->current_stock;
        $stockAfter = $stockBefore + $producedQuantity;
        $unitCost = $actualCost > 0 ? $actualCost / $producedQuantity : 0;

        FinishedProductMovement::create([
            'finished_product_id' => $product->id,
            'movement_type' => 'production',
            'quantity' => $producedQuantity,
            'unit_cost' => $unitCost,
            'total_value' => $actualCost,
            'reason' => 'Production completion',
            'notes' => "Manufacturing Order: {$order->order_number} - {$product->name}",
            'stock_before' => $stockBefore,
            'stock_after' => $stockAfter,
            'manufacturing_order_id' => $order->id,
            'user_id' => Auth::id(),
        ]);

        // Update product stock
        $product->update(['current_stock' => $stockAfter]);
    }

    /**
     * Update order status (for Kanban drag and drop).
     */
    public function updateStatus($id, Request $request): JsonResponse
    {
        try {
            $order = ManufacturingOrder::findOrFail($id);
            $newStatus = $request->input('status');

            // Validate status transition
            $validTransitions = [
                'planned' => ['in_progress', 'cancelled'],
                'in_progress' => ['completed', 'cancelled'],
                'completed' => [], // No transitions from completed
                'cancelled' => [] // No transitions from cancelled
            ];

            if (!in_array($newStatus, $validTransitions[$order->status] ?? [])) {
                return response()->json([
                    'message' => 'Invalid status transition'
                ], 422);
            }

            // Handle specific status changes
            if ($newStatus === 'in_progress') {
                return $this->start($id);
            } elseif ($newStatus === 'completed') {
                // For completion, require additional data
                return response()->json([
                    'message' => 'Completion requires additional data. Please use the complete order form.'
                ], 422);
            } elseif ($newStatus === 'cancelled') {
                return $this->cancel($id);
            }

            return response()->json(['message' => 'Status updated successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error updating status'], 500);
        }
    }

    /**
     * Get orders by status for dashboard.
     */
    public function getOrdersByStatus(): JsonResponse
    {
        $orders = [
            'planned' => ManufacturingOrder::planned()->with(['finishedProduct.unit', 'createdBy'])->get(),
            'in_progress' => ManufacturingOrder::inProgress()->with(['finishedProduct.unit', 'createdBy'])->get(),
            'completed' => ManufacturingOrder::completed()->with(['finishedProduct.unit', 'createdBy'])->latest()->take(10)->get(),
            'overdue' => ManufacturingOrder::with(['finishedProduct.unit', 'createdBy'])->get()->filter(fn($order) => $order->isOverdue())->values(),
        ];

        // Add computed properties for each order
        foreach ($orders as $status => $statusOrders) {
            $orders[$status] = $statusOrders->map(function ($order) {
                $orderArray = $order->toArray();
                $orderArray['completion_percentage'] = $order->completion_percentage;
                $orderArray['is_overdue'] = $order->isOverdue();
                $orderArray['status_label'] = $order->status_label;
                return $orderArray;
            });
        }

        return response()->json($orders);
    }

    /**
     * Print manufacturing order.
     */
    public function print(ManufacturingOrder $order)
    {
        $order->load([
            'finishedProduct.unit',
            'items.finishedProduct.unit',
            'consumptions.rawMaterial.unit',
            'createdBy'
        ]);

        return view('pages.manufacturing-orders.print', compact('order'));
    }
}
