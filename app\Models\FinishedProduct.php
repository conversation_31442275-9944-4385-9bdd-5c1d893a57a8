<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class FinishedProduct extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'unit_id',
        'current_stock',
        'minimum_stock',
        'selling_price',
        'production_time_minutes',
        'image_path',
        'image_alt',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'current_stock' => 'decimal:3',
        'minimum_stock' => 'decimal:3',
        'selling_price' => 'decimal:2',
        'production_time_minutes' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the unit that this finished product uses.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Get the stock movements for this finished product.
     */
    public function movements(): HasMany
    {
        return $this->hasMany(FinishedProductMovement::class)->orderBy('movement_date', 'desc');
    }

    /**
     * Get the recipes for this finished product.
     */
    public function recipes(): HasMany
    {
        return $this->hasMany(Recipe::class)->orderBy('sort_order');
    }

    /**
     * Get the manufacturing orders for this finished product.
     */
    public function manufacturingOrders(): HasMany
    {
        return $this->hasMany(ManufacturingOrder::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get the sale items for this finished product.
     */
    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Scope a query to only include active finished products.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include low stock items.
     */
    public function scopeLowStock($query)
    {
        return $query->whereColumn('current_stock', '<=', 'minimum_stock');
    }

    /**
     * Check if the finished product is low on stock.
     */
    public function isLowStock(): bool
    {
        return $this->current_stock <= $this->minimum_stock;
    }

    /**
     * Get the stock status (normal, warning, critical).
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->current_stock <= 0) {
            return 'critical';
        } elseif ($this->current_stock <= $this->minimum_stock) {
            return 'warning';
        }
        return 'normal';
    }

    /**
     * Get the stock status color for UI.
     */
    public function getStockStatusColorAttribute(): string
    {
        return match ($this->stock_status) {
            'critical' => 'red',
            'warning' => 'orange',
            'normal' => 'green',
            default => 'gray',
        };
    }

    /**
     * Get the total value of current stock.
     */
    public function getStockValueAttribute(): float
    {
        return $this->current_stock * ($this->selling_price ?? 0);
    }

    /**
     * Calculate the total cost of raw materials needed for one unit.
     */
    public function getProductionCostAttribute(): float
    {
        return $this->recipes->sum(function ($recipe) {
            return $recipe->quantity_required * ($recipe->rawMaterial->unit_price ?? 0);
        });
    }

    /**
     * Calculate profit margin per unit.
     */
    public function getProfitMarginAttribute(): float
    {
        if (!$this->selling_price || $this->production_cost <= 0) {
            return 0;
        }
        return $this->selling_price - $this->production_cost;
    }

    /**
     * Check if enough raw materials are available to produce a given quantity.
     */
    public function canProduce(float $quantity = 1): bool
    {
        // If no recipes exist, cannot produce
        if ($this->recipes->isEmpty()) {
            return false;
        }

        foreach ($this->recipes as $recipe) {
            $requiredQuantity = $recipe->quantity_required * $quantity;
            if ($recipe->rawMaterial->current_stock < $requiredQuantity) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get the maximum quantity that can be produced with current stock.
     */
    public function getMaxProducibleQuantityAttribute(): float
    {
        if ($this->recipes->isEmpty()) {
            return 0;
        }

        $maxQuantities = [];
        foreach ($this->recipes as $recipe) {
            if ($recipe->quantity_required > 0) {
                $maxQuantities[] = floor($recipe->rawMaterial->current_stock / $recipe->quantity_required);
            }
        }

        return empty($maxQuantities) ? 0 : min($maxQuantities);
    }

    /**
     * Get the image URL for display.
     */
    public function getImageUrlAttribute(): string
    {
        if ($this->image_path && file_exists(public_path($this->image_path))) {
            return asset($this->image_path);
        }
        return asset('assets/img/default/product-placeholder.svg');
    }

    /**
     * Get the image alt text.
     */
    public function getImageAltTextAttribute(): string
    {
        return $this->image_alt ?: $this->name;
    }
}
