<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FinishedProduct;
use App\Models\RawMaterial;
use App\Models\Recipe;

class RecipesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get products and materials for reference
        $chocolateCake = FinishedProduct::where('name', 'Chocolate Cake')->first();
        $vanillaCake = FinishedProduct::where('name', 'Vanilla Birthday Cake')->first();
        $chocolateCupcakes = FinishedProduct::where('name', 'Chocolate Cupcakes')->first();
        $vanillaCupcakes = FinishedProduct::where('name', 'Vanilla Cupcakes')->first();
        $chocolateChipCookies = FinishedProduct::where('name', 'Chocolate Chip Cookies')->first();

        // Raw materials
        $flour = RawMaterial::where('name', 'All-Purpose Flour')->first();
        $sugar = RawMaterial::where('name', 'Granulated Sugar')->first();
        $brownSugar = RawMaterial::where('name', 'Brown Sugar')->first();
        $butter = RawMaterial::where('name', 'Butter')->first();
        $eggs = RawMaterial::where('name', 'Eggs')->first();
        $vanilla = RawMaterial::where('name', 'Vanilla Extract')->first();
        $bakingPowder = RawMaterial::where('name', 'Baking Powder')->first();
        $salt = RawMaterial::where('name', 'Salt')->first();
        $milk = RawMaterial::where('name', 'Whole Milk')->first();
        $cocoaPowder = RawMaterial::where('name', 'Cocoa Powder')->first();
        $darkChocolate = RawMaterial::where('name', 'Dark Chocolate')->first();
        $whiteChocolateChips = RawMaterial::where('name', 'White Chocolate Chips')->first();
        $powderedSugar = RawMaterial::where('name', 'Powdered Sugar')->first();
        $heavyCream = RawMaterial::where('name', 'Heavy Cream')->first();

        // Chocolate Cake Recipe
        if ($chocolateCake) {
            $recipes = [
                // Cake base
                [
                    'finished_product_id' => $chocolateCake->id,
                    'raw_material_id' => $flour->id,
                    'quantity_required' => 0.500, // 500g flour
                    'unit_id' => $flour->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateCake->id,
                    'raw_material_id' => $sugar->id,
                    'quantity_required' => 0.400, // 400g sugar
                    'unit_id' => $sugar->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateCake->id,
                    'raw_material_id' => $cocoaPowder->id,
                    'quantity_required' => 0.100, // 100g cocoa powder
                    'unit_id' => $cocoaPowder->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateCake->id,
                    'raw_material_id' => $butter->id,
                    'quantity_required' => 0.200, // 200g butter
                    'unit_id' => $butter->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateCake->id,
                    'raw_material_id' => $eggs->id,
                    'quantity_required' => 4.000, // 4 eggs
                    'unit_id' => $eggs->unit_id,
                ],
            ];

            foreach ($recipes as $recipe) {
                Recipe::create($recipe);
            }
        }

        // Vanilla Cupcakes Recipe (simplified)
        if ($vanillaCupcakes) {
            $recipes = [
                [
                    'finished_product_id' => $vanillaCupcakes->id,
                    'raw_material_id' => $flour->id,
                    'quantity_required' => 0.180, // 180g flour
                    'unit_id' => $flour->unit_id,
                ],
                [
                    'finished_product_id' => $vanillaCupcakes->id,
                    'raw_material_id' => $sugar->id,
                    'quantity_required' => 0.140, // 140g sugar
                    'unit_id' => $sugar->unit_id,
                ],
                [
                    'finished_product_id' => $vanillaCupcakes->id,
                    'raw_material_id' => $butter->id,
                    'quantity_required' => 0.110, // 110g butter
                    'unit_id' => $butter->unit_id,
                ],
                [
                    'finished_product_id' => $vanillaCupcakes->id,
                    'raw_material_id' => $eggs->id,
                    'quantity_required' => 2.000, // 2 eggs
                    'unit_id' => $eggs->unit_id,
                ],
            ];

            foreach ($recipes as $recipe) {
                Recipe::create($recipe);
            }
        }

        // Chocolate Chip Cookies Recipe (simplified)
        if ($chocolateChipCookies) {
            $recipes = [
                [
                    'finished_product_id' => $chocolateChipCookies->id,
                    'raw_material_id' => $flour->id,
                    'quantity_required' => 0.280, // 280g flour
                    'unit_id' => $flour->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateChipCookies->id,
                    'raw_material_id' => $brownSugar->id,
                    'quantity_required' => 0.150, // 150g brown sugar
                    'unit_id' => $brownSugar->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateChipCookies->id,
                    'raw_material_id' => $butter->id,
                    'quantity_required' => 0.200, // 200g butter
                    'unit_id' => $butter->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateChipCookies->id,
                    'raw_material_id' => $eggs->id,
                    'quantity_required' => 2.000, // 2 eggs
                    'unit_id' => $eggs->unit_id,
                ],
                [
                    'finished_product_id' => $chocolateChipCookies->id,
                    'raw_material_id' => $whiteChocolateChips->id,
                    'quantity_required' => 0.200, // 200g chocolate chips
                    'unit_id' => $whiteChocolateChips->unit_id,
                ],
            ];

            foreach ($recipes as $recipe) {
                Recipe::create($recipe);
            }
        }
    }
}
