<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\UnitCategory;
use App\Models\Unit;
use Illuminate\Support\Facades\Validator;

class UnitCategoryController extends Controller
{
    /**
     * Display the unit categories management page.
     */
    public function index()
    {
        $categories = UnitCategory::with('units')->get();
        $totalCategories = $categories->count();
        $activeCategories = UnitCategory::active()->count();
        $inactiveCategories = $totalCategories - $activeCategories;
        $totalUnits = Unit::count();

        return view('pages.unit-categories.index', [
            'totalCategories' => $totalCategories,
            'activeCategories' => $activeCategories,
            'inactiveCategories' => $inactiveCategories,
            'totalUnits' => $totalUnits,
        ]);
    }

    /**
     * Get unit categories data for DataTable.
     */
    public function getData(Request $request): JsonResponse
    {
        $columns = [
            1 => 'id',
            2 => 'name',
            3 => 'description',
            4 => 'is_active',
        ];

        $totalData = UnitCategory::count();
        $totalFiltered = $totalData;

        $limit = $request->input('length');
        $start = $request->input('start');
        $order = $columns[$request->input('order.0.column')] ?? 'id';
        $dir = $request->input('order.0.dir');

        if (empty($request->input('search.value'))) {
            $categories = UnitCategory::with(['units', 'baseUnit'])
                ->offset($start)
                ->limit($limit)
                ->orderBy($order, $dir)
                ->get();
        } else {
            $search = $request->input('search.value');

            $categories = UnitCategory::with(['units', 'baseUnit'])
                ->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                })
                ->offset($start)
                ->limit($limit)
                ->orderBy($order, $dir)
                ->get();

            $totalFiltered = UnitCategory::where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                })
                ->count();
        }

        $data = [];

        if (!empty($categories)) {
            $ids = $start;

            foreach ($categories as $category) {
                $nestedData['id'] = $category->id;
                $nestedData['fake_id'] = ++$ids;
                $nestedData['name'] = $category->name;
                $nestedData['description'] = $category->description;
                $nestedData['base_unit'] = $category->baseUnit;
                $nestedData['base_unit_name'] = $category->baseUnit ? $category->baseUnit->display_name : 'No Base Unit';
                $nestedData['units_count'] = $category->units->count();
                $nestedData['active_units_count'] = $category->activeUnits->count();
                $nestedData['is_active'] = $category->is_active;

                $data[] = $nestedData;
            }
        }

        return response()->json([
            'draw' => intval($request->input('draw')),
            'recordsTotal' => intval($totalData),
            'recordsFiltered' => intval($totalFiltered),
            'code' => 200,
            'data' => $data,
        ]);
    }

    /**
     * Store a newly created unit category or update existing one.
     */
    public function store(Request $request): JsonResponse
    {
        // Convert checkbox value to boolean
        $requestData = $request->all();
        if (isset($requestData['is_active'])) {
            $requestData['is_active'] = in_array($requestData['is_active'], ['on', '1', 'true', true, 1], true);
        } else {
            $requestData['is_active'] = false;
        }

        $validator = Validator::make($requestData, [
            'name' => 'required|string|max:50|unique:unit_categories,name,' . $request->id,
            'description' => 'nullable|string',
            'base_unit_id' => 'nullable|exists:units,id',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $categoryId = $request->id;

        if ($categoryId) {
            // Update existing category
            $category = UnitCategory::findOrFail($categoryId);
            $category->update([
                'name' => $requestData['name'],
                'description' => $requestData['description'],
                'base_unit_id' => $requestData['base_unit_id'],
                'is_active' => $requestData['is_active'],
            ]);

            return response()->json(['message' => 'Unit category updated successfully']);
        } else {
            // Create new category
            UnitCategory::create([
                'name' => $requestData['name'],
                'description' => $requestData['description'],
                'base_unit_id' => $requestData['base_unit_id'],
                'is_active' => $requestData['is_active'],
            ]);

            return response()->json(['message' => 'Unit category created successfully']);
        }
    }

    /**
     * Show the form for editing the specified unit category.
     */
    public function edit($id): JsonResponse
    {
        $category = UnitCategory::with(['units', 'baseUnit'])->findOrFail($id);
        return response()->json($category);
    }

    /**
     * Remove the specified unit category from storage.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $category = UnitCategory::findOrFail($id);

            // Check if category has units
            if ($category->units()->count() > 0) {
                return response()->json([
                    'message' => 'Cannot delete category. It has units assigned to it.'
                ], 422);
            }

            $category->delete();
            return response()->json(['message' => 'Unit category deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error deleting unit category'], 500);
        }
    }

    /**
     * Get active unit categories for dropdowns.
     */
    public function getActiveCategories(): JsonResponse
    {
        $categories = UnitCategory::active()->orderBy('name')->get(['id', 'name', 'description']);
        return response()->json($categories);
    }

    /**
     * Get units for a specific category.
     */
    public function getCategoryUnits($id): JsonResponse
    {
        $category = UnitCategory::findOrFail($id);
        $units = $category->activeUnits()->orderBy('name')->get(['id', 'name', 'symbol', 'conversion_factor', 'is_base_unit']);
        
        return response()->json([
            'category' => $category,
            'units' => $units
        ]);
    }
}
