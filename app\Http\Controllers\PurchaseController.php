<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Purchase;
use App\Models\PurchaseItem;
use App\Models\RawMaterial;
use App\Models\RawMaterialMovement;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class PurchaseController extends Controller
{
    /**
     * Display the purchases management page.
     */
    public function index()
    {
        try {
            $purchases = Purchase::with(['createdBy'])->get();
            $totalPurchases = $purchases->count();
            $pendingPurchases = Purchase::pending()->count();
            $orderedPurchases = Purchase::ordered()->count();
            $receivedPurchases = Purchase::received()->count();
            $overduePurchases = $purchases->filter(fn($purchase) => $purchase->isOverdue())->count();

            return view('pages.purchases.index', [
                'totalPurchases' => $totalPurchases,
                'pendingPurchases' => $pendingPurchases,
                'orderedPurchases' => $orderedPurchases,
                'receivedPurchases' => $receivedPurchases,
                'overduePurchases' => $overduePurchases,
            ]);
        } catch (\Exception $e) {
            \Log::error('Purchase index error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get purchases data for DataTable.
     */
    public function getData(Request $request): JsonResponse
    {
        $columns = [
            1 => 'id',
            2 => 'purchase_number',
            3 => 'supplier_name',
            4 => 'purchase_date',
            5 => 'expected_delivery_date',
            6 => 'total_amount',
            7 => 'status',
            8 => 'created_at',
        ];

        $totalData = Purchase::count();
        $totalFiltered = $totalData;

        $limit = $request->input('length');
        $start = $request->input('start');
        $order = $columns[$request->input('order.0.column')] ?? 'id';
        $dir = $request->input('order.0.dir');

        $query = Purchase::with(['createdBy', 'items']);

        if (!empty($request->input('search.value'))) {
            $search = $request->input('search.value');

            $query->where(function($q) use ($search) {
                $q->where('purchase_number', 'LIKE', "%{$search}%")
                  ->orWhere('supplier_name', 'LIKE', "%{$search}%")
                  ->orWhere('supplier_contact', 'LIKE', "%{$search}%")
                  ->orWhere('notes', 'LIKE', "%{$search}%")
                  ->orWhereHas('createdBy', function($userQuery) use ($search) {
                      $userQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });

            $totalFiltered = $query->count();
        }

        $purchases = $query->offset($start)
            ->limit($limit)
            ->orderBy($order, $dir)
            ->get();

        $data = [];

        if (!empty($purchases)) {
            $ids = $start;

            foreach ($purchases as $purchase) {
                $nestedData['id'] = $purchase->id;
                $nestedData['fake_id'] = ++$ids;
                $nestedData['purchase_number'] = $purchase->purchase_number;
                $nestedData['supplier_name'] = $purchase->supplier_name;
                $nestedData['supplier_contact'] = $purchase->supplier_contact;
                $nestedData['supplier_email'] = $purchase->supplier_email;
                $nestedData['purchase_date'] = $purchase->purchase_date;
                $nestedData['expected_delivery_date'] = $purchase->expected_delivery_date;
                $nestedData['actual_delivery_date'] = $purchase->actual_delivery_date;
                $nestedData['subtotal'] = $purchase->subtotal;
                $nestedData['delivery_fee'] = $purchase->delivery_fee;
                $nestedData['handling_fee'] = $purchase->handling_fee;
                $nestedData['other_fees'] = $purchase->other_fees;
                $nestedData['total_fees'] = $purchase->total_fees;
                $nestedData['total_amount'] = $purchase->total_amount;
                $nestedData['status'] = $purchase->status;
                $nestedData['status_label'] = $purchase->status_label;
                $nestedData['status_color'] = $purchase->status_color;
                $nestedData['notes'] = $purchase->notes;
                $nestedData['created_by'] = $purchase->createdBy;
                $nestedData['received_by'] = $purchase->receivedBy;
                $nestedData['is_overdue'] = $purchase->isOverdue();
                $nestedData['can_receive'] = $purchase->canReceive();
                $nestedData['can_complete'] = $purchase->canComplete();
                $nestedData['can_cancel'] = $purchase->canCancel();
                $nestedData['total_items'] = $purchase->items->count();
                $nestedData['formatted_purchase_date'] = $purchase->formatted_purchase_date;
                $nestedData['formatted_expected_delivery'] = $purchase->formatted_expected_delivery;

                $data[] = $nestedData;
            }
        }

        return response()->json([
            'draw' => intval($request->input('draw')),
            'recordsTotal' => intval($totalData),
            'recordsFiltered' => intval($totalFiltered),
            'code' => 200,
            'data' => $data,
        ]);
    }

    /**
     * Store a newly created purchase or update existing one.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'supplier_name' => 'required|string|max:255',
            'supplier_contact' => 'nullable|string|max:50',
            'supplier_email' => 'nullable|email|max:255',
            'supplier_address' => 'nullable|string',
            'purchase_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after_or_equal:purchase_date',
            'delivery_fee' => 'nullable|numeric|min:0',
            'handling_fee' => 'nullable|numeric|min:0',
            'other_fees' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.raw_material_id' => 'required|exists:raw_materials,id',
            'items.*.quantity' => 'required|numeric|min:0.001|regex:/^\d+(\.\d{1,3})?$/',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $purchaseId = $request->id;

        try {
            DB::beginTransaction();

            // Calculate totals
            $subtotal = 0;
            foreach ($request->items as $item) {
                $subtotal += $item['quantity'] * $item['unit_price'];
            }

            $deliveryFee = $request->delivery_fee ?? 0;
            $handlingFee = $request->handling_fee ?? 0;
            $otherFees = $request->other_fees ?? 0;
            $totalAmount = $subtotal + $deliveryFee + $handlingFee + $otherFees;

            if ($purchaseId) {
                // Update existing purchase (only if status is pending)
                $purchase = Purchase::findOrFail($purchaseId);

                if ($purchase->status !== 'pending') {
                    return response()->json([
                        'message' => 'Cannot edit purchase that is not in pending status.'
                    ], 422);
                }

                $purchase->update([
                    'supplier_name' => $request->supplier_name,
                    'supplier_contact' => $request->supplier_contact,
                    'supplier_email' => $request->supplier_email,
                    'supplier_address' => $request->supplier_address,
                    'purchase_date' => $request->purchase_date,
                    'expected_delivery_date' => $request->expected_delivery_date,
                    'subtotal' => $subtotal,
                    'delivery_fee' => $deliveryFee,
                    'handling_fee' => $handlingFee,
                    'other_fees' => $otherFees,
                    'total_amount' => $totalAmount,
                    'notes' => $request->notes,
                ]);

                // Delete existing items and recreate
                $purchase->items()->delete();
            } else {
                // Create new purchase
                $purchase = Purchase::create([
                    'supplier_name' => $request->supplier_name,
                    'supplier_contact' => $request->supplier_contact,
                    'supplier_email' => $request->supplier_email,
                    'supplier_address' => $request->supplier_address,
                    'purchase_date' => $request->purchase_date,
                    'expected_delivery_date' => $request->expected_delivery_date,
                    'subtotal' => $subtotal,
                    'delivery_fee' => $deliveryFee,
                    'handling_fee' => $handlingFee,
                    'other_fees' => $otherFees,
                    'total_amount' => $totalAmount,
                    'notes' => $request->notes,
                    'created_by' => Auth::id(),
                ]);
            }

            // Create purchase items
            foreach ($request->items as $item) {
                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'raw_material_id' => $item['raw_material_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'notes' => $item['notes'] ?? null,
                ]);
            }

            DB::commit();

            $message = $purchaseId ? 'Purchase updated successfully' : 'Purchase created successfully';
            return response()->json(['message' => $message]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error saving purchase'], 500);
        }
    }

    /**
     * Show the form for editing the specified purchase.
     */
    public function edit($id): JsonResponse
    {
        $purchase = Purchase::with(['items.rawMaterial.unit', 'createdBy'])->findOrFail($id);
        return response()->json($purchase);
    }

    /**
     * Remove the specified purchase from storage.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $purchase = Purchase::findOrFail($id);

            // Can only delete pending purchases
            if ($purchase->status !== 'pending') {
                return response()->json([
                    'message' => 'Cannot delete purchase that is not in pending status.'
                ], 422);
            }

            $purchase->delete();
            return response()->json(['message' => 'Purchase deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error deleting purchase'], 500);
        }
    }

    /**
     * Update purchase status.
     */
    public function updateStatus(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,ordered,shipped,received,completed,cancelled',
            'actual_delivery_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $purchase = Purchase::findOrFail($id);

            $updateData = [
                'status' => $request->status,
                'notes' => $request->notes,
            ];

            if ($request->status === 'received' && $request->actual_delivery_date) {
                $updateData['actual_delivery_date'] = $request->actual_delivery_date;
                $updateData['received_by'] = Auth::id();
            }

            $purchase->update($updateData);

            return response()->json(['message' => 'Purchase status updated successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error updating purchase status'], 500);
        }
    }

    /**
     * Receive purchase items.
     */
    public function receiveItems(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.id' => 'required|exists:purchase_items,id',
            'items.*.received_quantity' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string',
            'actual_delivery_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $purchase = Purchase::findOrFail($id);

            if (!$purchase->canReceive()) {
                return response()->json([
                    'message' => 'Cannot receive items for this purchase.'
                ], 422);
            }

            foreach ($request->items as $itemData) {
                $purchaseItem = PurchaseItem::findOrFail($itemData['id']);

                $purchaseItem->update([
                    'received_quantity' => $itemData['received_quantity'],
                    'notes' => $itemData['notes'] ?? null,
                ]);

                // Create raw material movement if quantity received
                if ($itemData['received_quantity'] > 0) {
                    $this->createRawMaterialMovement($purchaseItem, $itemData['received_quantity']);
                }
            }

            // Update purchase status and delivery date
            $updateData = ['status' => 'received', 'received_by' => Auth::id()];
            if ($request->actual_delivery_date) {
                $updateData['actual_delivery_date'] = $request->actual_delivery_date;
            }
            $purchase->update($updateData);

            DB::commit();
            return response()->json(['message' => 'Items received successfully']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error receiving items'], 500);
        }
    }

    /**
     * Create raw material movement for received items.
     */
    private function createRawMaterialMovement(PurchaseItem $purchaseItem, $receivedQuantity)
    {
        $material = $purchaseItem->rawMaterial;
        $stockBefore = $material->current_stock;
        $stockAfter = $stockBefore + $receivedQuantity;

        RawMaterialMovement::create([
            'raw_material_id' => $material->id,
            'movement_type' => 'inbound',
            'quantity' => $receivedQuantity,
            'unit_price' => $purchaseItem->unit_price,
            'total_value' => $receivedQuantity * $purchaseItem->unit_price,
            'document_reference' => $purchaseItem->purchase->purchase_number,
            'reason' => 'Purchase receipt',
            'notes' => $purchaseItem->notes,
            'stock_before' => $stockBefore,
            'stock_after' => $stockAfter,
            'user_id' => Auth::id(),
        ]);

        // Update material stock and price
        $material->update([
            'current_stock' => $stockAfter,
            'unit_price' => $purchaseItem->unit_price, // Update with latest purchase price
        ]);
    }

    /**
     * Complete a purchase.
     */
    public function complete($id): JsonResponse
    {
        try {
            $purchase = Purchase::findOrFail($id);

            if (!$purchase->canComplete()) {
                return response()->json([
                    'message' => 'Cannot complete this purchase.'
                ], 422);
            }

            $purchase->update(['status' => 'completed']);
            return response()->json(['message' => 'Purchase completed successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error completing purchase'], 500);
        }
    }

    /**
     * Cancel a purchase.
     */
    public function cancel($id): JsonResponse
    {
        try {
            $purchase = Purchase::findOrFail($id);

            if (!$purchase->canCancel()) {
                return response()->json([
                    'message' => 'Cannot cancel this purchase.'
                ], 422);
            }

            $purchase->update(['status' => 'cancelled']);
            return response()->json(['message' => 'Purchase cancelled successfully']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error cancelling purchase'], 500);
        }
    }

    /**
     * Get purchase details with items.
     */
    public function show($id): JsonResponse
    {
        $purchase = Purchase::with([
            'items.rawMaterial.unit',
            'createdBy',
            'receivedBy'
        ])->findOrFail($id);

        return response()->json($purchase);
    }

    /**
     * Get active raw materials for purchase form.
     */
    public function getActiveRawMaterials(): JsonResponse
    {
        $materials = RawMaterial::with('unit')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return response()->json($materials);
    }

    /**
     * Get supplier suggestions based on previous purchases.
     */
    public function getSupplierSuggestions(Request $request): JsonResponse
    {
        $search = $request->input('search', '');

        $suppliers = Purchase::select('supplier_name', 'supplier_contact', 'supplier_email', 'supplier_address')
            ->where('supplier_name', 'LIKE', "%{$search}%")
            ->groupBy('supplier_name', 'supplier_contact', 'supplier_email', 'supplier_address')
            ->orderBy('supplier_name')
            ->limit(10)
            ->get();

        return response()->json($suppliers);
    }

    /**
     * Get purchases by status for dashboard.
     */
    public function getPurchasesByStatus(): JsonResponse
    {
        $purchases = [
            'pending' => Purchase::pending()->with('items')->get(),
            'ordered' => Purchase::ordered()->with('items')->get(),
            'shipped' => Purchase::shipped()->with('items')->get(),
            'received' => Purchase::received()->with('items')->get(),
            'overdue' => Purchase::with('items')->get()->filter(fn($purchase) => $purchase->isOverdue())->values(),
        ];

        return response()->json($purchases);
    }

    /**
     * Get purchase statistics.
     */
    public function getStatistics(): JsonResponse
    {
        $stats = [
            'total_purchases' => Purchase::count(),
            'pending_purchases' => Purchase::pending()->count(),
            'ordered_purchases' => Purchase::ordered()->count(),
            'received_purchases' => Purchase::received()->count(),
            'completed_purchases' => Purchase::completed()->count(),
            'total_amount_this_month' => Purchase::whereMonth('purchase_date', now()->month)
                ->whereYear('purchase_date', now()->year)
                ->sum('total_amount'),
            'average_delivery_time' => $this->calculateAverageDeliveryTime(),
            'top_suppliers' => $this->getTopSuppliers(),
        ];

        return response()->json($stats);
    }

    /**
     * Calculate average delivery time in days.
     */
    private function calculateAverageDeliveryTime(): float
    {
        $completedPurchases = Purchase::whereNotNull('actual_delivery_date')
            ->whereNotNull('expected_delivery_date')
            ->get();

        if ($completedPurchases->isEmpty()) {
            return 0;
        }

        $totalDays = $completedPurchases->sum(function($purchase) {
            return $purchase->expected_delivery_date->diffInDays($purchase->actual_delivery_date, false);
        });

        return round($totalDays / $completedPurchases->count(), 1);
    }

    /**
     * Get top suppliers by purchase amount.
     */
    private function getTopSuppliers(): array
    {
        return Purchase::select('supplier_name')
            ->selectRaw('COUNT(*) as purchase_count')
            ->selectRaw('SUM(total_amount) as total_amount')
            ->groupBy('supplier_name')
            ->orderByDesc('total_amount')
            ->limit(5)
            ->get()
            ->toArray();
    }
}
