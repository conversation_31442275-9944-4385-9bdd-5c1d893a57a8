/**
 * Global Currency Formatting
 * This file provides global currency formatting functionality across all pages
 */

'use strict';

$(function () {
  // Global currency data
  let globalCurrency = {
    currency_symbol: '$',
    currency_position: 'prefix',
    decimal_places: 2
  };

  // Load currency settings on page load
  loadGlobalCurrency();

  /**
   * Load currency settings from API
   */
  function loadGlobalCurrency() {
    $.get('/currency-settings/api/active')
      .done(function(currency) {
        globalCurrency = currency;
        console.log('Global currency loaded:', globalCurrency);
        
        // Trigger custom event to notify other scripts
        $(document).trigger('currencyLoaded', [globalCurrency]);
      })
      .fail(function() {
        console.warn('Failed to load global currency settings, using defaults');
        // Trigger event even with defaults
        $(document).trigger('currencyLoaded', [globalCurrency]);
      });
  }

  /**
   * Global currency formatting function
   */
  window.formatCurrency = function(amount, currencyData = null) {
    const currency = currencyData || globalCurrency;
    
    // Validate amount
    const numericAmount = parseFloat(amount || 0);
    if (isNaN(numericAmount)) {
      console.warn('Invalid amount provided to formatCurrency:', amount);
      return currency.currency_symbol + '0.00';
    }

    const formattedAmount = numericAmount.toFixed(currency.decimal_places || 2);

    if (currency.currency_position === 'prefix') {
      return currency.currency_symbol + formattedAmount;
    } else {
      return formattedAmount + currency.currency_symbol;
    }
  };

  /**
   * Get current currency data
   */
  window.getCurrentCurrency = function() {
    return globalCurrency;
  };

  /**
   * Refresh currency settings
   */
  window.refreshCurrency = function() {
    loadGlobalCurrency();
  };

  // Export for use in other modules
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
      formatCurrency: window.formatCurrency,
      getCurrentCurrency: window.getCurrentCurrency,
      refreshCurrency: window.refreshCurrency
    };
  }
});
