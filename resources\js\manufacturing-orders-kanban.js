/**
 * Manufacturing Orders Kanban
 */

'use strict';

(async function () {
  let boards;
  const kanbanSidebar = document.querySelector('.kanban-update-item-sidebar'),
    kanbanWrapper = document.querySelector('.kanban-wrapper'),
    baseUrl = window.location.origin + '/';

  // Init kanban Offcanvas
  const kanbanOffcanvas = new bootstrap.Offcanvas(kanbanSidebar);

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Get manufacturing orders data
  async function loadKanbanData() {
    try {
      const response = await fetch(baseUrl + 'manufacturing-orders/status/all');
      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }
      const ordersData = await response.json();

      // Transform data into kanban format
      boards = [
        {
          id: 'planned',
          title: __('Planned'),
          class: 'info',
          item: ordersData.planned.map(order => transformOrderToKanbanItem(order, 'planned'))
        },
        {
          id: 'in_progress',
          title: __('In Progress'),
          class: 'warning',
          item: ordersData.in_progress.map(order => transformOrderToKanbanItem(order, 'in_progress'))
        },
        {
          id: 'completed',
          title: __('Completed'),
          class: 'success',
          item: ordersData.completed.map(order => transformOrderToKanbanItem(order, 'completed'))
        }
      ];

      initKanban();
    } catch (error) {
      console.error('Error loading kanban data:', error);
      Swal.fire({
        icon: 'error',
        title: __('Error!'),
        text: __('Failed to load orders data'),
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    }
  }

  // Transform order data to kanban item format
  function transformOrderToKanbanItem(order, status) {
    const statusColors = {
      'planned': 'info',
      'in_progress': 'warning',
      'completed': 'success',
      'cancelled': 'danger'
    };

    const priorityColor = order.is_overdue ? 'danger' : statusColors[status];
    const dueDate = new Date(order.planned_date).toLocaleDateString();

    return {
      id: `order-${order.id}`,
      title: `
        <div class="d-flex justify-content-between align-items-start mb-2">
          <div class="d-flex flex-column">
            <h6 class="mb-1">${order.order_number}</h6>
            <small class="text-muted">${order.finished_product.name}</small>
          </div>
          <div class="dropdown kanban-tasks-item-dropdown">
            <i class="dropdown-toggle ri-more-2-line" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></i>
            <div class="dropdown-menu dropdown-menu-end">
              <a class="dropdown-item view-details" href="javascript:void(0)" data-id="${order.id}">
                <i class="ri-eye-line me-2"></i>${__('View Details')}
              </a>
              ${status === 'planned' ? `
                <a class="dropdown-item start-order" href="javascript:void(0)" data-id="${order.id}">
                  <i class="ri-play-line me-2"></i>${__('Start Order')}
                </a>
              ` : ''}
              ${status === 'in_progress' ? `
                <a class="dropdown-item complete-order" href="javascript:void(0)" data-id="${order.id}">
                  <i class="ri-check-line me-2"></i>${__('Complete Order')}
                </a>
              ` : ''}
              <a class="dropdown-item edit-order" href="javascript:void(0)" data-id="${order.id}">
                <i class="ri-edit-line me-2"></i>${__('Edit')}
              </a>
              ${status !== 'completed' ? `
                <a class="dropdown-item cancel-order text-danger" href="javascript:void(0)" data-id="${order.id}">
                  <i class="ri-close-line me-2"></i>${__('Cancel')}
                </a>
              ` : ''}
            </div>
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span class="badge bg-label-${priorityColor}">${order.planned_quantity} ${order.finished_product.unit ? order.finished_product.unit.symbol : ''}</span>
          <small class="text-muted">${dueDate}</small>
        </div>
        ${order.responsible_person ? `
          <div class="d-flex align-items-center">
            <i class="ri-user-line me-1"></i>
            <small class="text-muted">${order.responsible_person}</small>
          </div>
        ` : ''}
        ${order.completion_percentage > 0 ? `
          <div class="progress mt-2" style="height: 4px;">
            <div class="progress-bar bg-${statusColors[status]}" style="width: ${order.completion_percentage}%"></div>
          </div>
        ` : ''}
      `,
      class: order.is_overdue ? 'border-danger' : ''
    };
  }

  // Initialize kanban
  function initKanban() {
    if (!boards || boards.length === 0) return;

    const kanban = new jKanban({
      element: '.kanban-wrapper',
      gutter: '12px',
      widthBoard: '300px',
      dragItems: true,
      boards: boards,
      dragBoards: false,
      addItemButton: false,
      click: function (el) {
        const orderId = el.getAttribute('id').replace('order-', '');
        showOrderDetails(orderId);
      },
      dragendBoard: function (el) {
        // Handle board reordering if needed
      },
      dragend: function (el) {
        // Handle order status change when dragged between boards
        const orderId = el.getAttribute('id').replace('order-', '');
        const newStatus = el.closest('.kanban-board').getAttribute('data-id');
        updateOrderStatus(orderId, newStatus);
      }
    });

    // Kanban Wrapper scrollbar
    if (kanbanWrapper) {
      new PerfectScrollbar(kanbanWrapper);
    }

    // Prevent sidebar opening on dropdown clicks
    const tasksItemDropdown = document.querySelectorAll('.kanban-tasks-item-dropdown');
    tasksItemDropdown.forEach(function (dropdown) {
      dropdown.addEventListener('click', function (e) {
        e.stopPropagation();
      });
    });
  }

  // Show order details in sidebar
  function showOrderDetails(orderId) {
    $.get(`${baseUrl}manufacturing-orders/${orderId}`)
      .done(function (data) {
        // Build order details HTML (reuse from existing code)
        const html = buildOrderDetailsHTML(data);
        $('#order-details-content').html(html);

        // Add action buttons based on order status
        const actionButtons = buildActionButtons(data);
        $('#order-actions').html(actionButtons);

        // Load consumption data if order is in progress
        if (data.status === 'in_progress') {
          loadConsumptionData(orderId);
        }

        kanbanOffcanvas.show();
      })
      .fail(function (xhr) {
        const response = xhr.responseJSON;
        Swal.fire({
          icon: 'error',
          title: __('Error!'),
          text: response?.message || __('Error loading order details'),
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      });
  }

  // Update order status when dragged
  function updateOrderStatus(orderId, newStatus) {
    const statusMap = {
      'planned': 'planned',
      'in_progress': 'in_progress',
      'completed': 'completed'
    };

    const mappedStatus = statusMap[newStatus];
    if (!mappedStatus) return;

    // Handle status transitions
    if (mappedStatus === 'in_progress') {
      // Start the order
      $.post(`${baseUrl}manufacturing-orders/${orderId}/start`)
        .done(function (response) {
          showSuccessMessage(response.message || __('Order started successfully'));
        })
        .fail(function (xhr) {
          const response = xhr.responseJSON;
          showErrorMessage(response?.message || __('Error starting order'));
          // Reload kanban to revert changes
          loadKanbanData();
        });
    } else if (mappedStatus === 'completed') {
      // Show complete order modal instead of direct completion
      showCompleteOrderModal(orderId);
      // Reload kanban to revert the drag
      loadKanbanData();
    }
  }

  // Event handlers for dropdown actions
  $(document).on('click', '.view-details', function (e) {
    e.preventDefault();
    const orderId = $(this).data('id');
    showOrderDetails(orderId);
  });

  $(document).on('click', '.start-order', function (e) {
    e.preventDefault();
    const orderId = $(this).data('id');

    $.post(`${baseUrl}manufacturing-orders/${orderId}/start`)
      .done(function (response) {
        showSuccessMessage(response.message || __('Order started successfully'));
        loadKanbanData(); // Reload kanban
      })
      .fail(function (xhr) {
        const response = xhr.responseJSON;
        showErrorMessage(response?.message || __('Error starting order'));
      });
  });

  $(document).on('click', '.complete-order', function (e) {
    e.preventDefault();
    const orderId = $(this).data('id');
    showCompleteOrderModal(orderId);
  });

  $(document).on('click', '.edit-order', function (e) {
    e.preventDefault();
    const orderId = $(this).data('id');
    // Trigger edit from existing modal functionality
    $(`[data-id="${orderId}"].edit-record`).trigger('click');
  });

  $(document).on('click', '.cancel-order', function (e) {
    e.preventDefault();
    const orderId = $(this).data('id');

    Swal.fire({
      title: __('Are you sure?'),
      text: __('This will cancel the manufacturing order'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: __('Yes, cancel it!'),
      customClass: {
        confirmButton: 'btn btn-danger me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        $.post(`${baseUrl}manufacturing-orders/${orderId}/cancel`)
          .done(function (response) {
            showSuccessMessage(response.message || __('Order cancelled successfully'));
            loadKanbanData(); // Reload kanban
          })
          .fail(function (xhr) {
            const response = xhr.responseJSON;
            showErrorMessage(response?.message || __('Error cancelling order'));
          });
      }
    });
  });

  // Helper functions
  function showSuccessMessage(message) {
    Swal.fire({
      icon: 'success',
      title: __('Success!'),
      text: message,
      customClass: {
        confirmButton: 'btn btn-success'
      }
    });
  }

  function showErrorMessage(message) {
    Swal.fire({
      icon: 'error',
      title: __('Error!'),
      text: message,
      customClass: {
        confirmButton: 'btn btn-danger'
      }
    });
  }

  function showCompleteOrderModal(orderId) {
    $('#complete-order-id').val(orderId);
    $('#completeOrderModal').modal('show');
  }

  // Build order details HTML (reuse from existing manufacturing-orders-management.js)
  function buildOrderDetailsHTML(order) {
    const statusColors = {
      'planned': 'info',
      'in_progress': 'warning',
      'completed': 'success',
      'cancelled': 'danger'
    };

    const statusColor = statusColors[order.status] || 'secondary';
    const completionPercentage = order.completion_percentage || 0;
    const estimatedCost = order.estimated_cost ? (window.formatCurrency ? window.formatCurrency(order.estimated_cost) : '$' + parseFloat(order.estimated_cost).toFixed(2)) : 'N/A';
    const actualCost = order.actual_cost ? (window.formatCurrency ? window.formatCurrency(order.actual_cost) : '$' + parseFloat(order.actual_cost).toFixed(2)) : 'N/A';

    return `
      <div class="mb-4">
        <h6 class="mb-3">${__('Order Information')}</h6>
        <div class="row g-3">
          <div class="col-12">
            <label class="form-label">${__('Order Number')}</label>
            <p class="form-control-static">${order.order_number}</p>
          </div>
          <div class="col-12">
            <label class="form-label">${__('Product')}</label>
            <p class="form-control-static">${order.finished_product.name}</p>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Planned Quantity')}</label>
            <p class="form-control-static">${order.planned_quantity} ${order.finished_product.unit ? order.finished_product.unit.symbol : ''}</p>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Produced Quantity')}</label>
            <p class="form-control-static">${order.produced_quantity || 0} ${order.finished_product.unit ? order.finished_product.unit.symbol : ''}</p>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Status')}</label>
            <p class="form-control-static">
              <span class="badge bg-label-${statusColor}">${order.status_label || order.status}</span>
            </p>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Progress')}</label>
            <div class="progress mt-1" style="height: 8px;">
              <div class="progress-bar bg-${statusColor}" style="width: ${completionPercentage}%"></div>
            </div>
            <small class="text-muted">${completionPercentage}% ${__('completed')}</small>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Planned Date')}</label>
            <p class="form-control-static">${order.planned_date}</p>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Responsible Person')}</label>
            <p class="form-control-static">${order.responsible_person || 'N/A'}</p>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Estimated Cost')}</label>
            <p class="form-control-static">${estimatedCost}</p>
          </div>
          <div class="col-6">
            <label class="form-label">${__('Actual Cost')}</label>
            <p class="form-control-static">${actualCost}</p>
          </div>
          ${order.notes ? `
            <div class="col-12">
              <label class="form-label">${__('Notes')}</label>
              <p class="form-control-static">${order.notes}</p>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  // Build action buttons based on order status
  function buildActionButtons(order) {
    let buttons = '';

    // Start order button
    if (order.status === 'planned' && order.can_start) {
      buttons += `<button type="button" class="btn btn-success me-2 start-order-btn" data-id="${order.id}">${__('Start Order')}</button>`;
    }

    // Manage consumption button
    if (order.status === 'in_progress') {
      buttons += `<button type="button" class="btn btn-warning me-2 manage-consumption-btn" data-id="${order.id}">${__('Manage Materials')}</button>`;
    }

    // Complete order button
    if (order.can_complete) {
      buttons += `<button type="button" class="btn btn-primary me-2 complete-order-btn" data-id="${order.id}">${__('Complete Order')}</button>`;
    }

    // Cancel order button
    if (order.status !== 'completed' && order.status !== 'cancelled') {
      buttons += `<button type="button" class="btn btn-danger cancel-order-btn" data-id="${order.id}">${__('Cancel Order')}</button>`;
    }

    return buttons;
  }

  // Load consumption data for in-progress orders
  function loadConsumptionData(orderId) {
    // This would load the consumption/materials data for the order
    // Implementation can be added later if needed
    $('#order-consumption-content').html(`
      <div class="text-center text-muted py-4">
        <i class="ri-information-line ri-24px mb-2"></i>
        <p>${__('Material consumption details will be loaded here')}</p>
      </div>
    `);
  }

  // Load initial data
  await loadKanbanData();

})();
