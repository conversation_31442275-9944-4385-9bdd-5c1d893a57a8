<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserController extends Controller
{
  /**
   * Redirect to user-management view.
   *
   */
  public function UserManagement()
  {
    // Check permission
    if (!auth()->user()->can('view-users')) {
      abort(403, 'Unauthorized action.');
    }

    $users = User::with('roles')->get();
    $userCount = $users->count();
    $verified = User::whereNotNull('email_verified_at')->get()->count();
    $notVerified = User::whereNull('email_verified_at')->get()->count();
    $usersUnique = $users->unique(['email']);
    $userDuplicates = $users->diff($usersUnique)->count();

    return view('content.laravel-example.user-management', [
      'totalUser' => $userCount,
      'verified' => $verified,
      'notVerified' => $notVerified,
      'userDuplicates' => $userDuplicates,
    ]);
  }

  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function index(Request $request)
  {
    try {
      // Check permission
      if (!auth()->user()->can('view-users')) {
        return response()->json(['error' => 'Unauthorized'], 403);
      }

      $columns = [
        0 => 'id',
        1 => 'name',
        2 => 'name', // role column, but we'll order by name
        3 => 'email_verified_at', // status column
        4 => 'id', // actions column, order by id
      ];

      $search = [];

      $totalData = User::count();

      $totalFiltered = $totalData;

      $limit = $request->input('length', 10);
      $start = $request->input('start', 0);

      // Get order column safely
      $orderData = $request->input('order', []);
      $orderColumnIndex = isset($orderData[0]['column']) ? $orderData[0]['column'] : 1;
      $order = isset($columns[$orderColumnIndex]) ? $columns[$orderColumnIndex] : 'name';
      $dir = isset($orderData[0]['dir']) ? $orderData[0]['dir'] : 'asc';

    $searchData = $request->input('search', []);
    $searchValue = isset($searchData['value']) ? $searchData['value'] : '';

    // Get role filter from request
    $roleFilter = $request->input('role_filter', '');

    $query = User::with('roles');

    // Apply search filters
    if (!empty($searchValue)) {
      $query->where(function($q) use ($searchValue) {
        $q->where('id', 'LIKE', "%{$searchValue}%")
          ->orWhere('name', 'LIKE', "%{$searchValue}%")
          ->orWhere('email', 'LIKE', "%{$searchValue}%")
          ->orWhereHas('roles', function($roleQuery) use ($searchValue) {
            $roleQuery->where('name', 'LIKE', "%{$searchValue}%");
          });
      });
    }

    // Apply role filter
    if (!empty($roleFilter)) {
      $query->whereHas('roles', function($roleQuery) use ($roleFilter) {
        $roleQuery->where('name', $roleFilter);
      });
    }

    // Get total filtered count
    $totalFiltered = $query->count();

    // Get paginated results
    $users = $query->offset($start)
      ->limit($limit)
      ->orderBy($order, $dir)
      ->get();

    $data = [];

    if (!empty($users)) {
      // providing a dummy id instead of database ids
      $ids = $start;

      foreach ($users as $user) {
        $roles = $user->getRoleNames();
        $rolesBadges = $roles->map(function ($role) {
          $badgeClass = $this->getRoleBadgeClass($role);
          return '<span class="badge bg-label-' . $badgeClass . '">' . $role . '</span>';
        })->implode(' ');

        $nestedData['id'] = $user->id;
        $nestedData['fake_id'] = ++$ids;
        $nestedData['name'] = $user->name;
        $nestedData['email'] = $user->email;
        $nestedData['email_verified_at'] = $user->email_verified_at;
        $nestedData['role'] = $rolesBadges ?: '<span class="badge bg-label-secondary">No Role</span>';
        $nestedData['status'] = $user->email_verified_at ?
          '<span class="badge bg-label-success">Active</span>' :
          '<span class="badge bg-label-warning">Inactive</span>';

        // Actions with permission checks
        $actions = '<div class="dropdown">
          <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
            <i class="ri-more-2-line"></i>
          </button>
          <div class="dropdown-menu">';

        if (auth()->user()->can('edit-users')) {
          $actions .= '<a class="dropdown-item" href="javascript:void(0);" onclick="editUser(' . $user->id . ')">
            <i class="ri-pencil-line me-1"></i> Edit
          </a>';
        }

        if (auth()->user()->can('delete-users') && $user->id !== auth()->id()) {
          $actions .= '<a class="dropdown-item" href="javascript:void(0);" onclick="deleteUser(' . $user->id . ')">
            <i class="ri-delete-bin-7-line me-1"></i> Delete
          </a>';
        }

        $actions .= '</div></div>';
        $nestedData['actions'] = $actions;

        $data[] = $nestedData;
      }
    }

      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'code' => 200,
        'data' => $data,
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'message' => 'Internal Server Error: ' . $e->getMessage(),
        'code' => 500,
        'data' => [],
      ], 500);
    }
  }

  /**
   * Show the form for creating a new resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function create()
  {
    //
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\Response
   */
  public function store(Request $request)
  {
    $userID = $request->id;

    if ($userID) {
      // Check permission for updating
      if (!auth()->user()->can('edit-users')) {
        return response()->json([
          'success' => false,
          'message' => 'Unauthorized action.'
        ], 403);
      }

      $validator = Validator::make($request->all(), [
        'name' => 'required|string|max:255',
        'email' => 'required|string|email|max:255|unique:users,email,' . $userID,
        'role' => 'nullable|string|exists:roles,name',
      ]);

      if ($validator->fails()) {
        return response()->json([
          'success' => false,
          'message' => 'Validation failed',
          'errors' => $validator->errors()
        ], 422);
      }

      try {
        // update the value
        $user = User::updateOrCreate(
          ['id' => $userID],
          ['name' => $request->name, 'email' => $request->email]
        );

        // Update role if provided
        if ($request->filled('role')) {
          // Check if trying to assign Super Admin role
          if ($request->role === 'Super Admin' && !auth()->user()->can('manage-super-admin-roles')) {
            return response()->json([
              'success' => false,
              'message' => 'You do not have permission to assign Super Admin role'
            ], 403);
          }

          $user->syncRoles([$request->role]);
        }

        return response()->json([
          'success' => true,
          'message' => 'User updated successfully',
          'user' => $user->load('roles')
        ]);
      } catch (\Exception $e) {
        return response()->json([
          'success' => false,
          'message' => 'Failed to update user: ' . $e->getMessage()
        ], 500);
      }
    } else {
      // Check permission for creating
      if (!auth()->user()->can('create-users')) {
        return response()->json([
          'success' => false,
          'message' => 'Unauthorized action.'
        ], 403);
      }

      $validator = Validator::make($request->all(), [
        'name' => 'required|string|max:255',
        'email' => 'required|string|email|max:255|unique:users',
        'password' => 'required|string|min:8|confirmed',
        'role' => 'required|string|exists:roles,name',
      ]);

      if ($validator->fails()) {
        return response()->json([
          'success' => false,
          'message' => 'Validation failed',
          'errors' => $validator->errors()
        ], 422);
      }

      try {
        // Check if trying to assign Super Admin role
        if ($request->role === 'Super Admin' && !auth()->user()->can('manage-super-admin-roles')) {
          return response()->json([
            'success' => false,
            'message' => 'You do not have permission to create Super Admin accounts'
          ], 403);
        }

        // create new user
        $user = User::create([
          'name' => $request->name,
          'email' => $request->email,
          'password' => Hash::make($request->password),
          'email_verified_at' => now(),
        ]);

        // Assign role
        $user->assignRole($request->role);

        return response()->json([
          'success' => true,
          'message' => 'User created successfully',
          'user' => $user->load('roles')
        ]);
      } catch (\Exception $e) {
        return response()->json([
          'success' => false,
          'message' => 'Failed to create user: ' . $e->getMessage()
        ], 500);
      }
    }
  }

  /**
   * Display the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function show($id)
  {
    //
  }

  /**
   * Show the form for editing the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function edit($id): JsonResponse
  {
    if (!auth()->user()->can('edit-users')) {
      return response()->json([
        'success' => false,
        'message' => 'Unauthorized action.'
      ], 403);
    }

    try {
      $user = User::with('roles')->findOrFail($id);
      $roles = Role::all();
      $userRoles = $user->getRoleNames();

      return response()->json([
        'success' => true,
        'user' => $user,
        'roles' => $roles,
        'userRoles' => $userRoles
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'User not found'
      ], 404);
    }
  }

  /**
   * Update the specified resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function update(Request $request, $id)
  {
  }

  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroy($id)
  {
    if (!auth()->user()->can('delete-users')) {
      return response()->json([
        'success' => false,
        'message' => 'Unauthorized action.'
      ], 403);
    }

    try {
      $user = User::findOrFail($id);

      // Prevent deleting the current user
      if ($user->id === auth()->id()) {
        return response()->json([
          'success' => false,
          'message' => 'You cannot delete your own account'
        ], 403);
      }



      $user->delete();

      return response()->json([
        'success' => true,
        'message' => 'User deleted successfully'
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Failed to delete user: ' . $e->getMessage()
      ], 500);
    }
  }

  /**
   * Get all roles for dropdown
   */
  public function getRoles()
  {
    try {
      // Exclude Super Admin role unless user has permission to manage super admin roles
      $query = Role::select(['id', 'name']);

      if (!auth()->user()->can('manage-super-admin-roles')) {
        $query->where('name', '!=', 'Super Admin');
      }

      $roles = $query->get();

      return response()->json([
        'success' => true,
        'roles' => $roles
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Failed to fetch roles'
      ], 500);
    }
  }

  /**
   * Get role badge class for styling
   */
  private function getRoleBadgeClass($role)
  {
    $badgeClasses = [
      'Super Admin' => 'danger',
      'Admin' => 'primary',
      'Production Manager' => 'info',
      'Inventory Manager' => 'warning',
      'Sales Manager' => 'success',
      'Production Operator' => 'secondary',
      'Cashier' => 'dark',
      'Viewer' => 'light',
    ];

    return $badgeClasses[$role] ?? 'secondary';
  }

  /**
   * Role Management Methods
   */

  /**
   * Display roles management page
   */
  public function rolesIndex()
  {
    if (!auth()->user()->can('view-roles')) {
      abort(403, 'Unauthorized action.');
    }

    return view('content.apps.app-access-roles');
  }

  /**
   * Get roles cards data with user counts and avatars
   */
  public function getRoleCards()
  {
    if (!auth()->user()->can('view-roles')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }

    try {
      $roles = Role::withCount('users')
        ->with(['users' => function($query) {
          $query->select('id', 'name', 'email')->limit(4);
        }])
        ->where('name', '!=', 'Super Admin') // Exclude Super Admin role
        ->get();

      $roleCards = $roles->map(function ($role) {
        $users = $role->users->map(function ($user) {
          return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'initials' => $this->getUserInitials($user->name),
            'avatar' => null // You can add avatar logic here if needed
          ];
        });

        return [
          'id' => $role->id,
          'name' => $role->name,
          'users_count' => $role->users_count,
          'users' => $users,
          'remaining_count' => max(0, $role->users_count - 4)
        ];
      });

      return response()->json([
        'success' => true,
        'roles' => $roleCards
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Failed to fetch role cards data'
      ], 500);
    }
  }

  /**
   * Get user initials for avatar
   */
  private function getUserInitials($name)
  {
    $initials = collect(explode(' ', $name))
      ->map(function ($word) {
        return strtoupper(substr($word, 0, 1));
      })
      ->take(2)
      ->implode('');

    return $initials;
  }

  /**
   * Get roles data for DataTables
   */
  public function getRolesData(Request $request)
  {
    if (!auth()->user()->can('view-roles')) {
      return response()->json(['error' => 'Unauthorized'], 403);
    }

    if ($request->ajax()) {
      $roles = Role::withCount('users')->get();

      return response()->json([
        'data' => $roles->map(function ($role) {
          return [
            'id' => $role->id,
            'name' => $role->name,
            'users_count' => $role->users_count,
            'permissions' => $role->permissions->pluck('name')->implode(', '),
            'actions' => $this->getRoleActions($role)
          ];
        })
      ]);
    }
  }

  /**
   * Get role data for editing
   */
  public function editRole($id)
  {
    if (!auth()->user()->can('edit-roles')) {
      return response()->json([
        'success' => false,
        'message' => 'Unauthorized action.'
      ], 403);
    }

    try {
      $role = Role::with('permissions')->findOrFail($id);
      $allPermissions = Permission::all(['id', 'name']);
      $rolePermissions = $role->permissions->pluck('name')->toArray();

      return response()->json([
        'success' => true,
        'role' => $role,
        'permissions' => $allPermissions,
        'rolePermissions' => $rolePermissions
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Role not found'
      ], 404);
    }
  }

  /**
   * Store a new role
   */
  public function storeRole(Request $request)
  {
    if (!auth()->user()->can('create-roles')) {
      return response()->json([
        'success' => false,
        'message' => 'Unauthorized action.'
      ], 403);
    }

    $validator = Validator::make($request->all(), [
      'name' => 'required|string|max:255|unique:roles',
      'permissions' => 'array',
      'permissions.*' => 'exists:permissions,name',
    ]);

    if ($validator->fails()) {
      return response()->json([
        'success' => false,
        'message' => 'Validation failed',
        'errors' => $validator->errors()
      ], 422);
    }

    try {
      $role = Role::create(['name' => $request->name]);

      if ($request->has('permissions')) {
        $role->syncPermissions($request->permissions);
      }

      return response()->json([
        'success' => true,
        'message' => 'Role created successfully',
        'role' => $role->load('permissions')
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Failed to create role: ' . $e->getMessage()
      ], 500);
    }
  }

  /**
   * Update a role
   */
  public function updateRole(Request $request, $id)
  {
    if (!auth()->user()->can('edit-roles')) {
      return response()->json([
        'success' => false,
        'message' => 'Unauthorized action.'
      ], 403);
    }

    $validator = Validator::make($request->all(), [
      'name' => 'required|string|max:255|unique:roles,name,' . $id,
      'permissions' => 'array',
      'permissions.*' => 'exists:permissions,name',
    ]);

    if ($validator->fails()) {
      return response()->json([
        'success' => false,
        'message' => 'Validation failed',
        'errors' => $validator->errors()
      ], 422);
    }

    try {
      $role = Role::findOrFail($id);
      $role->update(['name' => $request->name]);

      if ($request->has('permissions')) {
        $role->syncPermissions($request->permissions);
      }

      return response()->json([
        'success' => true,
        'message' => 'Role updated successfully',
        'role' => $role->load('permissions')
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Failed to update role: ' . $e->getMessage()
      ], 500);
    }
  }

  /**
   * Delete a role
   */
  public function destroyRole($id)
  {
    if (!auth()->user()->can('delete-roles')) {
      return response()->json([
        'success' => false,
        'message' => 'Unauthorized action.'
      ], 403);
    }

    try {
      $role = Role::findOrFail($id);

      // Check if role is assigned to users
      if ($role->users()->count() > 0) {
        return response()->json([
          'success' => false,
          'message' => 'Cannot delete role that is assigned to users'
        ], 403);
      }

      $role->delete();

      return response()->json([
        'success' => true,
        'message' => 'Role deleted successfully'
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Failed to delete role: ' . $e->getMessage()
      ], 500);
    }
  }

  /**
   * Get permissions for role management
   */
  public function getPermissions()
  {
    try {
      $permissions = Permission::all(['id', 'name']);
      return response()->json([
        'success' => true,
        'permissions' => $permissions
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => 'Failed to fetch permissions'
      ], 500);
    }
  }

  /**
   * Get role actions for DataTable
   */
  private function getRoleActions($role)
  {
    $actions = '<div class="dropdown">
      <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
        <i class="ri-more-2-line"></i>
      </button>
      <div class="dropdown-menu">';

    if (auth()->user()->can('edit-roles')) {
      $actions .= '<a class="dropdown-item" href="javascript:void(0);" onclick="editRole(' . $role->id . ')">
        <i class="ri-pencil-line me-1"></i> Edit
      </a>';
    }

    if (auth()->user()->can('delete-roles') && $role->users_count == 0) {
      $actions .= '<a class="dropdown-item" href="javascript:void(0);" onclick="deleteRole(' . $role->id . ')">
        <i class="ri-delete-bin-7-line me-1"></i> Delete
      </a>';
    }

    $actions .= '</div></div>';
    return $actions;
  }
}
